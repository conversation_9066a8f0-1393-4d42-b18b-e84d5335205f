using FreeSql.DataAnnotations;
using SSIC.Entity.EntityBase;

namespace SSIC.Entity.Auth
{
    /// <summary>
    /// 角色实体
    /// </summary>
    [Table(Name = "auth_roles")]
    public class Role : EntityBasic
    {
        /// <summary>
        /// 角色名称
        /// </summary>
        [Column(StringLength = 50, IsNullable = false)]
        public string RoleName { get; set; } = string.Empty;

        /// <summary>
        /// 显示名称
        /// </summary>
        [Column(StringLength = 100)]
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// 角色描述
        /// </summary>
        [Column(StringLength = 500)]
        public string? Description { get; set; }

        /// <summary>
        /// 是否系统角色
        /// </summary>
        public bool IsSystemRole { get; set; } = false;

        /// <summary>
        /// 角色级别
        /// </summary>
        public int Level { get; set; } = 0;

        /// <summary>
        /// 用户角色关联
        /// </summary>
        [Navigate(ManyToMany = typeof(UserRole))]
        public virtual ICollection<User> Users { get; set; } = new List<User>();

        /// <summary>
        /// 角色权限关联
        /// </summary>
        //[Navigate(ManyToMany = typeof(RolePermission))]
        //public virtual ICollection<Permission> Permissions { get; set; } = new List<Permission>();
    }

    /// <summary>
    /// 用户角色关联表
    /// </summary>
    [Table(Name = "auth_user_roles")]
    public class UserRole
    {
        [Column(IsPrimary = true)]
        public Guid UserId { get; set; }

        [Column(IsPrimary = true)]
        public Guid RoleId { get; set; }

        /// <summary>
        /// 分配时间
        /// </summary>
        public DateTime AssignedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 分配人ID
        /// </summary>
        public Guid? AssignedBy { get; set; }

        /// <summary>
        /// 用户导航属性
        /// </summary>
        [Navigate(nameof(UserId))]
        public virtual User User { get; set; } = null!;

        /// <summary>
        /// 角色导航属性
        /// </summary>
        [Navigate(nameof(RoleId))]
        public virtual Role Role { get; set; } = null!;
    }
}
