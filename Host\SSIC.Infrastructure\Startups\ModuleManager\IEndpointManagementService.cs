using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SSIC.Infrastructure.Startups.ModuleManager
{
    /// <summary>
    /// 端点管理服务接口
    /// </summary>
    public interface IEndpointManagementService
    {
        /// <summary>
        /// 从模块信息创建端点
        /// </summary>
        /// <param name="moduleInfo">模块信息</param>
        /// <returns>端点列表</returns>
        IEnumerable<Endpoint> CreateEndpointsFromModule(ModuleInfo moduleInfo);

        /// <summary>
        /// 创建单个端点
        /// </summary>
        /// <param name="options">端点创建选项</param>
        /// <returns>端点</returns>
        Endpoint CreateEndpoint(EndpointCreationOptions options);

        /// <summary>
        /// 创建MVC控制器端点
        /// </summary>
        /// <param name="moduleInfo">模块信息</param>
        /// <param name="controllerInfo">控制器信息</param>
        /// <param name="actionInfo">动作信息</param>
        /// <returns>端点</returns>
        Endpoint CreateMvcEndpoint(ModuleInfo moduleInfo, ControllerInfo controllerInfo, ActionInfo actionInfo);

        /// <summary>
        /// 批量刷新端点
        /// </summary>
        /// <param name="modules">模块列表</param>
        /// <returns>所有端点</returns>
        Task<IEnumerable<Endpoint>> RefreshEndpointsAsync(IEnumerable<ModuleInfo> modules);

        /// <summary>
        /// 验证端点是否有效
        /// </summary>
        /// <param name="endpoint">端点</param>
        /// <returns>是否有效</returns>
        bool ValidateEndpoint(Endpoint endpoint);
    }

    /// <summary>
    /// 端点创建选项
    /// </summary>
    public class EndpointCreationOptions
    {
        public string RouteTemplate { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public List<string> HttpMethods { get; set; } = new();
        public int Order { get; set; } = 0;
        public Dictionary<string, object> Metadata { get; set; } = new();
        public RequestDelegate Handler { get; set; } = null!;
    }
}
