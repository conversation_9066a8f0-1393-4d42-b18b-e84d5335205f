using Microsoft.Extensions.Logging;
using SSIC.Infrastructure.OptionsEntity;
using System;
using System.Collections.Concurrent;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace SSIC.Infrastructure.Startups.HotReload
{
    /// <summary>
    /// 文件变化事件处理委托
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="changeType">变化类型</param>
    public delegate Task FileChangedEventHandler(string filePath, WatcherChangeTypes changeType);

    /// <summary>
    /// 简化的文件监控器接口
    /// </summary>
    public interface IFileWatcher : IDisposable
    {
        /// <summary>
        /// 文件变化事件
        /// </summary>
        event FileChangedEventHandler? FileChanged;

        /// <summary>
        /// 开始监控
        /// </summary>
        void StartWatching();

        /// <summary>
        /// 停止监控
        /// </summary>
        void StopWatching();
    }

    /// <summary>
    /// 简化的文件监控器实现 - 使用.NET自带的FileSystemWatcher
    /// </summary>
    public class FileWatcher : IFileWatcher
    {
        private readonly ModulePathOptions _moduleOptions;
        private readonly HotReloadOptions _hotReloadOptions;
        private readonly ILogger<FileWatcher> _logger;
        private readonly List<FileSystemWatcher> _watchers = new();
        private readonly ConcurrentDictionary<string, DateTime> _lastEventTimes = new();
        private readonly ConcurrentDictionary<string, WatcherChangeTypes> _lastEventTypes = new();
        private readonly Timer _debounceTimer;
        private bool _isDisposed;

        public event FileChangedEventHandler? FileChanged;

        public FileWatcher(
            ModulePathOptions moduleOptions,
            HotReloadOptions hotReloadOptions,
            ILogger<FileWatcher> logger)
        {
            _moduleOptions = moduleOptions ?? throw new ArgumentNullException(nameof(moduleOptions));
            _hotReloadOptions = hotReloadOptions ?? throw new ArgumentNullException(nameof(hotReloadOptions));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            // 初始化防抖定时器
            _debounceTimer = new Timer(ProcessPendingEvents, null, Timeout.Infinite, Timeout.Infinite);
        }

        /// <summary>
        /// 开始监控文件
        /// </summary>
        public void StartWatching()
        {
            if (!_hotReloadOptions.Enabled)
            {
                _logger.LogInformation("热加载功能已禁用，不启动文件监控");
                return;
            }

            // 获取模块路径
            var modulePath = _moduleOptions.GetModuleDirectory(true); // 假设是开发环境
            if (!Directory.Exists(modulePath))
            {
                _logger.LogWarning("模块目录不存在: {Path}", modulePath);
                return;
            }

            try
            {
                var watcher = new FileSystemWatcher(modulePath)
                {
                    NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.FileName,
                    IncludeSubdirectories = true,
                    Filter = "*.dll", // 只监控DLL文件
                    EnableRaisingEvents = true
                };

                // 注册事件处理程序
                watcher.Created += OnFileEvent;
                watcher.Changed += OnFileEvent;
                watcher.Deleted += OnFileEvent;
                watcher.Renamed += OnFileRenamed;

                _watchers.Add(watcher);
                _logger.LogInformation("开始监控模块目录: {Path}", modulePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建文件监控器失败: {Path}", modulePath);
            }
        }

        /// <summary>
        /// 停止监控文件
        /// </summary>
        public void StopWatching()
        {
            foreach (var watcher in _watchers)
            {
                watcher.EnableRaisingEvents = false;
                watcher.Created -= OnFileEvent;
                watcher.Changed -= OnFileEvent;
                watcher.Deleted -= OnFileEvent;
                watcher.Renamed -= OnFileRenamed;
                watcher.Dispose();
            }

            _watchers.Clear();
            _debounceTimer?.Change(Timeout.Infinite, Timeout.Infinite);
            _logger.LogInformation("文件监控已停止");
        }

        /// <summary>
        /// 处理文件事件（创建、修改、删除）
        /// </summary>
        private void OnFileEvent(object sender, FileSystemEventArgs e)
        {
            try
            {
                // 只处理模块DLL文件
                if (!IsModuleFile(e.FullPath))
                    return;

                var now = DateTime.UtcNow;
                var filePath = e.FullPath;

                _logger.LogInformation("检测到文件{ChangeType}: {Path}", e.ChangeType, filePath);

                // 如果防抖延迟为0，立即触发事件
                if (_hotReloadOptions.DebounceMs == 0)
                {
                    _logger.LogInformation("防抖延迟为0，立即触发FileChanged事件: {FilePath}", filePath);
                    Task.Run(async () =>
                    {
                        try
                        {
                            if (FileChanged != null)
                            {
                                await FileChanged.Invoke(filePath, e.ChangeType);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "立即处理文件变化事件失败: {FilePath}", filePath);
                        }
                    });
                }
                else
                {
                    // 防抖处理 - 避免重复事件
                    if (_lastEventTimes.TryGetValue(filePath, out var lastTime) &&
                        (now - lastTime).TotalMilliseconds < _hotReloadOptions.DebounceMs)
                    {
                        if (_hotReloadOptions.VerboseLogging)
                            _logger.LogDebug("跳过重复事件: {Path}", filePath);
                        return;
                    }

                    _lastEventTimes[filePath] = now;
                    _lastEventTypes[filePath] = e.ChangeType;

                    // 启动防抖定时器
                    _debounceTimer.Change(_hotReloadOptions.DebounceMs, Timeout.Infinite);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理文件事件失败: {Path}", e.FullPath);
            }
        }

        /// <summary>
        /// 处理文件重命名事件
        /// </summary>
        private void OnFileRenamed(object sender, RenamedEventArgs e)
        {
            try
            {
                // 处理旧文件删除
                if (IsModuleFile(e.OldFullPath))
                {
                    _logger.LogInformation("文件重命名 - 旧文件: {OldPath}", e.OldFullPath);

                    if (_hotReloadOptions.DebounceMs == 0)
                    {
                        // 立即触发删除事件
                        Task.Run(async () =>
                        {
                            try
                            {
                                if (FileChanged != null)
                                {
                                    await FileChanged.Invoke(e.OldFullPath, WatcherChangeTypes.Deleted);
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "立即处理文件删除事件失败: {FilePath}", e.OldFullPath);
                            }
                        });
                    }
                    else
                    {
                        _lastEventTimes.TryRemove(e.OldFullPath, out _);
                        _lastEventTypes.TryRemove(e.OldFullPath, out _);
                    }
                }

                // 处理新文件创建
                if (IsModuleFile(e.FullPath))
                {
                    _logger.LogInformation("文件重命名 - 新文件: {NewPath}", e.FullPath);

                    if (_hotReloadOptions.DebounceMs == 0)
                    {
                        // 立即触发创建事件
                        Task.Run(async () =>
                        {
                            try
                            {
                                if (FileChanged != null)
                                {
                                    await FileChanged.Invoke(e.FullPath, WatcherChangeTypes.Created);
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "立即处理文件创建事件失败: {FilePath}", e.FullPath);
                            }
                        });
                    }
                    else
                    {
                        _lastEventTimes[e.FullPath] = DateTime.UtcNow;
                        _lastEventTypes[e.FullPath] = WatcherChangeTypes.Created;
                        _debounceTimer.Change(_hotReloadOptions.DebounceMs, Timeout.Infinite);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理文件重命名事件失败: {OldPath} -> {NewPath}", e.OldFullPath, e.FullPath);
            }
        }

        /// <summary>
        /// 防抖定时器回调 - 处理待处理的事件
        /// </summary>
        private void ProcessPendingEvents(object? state)
        {
            try
            {
                var recentEvents = new List<string>();
                var cutoffTime = DateTime.UtcNow.AddMilliseconds(-_hotReloadOptions.DebounceMs);

                // 收集最近的事件
                foreach (var kvp in _lastEventTimes)
                {
                    if (kvp.Value >= cutoffTime)
                    {
                        recentEvents.Add(kvp.Key);
                    }
                }

                // 触发文件变化事件
                foreach (var filePath in recentEvents)
                {
                    // 使用记录的事件类型，如果没有记录则根据文件是否存在判断
                    var changeType = _lastEventTypes.TryGetValue(filePath, out var recordedType)
                        ? recordedType
                        : (File.Exists(filePath) ? WatcherChangeTypes.Changed : WatcherChangeTypes.Deleted);

                    Task.Run(async () =>
                    {
                        try
                        {
                            await (FileChanged?.Invoke(filePath, changeType) ?? Task.CompletedTask);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "处理文件变化事件失败: {FilePath}", filePath);
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理防抖事件失败");
            }
        }

        /// <summary>
        /// 检查是否为模块文件
        /// </summary>
        private bool IsModuleFile(string filePath)
        {
            if (string.IsNullOrEmpty(filePath) || !filePath.EndsWith(".dll", StringComparison.OrdinalIgnoreCase))
                return false;

            var fileName = Path.GetFileName(filePath);

            // 使用模块配置中的通配符模式进行匹配
            foreach (var pattern in _moduleOptions.WildcardPatterns)
            {
                if (IsWildcardMatch(fileName, pattern))
                    return true;
            }

            return false;
        }

        /// <summary>
        /// 简单的通配符匹配
        /// </summary>
        private static bool IsWildcardMatch(string input, string pattern)
        {
            if (string.IsNullOrEmpty(pattern))
                return false;

            // 简单的通配符匹配：支持 * 和 ?
            var regex = "^" + pattern.Replace("*", ".*").Replace("?", ".") + "$";
            return System.Text.RegularExpressions.Regex.IsMatch(input, regex, System.Text.RegularExpressions.RegexOptions.IgnoreCase);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        protected virtual void Dispose(bool disposing)
        {
            if (_isDisposed)
                return;

            if (disposing)
            {
                StopWatching();
                _debounceTimer?.Dispose();
                _lastEventTimes.Clear();
                _lastEventTypes.Clear();
            }

            _isDisposed = true;
        }
    }
} 