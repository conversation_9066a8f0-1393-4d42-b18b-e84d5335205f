using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace SSIC.Infrastructure.Startups.ModuleManager
{
    /// <summary>
    /// 模块发现服务接口
    /// </summary>
    public interface IModuleDiscoveryService
    {
        /// <summary>
        /// 发现所有已加载的模块
        /// </summary>
        /// <returns>模块信息列表</returns>
        IEnumerable<ModuleInfo> DiscoverLoadedModules();

        /// <summary>
        /// 从程序集中发现控制器
        /// </summary>
        /// <param name="assembly">程序集</param>
        /// <returns>控制器信息列表</returns>
        IEnumerable<ControllerInfo> DiscoverControllersInAssembly(Assembly assembly);

        /// <summary>
        /// 从控制器类型中发现动作方法
        /// </summary>
        /// <param name="controllerType">控制器类型</param>
        /// <returns>动作方法信息列表</returns>
        IEnumerable<ActionInfo> DiscoverActionsInController(Type controllerType);

        /// <summary>
        /// 构建控制器的路由模板
        /// </summary>
        /// <param name="moduleInfo">模块信息</param>
        /// <param name="controllerInfo">控制器信息</param>
        /// <param name="actionInfo">动作信息（可选）</param>
        /// <returns>路由模板</returns>
        string BuildRouteTemplate(ModuleInfo moduleInfo, ControllerInfo controllerInfo, ActionInfo actionInfo = null);
    }

    /// <summary>
    /// 模块信息
    /// </summary>
    public class ModuleInfo
    {
        public string Name { get; set; } = string.Empty;
        public string AssemblyName { get; set; } = string.Empty;
        public Assembly Assembly { get; set; } = null!;
        public DateTime LoadTime { get; set; }
        public List<ControllerInfo> Controllers { get; set; } = new();
    }

    /// <summary>
    /// 控制器信息
    /// </summary>
    public class ControllerInfo
    {
        public string Name { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public Type Type { get; set; } = null!;
        public string RouteTemplate { get; set; } = string.Empty;
        public List<ActionInfo> Actions { get; set; } = new();
    }

    /// <summary>
    /// 动作方法信息
    /// </summary>
    public class ActionInfo
    {
        public string Name { get; set; } = string.Empty;
        public MethodInfo Method { get; set; } = null!;
        public string RouteTemplate { get; set; } = string.Empty;
        public List<string> HttpMethods { get; set; } = new();
        public List<Attribute> Attributes { get; set; } = new();
    }
}
