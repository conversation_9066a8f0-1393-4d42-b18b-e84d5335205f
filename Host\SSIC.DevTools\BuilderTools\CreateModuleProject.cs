using Serilog;
using SSIC.Infrastructure.BaseProvider;
using SSIC.Utilities;
using SSIC.DevTools.Controllers;

namespace SSIC.DevTools.BuilderTools
{
    /// <summary>
    /// 模块化项目代码生成器
    /// 生成符合SSIC.Modules.{ModuleName}结构的代码
    /// </summary>
    public partial class CreateModuleProject
    {
        /// <summary>
        /// 生成模块化项目文件（支持实体信息）
        /// </summary>
        /// <param name="entities">要生成的实体信息数组，包含实体名称和中文描述</param>
        /// <param name="entityNames">要生成的实体名称数组，为空则生成所有实体对应的服务和控制器（向后兼容）</param>
        /// <param name="controllerNames">要生成的控制器名称数组，为空则生成所有实体对应的控制器（已弃用）</param>
        /// <returns></returns>
        public string CreateModuleFiles(EntityInfo[]? entities = null, string[]? entityNames = null, string[]? controllerNames = null)
        {
            var ServerHost = Directory.GetCurrentDirectory();
            //设置实体库地址
            var HostPath = ServerHost.Substring(0, ServerHost.LastIndexOf("\\"));
            DirectoryInfo theFolder = new DirectoryInfo($"{HostPath}\\SSIC.Entity\\");

            //设置模块路径
            var ModulesPath = $"{HostPath}\\SSIC.Modules\\";

            //读取模板路径
            var TemplatePath = $"{HostPath}\\SSIC.Infrastructure\\Template\\";

            // 为了向后兼容，如果传入了controllerNames但没有entityNames，则使用controllerNames作为entityNames
            var actualEntityNames = entities?.Select(e => e.Name).ToArray() ?? entityNames ?? controllerNames;
            var actualEntities = entities;

            // 如果指定了实体信息，则只生成一个默认模块
            if (actualEntityNames != null && actualEntityNames.Length > 0)
            {
                var defaultModuleName = "Default";
                var defaultEntityFolder = new DirectoryInfo($"{HostPath}\\SSIC.Entity\\{defaultModuleName}");
                CreateModuleStructure(ModulesPath, defaultModuleName, TemplatePath, defaultEntityFolder, actualEntities, actualEntityNames);
                CreateProjectFiles(ModulesPath, defaultModuleName);
                Log.Information($"生成模块 {defaultModuleName} 成功!");
            }
            else
            {
                // 如果没有指定实体名称，则尝试从实体文件夹获取模块
                if (theFolder.Exists)
                {
                    DirectoryInfo[] dirInfo = theFolder.GetDirectories();//获取所在目录的文件夹

                    foreach (DirectoryInfo NextFolder in dirInfo.Where(n =>
                        n.Name.ToLower() != "entitybase" &&
                        n.Name.ToLower() != "tenantentity" &&
                        n.Name.ToLower() != "bin" &&
                        n.Name.ToLower() != "obj").ToList())
                    {
                        var moduleName = NextFolder.Name; // 例如：Auth, Sys
                        CreateModuleStructure(ModulesPath, moduleName, TemplatePath, NextFolder, actualEntities, actualEntityNames);
                        CreateProjectFiles(ModulesPath, moduleName);
                        Log.Information($"生成模块 {moduleName} 成功!");
                    }
                }
                else
                {
                    // 如果实体文件夹不存在，生成一个默认模块
                    var defaultModuleName = "Default";
                    var defaultEntityFolder = new DirectoryInfo($"{HostPath}\\SSIC.Entity\\{defaultModuleName}");
                    CreateModuleStructure(ModulesPath, defaultModuleName, TemplatePath, defaultEntityFolder, actualEntities, actualEntityNames);
                    CreateProjectFiles(ModulesPath, defaultModuleName);
                    Log.Information($"生成默认模块 {defaultModuleName} 成功!");
                }
            }

            return "模块化项目生成完成!";
        }

        /// <summary>
        /// 生成模块化项目文件（原方法，向后兼容）
        /// </summary>
        /// <param name="entityNames">要生成的实体名称数组，为空则生成所有实体对应的服务和控制器</param>
        /// <param name="controllerNames">要生成的控制器名称数组，为空则生成所有实体对应的控制器（已弃用，使用entityNames参数）</param>
        /// <returns></returns>
        public string CreateModuleFiles(string[]? entityNames = null, string[]? controllerNames = null)
        {
            var ServerHost = Directory.GetCurrentDirectory();
            //设置实体库地址
            var HostPath = ServerHost.Substring(0, ServerHost.LastIndexOf("\\"));
            DirectoryInfo theFolder = new DirectoryInfo($"{HostPath}\\SSIC.Entity\\");

            //设置模块路径
            var ModulesPath = $"{HostPath}\\SSIC.Modules\\";

            //读取模板路径
            var TemplatePath = $"{HostPath}\\SSIC.Infrastructure\\Template\\";

            // 为了向后兼容，如果传入了controllerNames但没有entityNames，则使用controllerNames作为entityNames
            var actualEntityNames = entityNames ?? controllerNames;

            // 如果指定了实体名称，则只生成一个默认模块
            if (actualEntityNames != null && actualEntityNames.Length > 0)
            {
                var defaultModuleName = "Default";
                var defaultEntityFolder = new DirectoryInfo($"{HostPath}\\SSIC.Entity\\{defaultModuleName}");
                CreateModuleStructure(ModulesPath, defaultModuleName, TemplatePath, defaultEntityFolder, null, actualEntityNames);
                CreateProjectFiles(ModulesPath, defaultModuleName);
                Log.Information($"生成模块 {defaultModuleName} 成功!");
            }
            else
            {
                // 如果没有指定实体名称，则尝试从实体文件夹获取模块
                if (theFolder.Exists)
                {
                    DirectoryInfo[] dirInfo = theFolder.GetDirectories();//获取所在目录的文件夹

                    foreach (DirectoryInfo NextFolder in dirInfo.Where(n =>
                        n.Name.ToLower() != "entitybase" &&
                        n.Name.ToLower() != "tenantentity" &&
                        n.Name.ToLower() != "bin" &&
                        n.Name.ToLower() != "obj").ToList())
                    {
                        var moduleName = NextFolder.Name; // 例如：Auth, Sys
                        CreateModuleStructure(ModulesPath, moduleName, TemplatePath, NextFolder, null, actualEntityNames);
                        CreateProjectFiles(ModulesPath, moduleName);
                        Log.Information($"生成模块 {moduleName} 成功!");
                    }
                }
                else
                {
                    // 如果实体文件夹不存在，生成一个默认模块
                    var defaultModuleName = "Default";
                    var defaultEntityFolder = new DirectoryInfo($"{HostPath}\\SSIC.Entity\\{defaultModuleName}");
                    CreateModuleStructure(ModulesPath, defaultModuleName, TemplatePath, defaultEntityFolder, null, actualEntityNames);
                    CreateProjectFiles(ModulesPath, defaultModuleName);
                    Log.Information($"生成默认模块 {defaultModuleName} 成功!");
                }
            }

            return "模块化项目生成完成!";
        }

        /// <summary>
        /// 生成指定模块的项目文件（支持实体信息）
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        /// <param name="entities">要生成的实体信息数组，包含实体名称和中文描述</param>
        /// <param name="entityNames">要生成的实体名称数组，为空则生成所有实体对应的服务和控制器（向后兼容）</param>
        /// <param name="controllerNames">要生成的控制器名称数组（已弃用）</param>
        /// <returns></returns>
        public string CreateModuleFiles(string moduleName, EntityInfo[]? entities = null, string[]? entityNames = null, string[]? controllerNames = null)
        {
            var ServerHost = Directory.GetCurrentDirectory();
            //设置实体库地址
            var HostPath = ServerHost.Substring(0, ServerHost.LastIndexOf("\\"));
            DirectoryInfo theFolder = new DirectoryInfo($"{HostPath}\\SSIC.Entity\\{moduleName}");

            //设置模块路径
            var ModulesPath = $"{HostPath}\\SSIC.Modules\\";

            //读取模板路径
            var TemplatePath = $"{HostPath}\\SSIC.Infrastructure\\Template\\";

            // 为了向后兼容，如果传入了controllerNames但没有entityNames，则使用controllerNames作为entityNames
            var actualEntityNames = entities?.Select(e => e.Name).ToArray() ?? entityNames ?? controllerNames;
            var actualEntities = entities;
            CreateModuleStructure(ModulesPath, moduleName, TemplatePath, theFolder, actualEntities, actualEntityNames);
            CreateProjectFiles(ModulesPath, moduleName);
            Log.Information($"生成模块 {moduleName} 成功!");

            return $"模块 {moduleName} 生成完成!";
        }

        /// <summary>
        /// 生成指定模块的项目文件（原方法，向后兼容）
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        /// <param name="entityNames">要生成的实体名称数组，为空则生成所有实体对应的服务和控制器</param>
        /// <param name="controllerNames">要生成的控制器名称数组（已弃用，使用entityNames参数）</param>
        /// <returns></returns>
        public string CreateModuleFiles(string moduleName, string[]? entityNames = null, string[]? controllerNames = null)
        {
            var ServerHost = Directory.GetCurrentDirectory();
            //设置实体库地址
            var HostPath = ServerHost.Substring(0, ServerHost.LastIndexOf("\\"));
            DirectoryInfo theFolder = new DirectoryInfo($"{HostPath}\\SSIC.Entity\\{moduleName}");

            //设置模块路径
            var ModulesPath = $"{HostPath}\\SSIC.Modules\\";

            //读取模板路径
            var TemplatePath = $"{HostPath}\\SSIC.Infrastructure\\Template\\";

            // 为了向后兼容，如果传入了controllerNames但没有entityNames，则使用controllerNames作为entityNames
            var actualEntityNames = entityNames ?? controllerNames;
            CreateModuleStructure(ModulesPath, moduleName, TemplatePath, theFolder, null, actualEntityNames);
            CreateProjectFiles(ModulesPath, moduleName);
            Log.Information($"生成模块 {moduleName} 成功!");

            return $"模块 {moduleName} 生成完成!";
        }

        /// <summary>
        /// 创建模块结构
        /// </summary>
        /// <param name="modulesPath">模块根路径</param>
        /// <param name="moduleName">模块名称</param>
        /// <param name="templatePath">模板路径</param>
        /// <param name="entityFolder">实体文件夹</param>
        /// <param name="entities">要生成的实体信息数组，包含实体名称和中文描述</param>
        /// <param name="entityNames">要生成的实体名称数组，为空则生成所有实体对应的服务和控制器</param>
        private void CreateModuleStructure(string modulesPath, string moduleName, string templatePath, DirectoryInfo entityFolder, EntityInfo[]? entities = null, string[]? entityNames = null)
        {
            // 创建模块目录结构
            var moduleBasePath = $"{modulesPath}SSIC.Modules.{moduleName}\\";
            var servicesPath = $"{moduleBasePath}SSIC.Modules.{moduleName}.Services\\";
            var controllersPath = $"{moduleBasePath}SSIC.Modules.{moduleName}.Controller\\";

            // 确保目录存在
            Directory.CreateDirectory(servicesPath);
            Directory.CreateDirectory(controllersPath);

            var CreateFileList = new List<ModulePathDic>();

            // 如果指定了实体信息，优先使用实体信息生成代码
            if (entities != null && entities.Length > 0)
            {
                foreach (var entity in entities)
                {
                    if (!string.IsNullOrWhiteSpace(entity.Name))
                    {
                        // 生成服务接口文件
                        CreateFileList.AddRange(CreateServiceInterfaceFiles(servicesPath, templatePath, moduleName, entity.Name, entity.Description));

                        // 生成服务实现文件
                        CreateFileList.AddRange(CreateServiceImplementationFiles(servicesPath, templatePath, moduleName, entity.Name, entity.Description));

                        // 生成控制器文件
                        CreateFileList.AddRange(CreateControllerFiles(controllersPath, templatePath, moduleName, entity.Name, entity.Description));

                        Log.Debug($"已添加实体 {entity.Name}({entity.Description}) 的服务和控制器文件生成任务");
                    }
                }
            }
            // 如果指定了实体名称，直接使用指定的实体名称生成代码
            else if (entityNames != null && entityNames.Length > 0)
            {
                foreach (var entityName in entityNames)
                {
                    if (!string.IsNullOrWhiteSpace(entityName))
                    {
                        // 生成服务接口文件
                        CreateFileList.AddRange(CreateServiceInterfaceFiles(servicesPath, templatePath, moduleName, entityName));

                        // 生成服务实现文件
                        CreateFileList.AddRange(CreateServiceImplementationFiles(servicesPath, templatePath, moduleName, entityName));

                        // 生成控制器文件
                        CreateFileList.AddRange(CreateControllerFiles(controllersPath, templatePath, moduleName, entityName));

                        Log.Debug($"已添加实体 {entityName} 的服务和控制器文件生成任务");
                    }
                }
            }
            else
            {
                // 如果没有指定实体名称，则从实体文件夹中获取所有实体文件
                if (entityFolder.Exists)
                {
                    FileInfo[] filelist = entityFolder.GetFiles("*.cs");

                    foreach (var file in filelist)
                    {
                        var entityName = file.Name.Replace(".cs", "");

                        // 生成服务接口文件
                        CreateFileList.AddRange(CreateServiceInterfaceFiles(servicesPath, templatePath, moduleName, entityName));

                        // 生成服务实现文件
                        CreateFileList.AddRange(CreateServiceImplementationFiles(servicesPath, templatePath, moduleName, entityName));

                        // 生成控制器文件
                        CreateFileList.AddRange(CreateControllerFiles(controllersPath, templatePath, moduleName, entityName));

                        Log.Debug($"已添加实体 {entityName} 的服务和控制器文件生成任务");
                    }
                }
                else
                {
                    Log.Warning($"实体文件夹不存在且未指定实体名称: {entityFolder.FullName}");
                }
            }

            // 生成所有文件
            foreach (var item in CreateFileList)
            {
                LoadModuleFile(item.Path, item.TemplatePath, item.ModuleName, item.EntityName, item.EntityDescription, item.Filename, item.IsReplace);
            }
        }

        /// <summary>
        /// 创建服务接口文件配置（支持中文描述）
        /// </summary>
        private List<ModulePathDic> CreateServiceInterfaceFiles(string servicesPath, string templatePath, string moduleName, string entityName, string entityDescription = "")
        {
            var files = new List<ModulePathDic>();

            // 基础接口文件
            files.Add(new ModulePathDic
            {
                Path = $"{servicesPath}Interfaces\\",
                Filename = $"I{entityName}Service.cs",
                TemplatePath = $"{templatePath}IServices\\BaseIService.html",
                ModuleName = moduleName,
                EntityName = entityName,
                EntityDescription = entityDescription,
                IsReplace = true
            });

            // 扩展接口文件
            files.Add(new ModulePathDic
            {
                Path = $"{servicesPath}Interfaces\\Expands\\",
                Filename = $"I{entityName}Service.cs",
                TemplatePath = $"{templatePath}IServices\\BaseIServiceExpands.html",
                ModuleName = moduleName,
                EntityName = entityName,
                EntityDescription = entityDescription,
                IsReplace = false
            });

            return files;
        }

        /// <summary>
        /// 创建服务接口文件配置（原方法，向后兼容）
        /// </summary>
        private List<ModulePathDic> CreateServiceInterfaceFiles(string servicesPath, string templatePath, string moduleName, string entityName)
        {
            var files = new List<ModulePathDic>();
            
            // 基础接口文件
            files.Add(new ModulePathDic
            {
                Path = $"{servicesPath}Interfaces\\",
                Filename = $"I{entityName}Service.cs",
                TemplatePath = $"{templatePath}IServices\\BaseIService.html",
                ModuleName = moduleName,
                EntityName = entityName,
                IsReplace = true
            });

            // 扩展接口文件
            files.Add(new ModulePathDic
            {
                Path = $"{servicesPath}Interfaces\\Expands\\",
                Filename = $"I{entityName}Service.cs",
                TemplatePath = $"{templatePath}IServices\\BaseIServiceExpands.html",
                ModuleName = moduleName,
                EntityName = entityName,
                IsReplace = false
            });

            return files;
        }

        /// <summary>
        /// 创建服务实现文件配置（支持中文描述）
        /// </summary>
        private List<ModulePathDic> CreateServiceImplementationFiles(string servicesPath, string templatePath, string moduleName, string entityName, string entityDescription = "")
        {
            var files = new List<ModulePathDic>();

            // 基础实现文件
            files.Add(new ModulePathDic
            {
                Path = $"{servicesPath}Implementations\\",
                Filename = $"{entityName}Service.cs",
                TemplatePath = $"{templatePath}Services\\BaseService.html",
                ModuleName = moduleName,
                EntityName = entityName,
                EntityDescription = entityDescription,
                IsReplace = true
            });

            // 扩展实现文件
            files.Add(new ModulePathDic
            {
                Path = $"{servicesPath}Implementations\\Expands\\",
                Filename = $"{entityName}Service.cs",
                TemplatePath = $"{templatePath}Services\\BaseServiceExpands.html",
                ModuleName = moduleName,
                EntityName = entityName,
                EntityDescription = entityDescription,
                IsReplace = false
            });

            return files;
        }

        /// <summary>
        /// 创建服务实现文件配置（原方法，向后兼容）
        /// </summary>
        private List<ModulePathDic> CreateServiceImplementationFiles(string servicesPath, string templatePath, string moduleName, string entityName)
        {
            var files = new List<ModulePathDic>();
            
            // 基础实现文件
            files.Add(new ModulePathDic
            {
                Path = $"{servicesPath}Implementations\\",
                Filename = $"{entityName}Service.cs",
                TemplatePath = $"{templatePath}Services\\BaseService.html",
                ModuleName = moduleName,
                EntityName = entityName,
                IsReplace = true
            });

            // 扩展实现文件
            files.Add(new ModulePathDic
            {
                Path = $"{servicesPath}Implementations\\Expands\\",
                Filename = $"{entityName}Service.cs",
                TemplatePath = $"{templatePath}Services\\BaseServiceExpands.html",
                ModuleName = moduleName,
                EntityName = entityName,
                IsReplace = false
            });

            return files;
        }

        /// <summary>
        /// 创建控制器文件配置（支持中文描述）
        /// </summary>
        private List<ModulePathDic> CreateControllerFiles(string controllersPath, string templatePath, string moduleName, string entityName, string entityDescription = "")
        {
            var files = new List<ModulePathDic>();

            // 基础控制器文件
            files.Add(new ModulePathDic
            {
                Path = $"{controllersPath}Controllers\\",
                Filename = $"{entityName}Controller.cs",
                TemplatePath = $"{templatePath}Controllers\\BaseController.html",
                ModuleName = moduleName,
                EntityName = entityName,
                EntityDescription = entityDescription,
                IsReplace = true
            });

            // 扩展控制器文件
            files.Add(new ModulePathDic
            {
                Path = $"{controllersPath}Controllers\\Expands\\",
                Filename = $"{entityName}Controller.cs",
                TemplatePath = $"{templatePath}Controllers\\BaseControllerExpands.html",
                ModuleName = moduleName,
                EntityName = entityName,
                EntityDescription = entityDescription,
                IsReplace = false
            });

            return files;
        }

        /// <summary>
        /// 创建控制器文件配置（原方法，向后兼容）
        /// </summary>
        private List<ModulePathDic> CreateControllerFiles(string controllersPath, string templatePath, string moduleName, string entityName)
        {
            var files = new List<ModulePathDic>();
            
            // 基础控制器文件
            files.Add(new ModulePathDic
            {
                Path = $"{controllersPath}Controllers\\",
                Filename = $"{entityName}Controller.cs",
                TemplatePath = $"{templatePath}Controllers\\BaseController.html",
                ModuleName = moduleName,
                EntityName = entityName,
                IsReplace = true
            });

            // 扩展控制器文件
            files.Add(new ModulePathDic
            {
                Path = $"{controllersPath}Controllers\\Expands\\",
                Filename = $"{entityName}Controller.cs",
                TemplatePath = $"{templatePath}Controllers\\BaseControllerExpands.html",
                ModuleName = moduleName,
                EntityName = entityName,
                IsReplace = false
            });

            return files;
        }

        /// <summary>
        /// 创建项目文件(.csproj, Program.cs等)
        /// </summary>
        /// <param name="modulesPath">模块根路径</param>
        /// <param name="moduleName">模块名称</param>
        private void CreateProjectFiles(string modulesPath, string moduleName)
        {
            var moduleBasePath = $"{modulesPath}SSIC.Modules.{moduleName}\\";
            var servicesPath = $"{moduleBasePath}SSIC.Modules.{moduleName}.Services\\";
            var controllersPath = $"{moduleBasePath}SSIC.Modules.{moduleName}.Controller\\";

            // 创建Services项目文件
            CreateServicesProjectFile(servicesPath, moduleName);

            // 创建Controller项目文件
            CreateControllerProjectFile(controllersPath, moduleName);

            // 创建解决方案文件
            CreateSolutionFile(moduleBasePath, moduleName);
        }

        /// <summary>
        /// 创建Services项目文件
        /// </summary>
        private void CreateServicesProjectFile(string servicesPath, string moduleName)
        {
            var projectContent = $@"<Project Sdk=""Microsoft.NET.Sdk"">

  <PropertyGroup>
    <TargetFramework>net10.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include=""..\..\..\SSIC.Entity\SSIC.Entity.csproj"" />
    <ProjectReference Include=""..\..\..\SSIC.Infrastructure\SSIC.Infrastructure.csproj"" />
  </ItemGroup>

</Project>";

            FileHelper.WriteFile(servicesPath, $"SSIC.Modules.{moduleName}.Services.csproj", projectContent);
        }

        /// <summary>
        /// 创建Controller项目文件
        /// </summary>
        private void CreateControllerProjectFile(string controllersPath, string moduleName)
        {
            var projectContent = $@"<Project Sdk=""Microsoft.NET.Sdk.Web"">

  <PropertyGroup>
    <TargetFramework>net10.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <OutputPath>..\..\..\..\SSIC.HostServer\Modules\SSIC.Modules.{moduleName}.Controller\</OutputPath>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include=""..\..\..\SSIC.Entity\SSIC.Entity.csproj"" />
    <ProjectReference Include=""..\..\..\SSIC.Infrastructure\SSIC.Infrastructure.csproj"" />
    <ProjectReference Include=""..\SSIC.Modules.{moduleName}.Services\SSIC.Modules.{moduleName}.Services.csproj"" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include=""Controllers\Expands\"" />
  </ItemGroup>

  <ItemGroup>
    <Content Include=""..\..\..\SSIC.Infrastructure\appsettings.json"">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include=""..\..\..\SSIC.Infrastructure\Corssettings.json"">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include=""..\..\..\SSIC.Infrastructure\DbInfosettings.json"">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include=""..\..\..\SSIC.Infrastructure\modulesettings.json"">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include=""Aspire.Seq"" Version=""9.2.0"" />
  </ItemGroup>

</Project>";

            FileHelper.WriteFile(controllersPath, $"SSIC.Modules.{moduleName}.Controller.csproj", projectContent);

            // 创建Program.cs
            var programContent = @"using SSIC.Infrastructure.Startups;
var builder = WebApplication.CreateBuilder(args);
var app = builder.AddStartup();
app.Run();";

            FileHelper.WriteFile(controllersPath, "Program.cs", programContent);

            // 创建Properties文件夹和launchSettings.json
            CreateLaunchSettings(controllersPath, moduleName);
        }

        /// <summary>
        /// 创建解决方案文件
        /// </summary>
        private void CreateSolutionFile(string moduleBasePath, string moduleName)
        {
            var solutionContent = $@"Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project(""{{9A19103F-16F7-4668-BE54-9A1E7A4F7556}}"") = ""SSIC.Modules.{moduleName}.Services"", ""SSIC.Modules.{moduleName}.Services\SSIC.Modules.{moduleName}.Services.csproj"", ""{{11111111-1111-1111-1111-111111111111}}""
EndProject
Project(""{{9A19103F-16F7-4668-BE54-9A1E7A4F7556}}"") = ""SSIC.Modules.{moduleName}.Controller"", ""SSIC.Modules.{moduleName}.Controller\SSIC.Modules.{moduleName}.Controller.csproj"", ""{{22222222-2222-2222-2222-222222222222}}""
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{{11111111-1111-1111-1111-111111111111}}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{{11111111-1111-1111-1111-111111111111}}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{{11111111-1111-1111-1111-111111111111}}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{{11111111-1111-1111-1111-111111111111}}.Release|Any CPU.Build.0 = Release|Any CPU
		{{22222222-2222-2222-2222-222222222222}}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{{22222222-2222-2222-2222-222222222222}}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{{22222222-2222-2222-2222-222222222222}}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{{22222222-2222-2222-2222-222222222222}}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal";

            FileHelper.WriteFile(moduleBasePath, $"SSIC.Modules.{moduleName}.sln", solutionContent);
        }

        /// <summary>
        /// 创建启动配置文件
        /// </summary>
        /// <param name="controllersPath">控制器项目路径</param>
        /// <param name="moduleName">模块名称</param>
        private void CreateLaunchSettings(string controllersPath, string moduleName)
        {
            var propertiesPath = $"{controllersPath}Properties\\";
            Directory.CreateDirectory(propertiesPath);

            var launchSettingsContent = $@"{{
  ""profiles"": {{
    ""http"": {{
      ""commandName"": ""Project"",
      ""launchBrowser"": true,
      ""launchUrl"": ""scalar/v1"",
      ""environmentVariables"": {{
        ""ASPNETCORE_ENVIRONMENT"": ""Development""
      }},
      ""dotnetRunMessages"": true,
      ""applicationUrl"": ""http://localhost:5000""
    }},
    ""https"": {{
      ""commandName"": ""Project"",
      ""launchBrowser"": true,
      ""launchUrl"": ""scalar/v1"",
      ""environmentVariables"": {{
        ""ASPNETCORE_ENVIRONMENT"": ""Development""
      }},
      ""dotnetRunMessages"": true,
      ""applicationUrl"": ""https://localhost:7000;http://localhost:5000""
    }},
    ""IIS Express"": {{
      ""commandName"": ""IISExpress"",
      ""launchBrowser"": true,
      ""launchUrl"": ""scalar/v1"",
      ""environmentVariables"": {{
        ""ASPNETCORE_ENVIRONMENT"": ""Development""
      }}
    }}
  }},
  ""$schema"": ""http://json.schemastore.org/launchsettings.json"",
  ""iisSettings"": {{
    ""windowsAuthentication"": false,
    ""anonymousAuthentication"": true,
    ""iisExpress"": {{
      ""applicationUrl"": ""http://localhost:44000"",
      ""sslPort"": 44300
    }}
  }}
}}";

            FileHelper.WriteFile(propertiesPath, "launchSettings.json", launchSettingsContent);
        }

        /// <summary>
        /// 读取Html模板文件并生成目标文件
        /// </summary>
        /// <param name="path">文件地址</param>
        /// <param name="templatePath">模板路径</param>
        /// <param name="moduleName">模块名称</param>
        /// <param name="entityName">实体名称</param>
        /// <param name="entityDescription">实体中文描述</param>
        /// <param name="filename">文件名</param>
        /// <param name="isReplace">是否替换</param>
        private void LoadModuleFile(string path, string templatePath, string moduleName, string entityName, string entityDescription, string filename, bool isReplace = false)
        {
            try
            {
                // 处理中文描述，如果有描述则添加括号
                var descriptionText = !string.IsNullOrEmpty(entityDescription) ? $"（{entityDescription}）" : "";

                if (isReplace)
                {
                    var domainContent = FileHelper.ReadFile(templatePath)
                        .Replace("{BusinessName}", moduleName)
                        .Replace("{EntityName}", entityName)
                        .Replace("{EntityDescription}", descriptionText);
                    FileHelper.WriteFile(path, filename, domainContent);
                    Log.Debug($"生成文件: {path}{filename}");
                }
                else
                {
                    if (!File.Exists(path + filename))
                    {
                        var domainContent = FileHelper.ReadFile(templatePath)
                            .Replace("{BusinessName}", moduleName)
                            .Replace("{EntityName}", entityName)
                            .Replace("{EntityDescription}", descriptionText);
                        FileHelper.WriteFile(path, filename, domainContent);
                        Log.Debug($"生成扩展文件: {path}{filename}");
                    }
                    else
                    {
                        Log.Debug($"扩展文件已存在，跳过: {path}{filename}");
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, $"生成文件失败: {path}{filename}");
            }
        }
    }

    /// <summary>
    /// 模块文件路径配置
    /// </summary>
    public class ModulePathDic
    {
        public string Path { get; set; } = string.Empty;
        public string TemplatePath { get; set; } = string.Empty;
        public string ModuleName { get; set; } = string.Empty;
        public string Filename { get; set; } = string.Empty;
        public bool IsReplace { get; set; }
        public string EntityName { get; set; } = string.Empty;
        public string EntityDescription { get; set; } = string.Empty;
    }
}
