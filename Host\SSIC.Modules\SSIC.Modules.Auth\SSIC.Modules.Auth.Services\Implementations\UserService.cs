/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *Service提供业务逻辑操作，如果要增加业务逻辑请在当前目录下Expands文件夹UserService编写代码
*/
using SSIC.Modules.Auth.Services.Interfaces;
using SSIC.Infrastructure.BaseProvider;
using SSIC.Entity.Auth;
using FreeSql;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SSIC.Modules.Auth.Services.Implementations
{
    /// <summary>
    /// User服务实现类（用户管理）
    /// 实现User实体的业务逻辑操作
    /// </summary>
    public partial class UserService : ServiceBase, IUserService
    {
        /// <summary>
        /// 初始化User服务
        /// </summary>
        /// <param name="fsql">FreeSql实例</param>
        /// <param name="logger">日志记录器</param>
        public UserService(IFreeSql fsql, ILogger<UserService> logger) : base(fsql, logger)
        {
        }

        // 在此处添加User特定的业务方法实现
    }
}
