using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using SSIC.Infrastructure.Startups.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace SSIC.Infrastructure.Startups.ModuleManager
{
    /// <summary>
    /// 模块发现服务实现
    /// </summary>
    public class ModuleDiscoveryService : IModuleDiscoveryService
    {
        private readonly ILogger<ModuleDiscoveryService> _logger;

        public ModuleDiscoveryService(ILogger<ModuleDiscoveryService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 发现所有已加载的模块
        /// </summary>
        public IEnumerable<ModuleInfo> DiscoverLoadedModules()
        {
            var modules = new List<ModuleInfo>();

            try
            {
                var moduleAssemblies = ModuleUtilities.GetModuleAssemblies();
                _logger.LogInformation("发现 {Count} 个模块程序集", moduleAssemblies.Count);

                foreach (var assembly in moduleAssemblies)
                {
                    var assemblyName = assembly.GetName().Name!;
                    var moduleName = ModuleUtilities.ExtractModuleName(assemblyName) ?? "Unknown";

                    var moduleInfo = new ModuleInfo
                    {
                        Name = moduleName,
                        AssemblyName = assemblyName,
                        Assembly = assembly,
                        LoadTime = GetAssemblyLoadTime(assembly)
                    };

                    // 发现控制器
                    moduleInfo.Controllers = DiscoverControllersInAssembly(assembly).ToList();

                    _logger.LogDebug("模块 {ModuleName} 包含 {ControllerCount} 个控制器",
                        moduleName, moduleInfo.Controllers.Count);

                    modules.Add(moduleInfo);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发现模块时发生错误");
            }

            return modules;
        }

        /// <summary>
        /// 从程序集中发现控制器
        /// </summary>
        public IEnumerable<ControllerInfo> DiscoverControllersInAssembly(Assembly assembly)
        {
            var controllers = new List<ControllerInfo>();

            try
            {
                var controllerTypes = assembly.GetExportedTypes()
                    .Where(ModuleUtilities.IsControllerType)
                    .ToList();

                foreach (var controllerType in controllerTypes)
                {
                    var controllerInfo = new ControllerInfo
                    {
                        Name = ModuleUtilities.ExtractControllerName(controllerType.Name),
                        FullName = controllerType.FullName!,
                        Type = controllerType,
                        RouteTemplate = GetControllerRouteTemplate(controllerType)
                    };

                    // 发现动作方法
                    controllerInfo.Actions = DiscoverActionsInController(controllerType).ToList();

                    _logger.LogDebug("控制器 {ControllerName} 包含 {ActionCount} 个动作方法",
                        controllerInfo.Name, controllerInfo.Actions.Count);

                    controllers.Add(controllerInfo);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从程序集 {AssemblyName} 发现控制器时发生错误", assembly.GetName().Name);
            }

            return controllers;
        }

        /// <summary>
        /// 从控制器类型中发现动作方法
        /// </summary>
        public IEnumerable<ActionInfo> DiscoverActionsInController(Type controllerType)
        {
            var actions = new List<ActionInfo>();

            try
            {
                var actionMethods = controllerType.GetMethods(BindingFlags.Public | BindingFlags.Instance | BindingFlags.DeclaredOnly)
                    .Where(ModuleUtilities.IsActionMethod)
                    .ToList();

                foreach (var method in actionMethods)
                {
                    var actionInfo = new ActionInfo
                    {
                        Name = method.Name,
                        Method = method,
                        RouteTemplate = GetActionRouteTemplate(method),
                        HttpMethods = ModuleUtilities.GetHttpMethods(method),
                        Attributes = method.GetCustomAttributes().ToList()
                    };

                    actions.Add(actionInfo);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从控制器 {ControllerName} 发现动作方法时发生错误", controllerType.Name);
            }

            return actions;
        }

        /// <summary>
        /// 构建控制器的路由模板
        /// </summary>
        public string BuildRouteTemplate(ModuleInfo moduleInfo, ControllerInfo controllerInfo, ActionInfo actionInfo = null)
        {
            try
            {
                var moduleName = moduleInfo.Name.ToLowerInvariant();
                var controllerName = controllerInfo.Name.ToLowerInvariant();

                // 如果控制器有自定义路由模板
                if (!string.IsNullOrEmpty(controllerInfo.RouteTemplate))
                {
                    if (actionInfo != null && !string.IsNullOrEmpty(actionInfo.RouteTemplate))
                    {
                        return CombineRouteTemplates(controllerInfo.RouteTemplate, actionInfo.RouteTemplate);
                    }
                    return controllerInfo.RouteTemplate;
                }

                // 使用默认的模块前缀路由
                var baseRoute = $"api/{moduleName}/{controllerName}";

                if (actionInfo != null && !string.IsNullOrEmpty(actionInfo.RouteTemplate))
                {
                    return CombineRouteTemplates(baseRoute, actionInfo.RouteTemplate);
                }

                return baseRoute;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "构建路由模板时发生错误");
                return $"api/{moduleInfo.Name.ToLowerInvariant()}/{controllerInfo.Name.ToLowerInvariant()}";
            }
        }

        #region 私有辅助方法

        private string GetControllerRouteTemplate(Type controllerType)
        {
            var routeAttribute = controllerType.GetCustomAttribute<RouteAttribute>();
            return routeAttribute?.Template ?? string.Empty;
        }

        private string GetActionRouteTemplate(MethodInfo method)
        {
            // 检查HTTP方法特性中的模板
            var httpMethodAttributes = method.GetCustomAttributes()
                .Where(a => a.GetType().Name.StartsWith("Http") && 
                          a.GetType().Name.EndsWith("Attribute"))
                .ToList();

            foreach (var attr in httpMethodAttributes)
            {
                var templateProperty = attr.GetType().GetProperty("Template");
                if (templateProperty != null)
                {
                    var template = templateProperty.GetValue(attr) as string;
                    if (!string.IsNullOrEmpty(template))
                    {
                        return template;
                    }
                }
            }

            return string.Empty;
        }



        private string CombineRouteTemplates(string baseTemplate, string actionTemplate)
        {
            if (string.IsNullOrEmpty(baseTemplate))
                return actionTemplate;
            
            if (string.IsNullOrEmpty(actionTemplate))
                return baseTemplate;

            return $"{baseTemplate.TrimEnd('/')}/{actionTemplate.TrimStart('/')}";
        }

        private DateTime GetAssemblyLoadTime(Assembly assembly)
        {
            try
            {
                var location = assembly.Location;
                if (!string.IsNullOrEmpty(location) && System.IO.File.Exists(location))
                {
                    return System.IO.File.GetCreationTime(location);
                }
            }
            catch
            {
                // 忽略错误
            }
            
            return DateTime.Now;
        }

        #endregion
    }
}
