using SSIC.DevTools.BuilderTools;
using Serilog;

namespace SSIC.DevTools
{
    /// <summary>
    /// 代码生成器控制台应用
    /// </summary>
    public class CodeGenerator
    {
        /// <summary>
        /// 运行代码生成器
        /// </summary>
        public static void RunGenerator()
        {
            // 配置Serilog
            Log.Logger = new LoggerConfiguration()
                .WriteTo.Console()
                .WriteTo.File("logs/codegen-.txt", rollingInterval: RollingInterval.Day)
                .CreateLogger();

            try
            {
                Console.WriteLine("=== SSIC 模块化代码生成器 ===");
                Console.WriteLine();
                
                Console.WriteLine("请选择生成模式:");
                Console.WriteLine("1. 生成新的模块化项目结构 (推荐)");
                Console.WriteLine("2. 生成传统项目结构 (兼容旧版)");
                Console.WriteLine("0. 退出");
                Console.Write("请输入选择 (0-2): ");
                
                var choice = Console.ReadLine();
                
                switch (choice)
                {
                    case "1":
                        GenerateModuleProjects();
                        break;
                    case "2":
                        GenerateTraditionalProjects();
                        break;
                    case "0":
                        Console.WriteLine("退出程序。");
                        return;
                    default:
                        Console.WriteLine("无效选择，请重新运行程序。");
                        return;
                }
                
                Console.WriteLine();
                Console.WriteLine("代码生成完成! 按任意键退出...");
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "代码生成过程中发生错误");
                Console.WriteLine($"错误: {ex.Message}");
                Console.WriteLine("按任意键退出...");
                Console.ReadKey();
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }

        /// <summary>
        /// 生成模块化项目结构
        /// </summary>
        private static void GenerateModuleProjects()
        {
            Console.WriteLine();
            Console.WriteLine("开始生成模块化项目代码...");

            // 询问是否需要指定生成范围
            Console.WriteLine("是否需要指定生成范围? (y/n，默认n):");
            var needConfig = Console.ReadLine()?.ToLower() == "y";

            string? moduleName = null;
            string[]? serviceNames = null;
            string[]? controllerNames = null;

            if (needConfig)
            {
                Console.Write("请输入模块名称 (可选，为空则生成所有模块): ");
                moduleName = Console.ReadLine()?.Trim();
                if (string.IsNullOrEmpty(moduleName)) moduleName = null;

                Console.Write("请输入实体名称 (可选，多个用逗号分隔，将同时生成服务和控制器): ");
                var entityInput = Console.ReadLine()?.Trim();
                if (!string.IsNullOrEmpty(entityInput))
                {
                    serviceNames = entityInput.Split(',').Select(s => s.Trim()).Where(s => !string.IsNullOrEmpty(s)).ToArray();
                    controllerNames = serviceNames; // 使用相同的实体名称数组
                    if (serviceNames.Length == 0)
                    {
                        serviceNames = null;
                        controllerNames = null;
                    }
                }

                Console.WriteLine();
                Console.WriteLine("生成配置:");
                Console.WriteLine($"- 模块名称: {moduleName ?? "所有模块"}");
                Console.WriteLine($"- 服务名称: {(serviceNames != null ? string.Join(", ", serviceNames) : "所有服务")}");
                Console.WriteLine($"- 控制器名称: {(controllerNames != null ? string.Join(", ", controllerNames) : "所有控制器")}");
                Console.WriteLine();
            }

            Log.Information("开始生成模块化项目代码...");

            var generator = new CreateModuleProject();
            string result;

            if (!string.IsNullOrEmpty(moduleName))
            {
                result = generator.CreateModuleFiles(moduleName, serviceNames, controllerNames);
            }
            else
            {
                result = generator.CreateModuleFiles(serviceNames, controllerNames);
            }

            Console.WriteLine(result);
            Log.Information(result);
            
            Console.WriteLine();
            Console.WriteLine("生成的项目结构:");
            Console.WriteLine("SSIC.Modules/");
            Console.WriteLine("├── SSIC.Modules.{ModuleName}/");
            Console.WriteLine("│   ├── SSIC.Modules.{ModuleName}.Services/");
            Console.WriteLine("│   │   ├── Interfaces/");
            Console.WriteLine("│   │   │   ├── I{EntityName}Service.cs");
            Console.WriteLine("│   │   │   └── Expands/");
            Console.WriteLine("│   │   │       └── I{EntityName}Service.cs");
            Console.WriteLine("│   │   ├── Implementations/");
            Console.WriteLine("│   │   │   ├── {EntityName}Service.cs");
            Console.WriteLine("│   │   │   └── Expands/");
            Console.WriteLine("│   │   │       └── {EntityName}Service.cs");
            Console.WriteLine("│   │   └── SSIC.Modules.{ModuleName}.Services.csproj");
            Console.WriteLine("│   ├── SSIC.Modules.{ModuleName}.Controller/");
            Console.WriteLine("│   │   ├── Controllers/");
            Console.WriteLine("│   │   │   ├── {EntityName}Controller.cs");
            Console.WriteLine("│   │   │   └── Expands/");
            Console.WriteLine("│   │   │       └── {EntityName}Controller.cs");
            Console.WriteLine("│   │   ├── Program.cs");
            Console.WriteLine("│   │   └── SSIC.Modules.{ModuleName}.Controller.csproj");
            Console.WriteLine("│   └── SSIC.Modules.{ModuleName}.sln");
        }

        /// <summary>
        /// 生成传统项目结构
        /// </summary>
        private static void GenerateTraditionalProjects()
        {
            Console.WriteLine();
            Console.WriteLine("开始生成传统项目代码...");
            Log.Information("开始生成传统项目代码...");
            
            var generator = new CreateProject();
            var result = generator.createfile();
            
            Console.WriteLine("传统项目代码生成完成!");
            Log.Information("传统项目代码生成完成!");
            
            Console.WriteLine();
            Console.WriteLine("生成的项目结构:");
            Console.WriteLine("SSFB.Business/");
            Console.WriteLine("├── {BusinessName}/");
            Console.WriteLine("│   ├── IService/");
            Console.WriteLine("│   │   ├── I{EntityName}Service.cs");
            Console.WriteLine("│   │   └── Expands/");
            Console.WriteLine("│   │       └── I{EntityName}Service.cs");
            Console.WriteLine("│   └── Service/");
            Console.WriteLine("│       ├── {EntityName}Service.cs");
            Console.WriteLine("│       └── Expands/");
            Console.WriteLine("│           └── {EntityName}Service.cs");
            Console.WriteLine("SSFB.WebApi/Controllers/");
            Console.WriteLine("└── {BusinessName}/");
            Console.WriteLine("    ├── {EntityName}Controller.cs");
            Console.WriteLine("    └── Expands/");
            Console.WriteLine("        └── {EntityName}Controller.cs");
        }

        /// <summary>
        /// 显示帮助信息
        /// </summary>
        private static void ShowHelp()
        {
            Console.WriteLine("=== SSIC 代码生成器使用说明 ===");
            Console.WriteLine();
            Console.WriteLine("功能说明:");
            Console.WriteLine("1. 模块化项目结构 - 生成符合SSIC.Modules.{ModuleName}结构的代码");
            Console.WriteLine("   - 支持Service层架构");
            Console.WriteLine("   - 自动依赖注入");
            Console.WriteLine("   - 完整的项目文件");
            Console.WriteLine("   - 解决方案文件");
            Console.WriteLine();
            Console.WriteLine("2. 传统项目结构 - 兼容旧版本的代码生成");
            Console.WriteLine("   - SSFB命名空间");
            Console.WriteLine("   - 传统的文件夹结构");
            Console.WriteLine();
            Console.WriteLine("使用前提:");
            Console.WriteLine("- 确保SSIC.Entity文件夹中有实体类文件");
            Console.WriteLine("- 确保SSIC.Infrastructure/Template文件夹中有模板文件");
            Console.WriteLine();
            Console.WriteLine("生成的文件:");
            Console.WriteLine("- 服务接口文件 (I{EntityName}Service.cs)");
            Console.WriteLine("- 服务实现文件 ({EntityName}Service.cs)");
            Console.WriteLine("- 控制器文件 ({EntityName}Controller.cs)");
            Console.WriteLine("- 项目文件 (.csproj)");
            Console.WriteLine("- 解决方案文件 (.sln)");
        }
    }
}
