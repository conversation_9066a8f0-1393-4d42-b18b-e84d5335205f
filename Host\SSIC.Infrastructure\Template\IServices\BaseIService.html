/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *Service提供业务逻辑操作，如果要增加业务逻辑请在当前目录下Expands文件夹I{EntityName}Service编写接口
 */
using SSIC.Infrastructure.BaseProvider;
using SSIC.Infrastructure.DependencyInjection;
using SSIC.Infrastructure.DependencyInjection.Interface;
using SSIC.Entity.{BusinessName};
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SSIC.Modules.{BusinessName}.Services.Interfaces
{
    /// <summary>
    /// {EntityName}服务接口{EntityDescription}
    /// 提供{EntityName}实体的业务逻辑操作
    /// </summary>
    public partial interface I{EntityName}Service : IServiceBase, IScoped
    {
        // 在此处添加{EntityName}特定的业务方法
    }
}
