using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SSIC.Infrastructure.Middleware;
using SSIC.Infrastructure.Startups.Endpoints;
using SSIC.Infrastructure.Startups.ModuleManager;
using System;
using System.Linq;

namespace SSIC.Infrastructure.Extensions
{
    /// <summary>
    /// 应用程序构建器扩展方法
    /// </summary>
    public static class ApplicationBuilderExtensions
    {
        /// <summary>
        /// 使用SSIC基础设施中间件
        /// </summary>
        /// <param name="app">应用程序构建器</param>
        /// <returns>应用程序构建器</returns>
        public static IApplicationBuilder UseSSICInfrastructure(this IApplicationBuilder app)
        {
            // 初始化动态端点管理器
            InitializeDynamicEndpointManager(app);

            // 添加模块热加载中间件（开发环境）
            app.UseModuleHotReload();

            // 添加动态路由中间件
            app.UseDynamicRouting();

            // 添加端点刷新中间件
            app.UseEndpointRefresh();

            return app;
        }

        /// <summary>
        /// 使用动态路由
        /// </summary>
        /// <param name="app">应用程序构建器</param>
        /// <returns>应用程序构建器</returns>
        public static IApplicationBuilder UseDynamicRouting(this IApplicationBuilder app)
        {
            return app.UseMiddleware<DynamicRoutingMiddleware>();
        }

        /// <summary>
        /// 使用模块热加载（仅在开发环境）
        /// </summary>
        /// <param name="app">应用程序构建器</param>
        /// <returns>应用程序构建器</returns>
        public static IApplicationBuilder UseModuleHotReload(this IApplicationBuilder app)
        {
            var env = app.ApplicationServices.GetService<IWebHostEnvironment>();

            // 只在开发环境启用热加载
            if (env != null && env.IsDevelopment())
            {
                return app.UseMiddleware<ModuleHotReloadMiddleware>();
            }

            return app;
        }

        /// <summary>
        /// 使用端点刷新中间件
        /// </summary>
        /// <param name="app">应用程序构建器</param>
        /// <returns>应用程序构建器</returns>
        public static IApplicationBuilder UseEndpointRefresh(this IApplicationBuilder app)
        {
            return app.UseMiddleware<EndpointRefreshMiddleware>();
        }

        /// <summary>
        /// 初始化并刷新所有模块端点
        /// </summary>
        /// <param name="app">应用程序构建器</param>
        /// <returns>应用程序构建器</returns>
        public static IApplicationBuilder InitializeAndRefreshModules(this IApplicationBuilder app)
        {
            try
            {
                var loggerFactory = app.ApplicationServices.GetService<ILoggerFactory>();
                var logger = loggerFactory?.CreateLogger("ApplicationBuilderExtensions");
                logger?.LogInformation("开始初始化和刷新模块端点...");

                var moduleDiscoveryService = app.ApplicationServices.GetService<IModuleDiscoveryService>();
                var endpointManagementService = app.ApplicationServices.GetService<IEndpointManagementService>();

                if (moduleDiscoveryService != null && endpointManagementService != null)
                {
                    // 发现所有模块
                    var modules = moduleDiscoveryService.DiscoverLoadedModules().ToList();
                    logger?.LogInformation("发现 {ModuleCount} 个模块", modules.Count);

                    // 刷新端点
                    var refreshTask = endpointManagementService.RefreshEndpointsAsync(modules);
                    var endpoints = refreshTask.GetAwaiter().GetResult();

                    logger?.LogInformation("模块端点初始化完成，共 {EndpointCount} 个端点", endpoints.Count());

                    // 通知动态端点管理器
                    var dynamicEndpointManager = DynamicEndpointManager.Instance;
                    dynamicEndpointManager?.RefreshEndpoints(app.ApplicationServices, modules.Select(m => m.Assembly));
                }
                else
                {
                    logger?.LogWarning("无法获取模块发现服务或端点管理服务");
                }
            }
            catch (Exception ex)
            {
                var loggerFactory = app.ApplicationServices.GetService<ILoggerFactory>();
            var logger = loggerFactory?.CreateLogger("ApplicationBuilderExtensions");
                logger?.LogError(ex, "初始化和刷新模块端点时发生错误");
            }

            return app;
        }

        /// <summary>
        /// 初始化动态端点管理器
        /// </summary>
        private static void InitializeDynamicEndpointManager(IApplicationBuilder app)
        {
            try
            {
                // 初始化动态端点管理器
                DynamicEndpointManager.Initialize(app.ApplicationServices);

                var loggerFactory = app.ApplicationServices.GetService<ILoggerFactory>();
                var logger = loggerFactory?.CreateLogger("ApplicationBuilderExtensions");
                logger?.LogInformation("动态端点管理器初始化完成");
            }
            catch (Exception ex)
            {
                var loggerFactory = app.ApplicationServices.GetService<ILoggerFactory>();
                var logger = loggerFactory?.CreateLogger("ApplicationBuilderExtensions");
                logger?.LogError(ex, "初始化动态端点管理器时发生错误");
            }
        }
    }
}
