{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "codegen.html", "AssetFile": "codegen.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000248818114"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4018"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"D1DwPYnQwIFFLn1RODRGFWiNpW0IAdROr51pk6cpsHA=\""}, {"Name": "ETag", "Value": "W/\"IImG8VAR6H3oQttRVgh6xkVuBHewvnLOV/Up6rfLcPM=\""}, {"Name": "Last-Modified", "Value": "Wed, 20 Aug 2025 10:29:44 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IImG8VAR6H3oQttRVgh6xkVuBHewvnLOV/Up6rfLcPM="}]}, {"Route": "codegen.html", "AssetFile": "codegen.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "16853"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"IImG8VAR6H3oQttRVgh6xkVuBHewvnLOV/Up6rfLcPM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 19 Aug 2025 08:28:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IImG8VAR6H3oQttRVgh6xkVuBHewvnLOV/Up6rfLcPM="}]}, {"Route": "codegen.html.gz", "AssetFile": "codegen.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4018"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"D1DwPYnQwIFFLn1RODRGFWiNpW0IAdROr51pk6cpsHA=\""}, {"Name": "Last-Modified", "Value": "Wed, 20 Aug 2025 10:29:44 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-D1DwPYnQwIFFLn1RODRGFWiNpW0IAdROr51pk6cpsHA="}]}, {"Route": "codegen.sxaa5jpl0x.html", "AssetFile": "codegen.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000248818114"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4018"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"D1DwPYnQwIFFLn1RODRGFWiNpW0IAdROr51pk6cpsHA=\""}, {"Name": "ETag", "Value": "W/\"IImG8VAR6H3oQttRVgh6xkVuBHewvnLOV/Up6rfLcPM=\""}, {"Name": "Last-Modified", "Value": "Wed, 20 Aug 2025 10:29:44 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sxaa5jpl0x"}, {"Name": "integrity", "Value": "sha256-IImG8VAR6H3oQttRVgh6xkVuBHewvnLOV/Up6rfLcPM="}, {"Name": "label", "Value": "codegen.html"}]}, {"Route": "codegen.sxaa5jpl0x.html", "AssetFile": "codegen.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "16853"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"IImG8VAR6H3oQttRVgh6xkVuBHewvnLOV/Up6rfLcPM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 19 Aug 2025 08:28:00 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sxaa5jpl0x"}, {"Name": "integrity", "Value": "sha256-IImG8VAR6H3oQttRVgh6xkVuBHewvnLOV/Up6rfLcPM="}, {"Name": "label", "Value": "codegen.html"}]}, {"Route": "codegen.sxaa5jpl0x.html.gz", "AssetFile": "codegen.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4018"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"D1DwPYnQwIFFLn1RODRGFWiNpW0IAdROr51pk6cpsHA=\""}, {"Name": "Last-Modified", "Value": "Wed, 20 Aug 2025 10:29:44 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sxaa5jpl0x"}, {"Name": "integrity", "Value": "sha256-D1DwPYnQwIFFLn1RODRGFWiNpW0IAdROr51pk6cpsHA="}, {"Name": "label", "Value": "codegen.html.gz"}]}]}