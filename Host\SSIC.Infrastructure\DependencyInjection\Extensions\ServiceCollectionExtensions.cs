
using Microsoft.Extensions.DependencyInjection;
using SSIC.Infrastructure.BaseProvider;
using SSIC.Infrastructure.DependencyInjection.Interface;
using System;
using System.Linq;
using System.Reflection;

namespace SSIC.Infrastructure.DependencyInjection.Extensions
{
    /// <summary>
    /// 作用域注册服务
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddDependencyInjection(this IServiceCollection services, params Assembly[] assemblies)
        {
            assemblies = assemblies.Length == 0
                ? AppDomain.CurrentDomain.GetAssemblies()
                : assemblies;

            // 获取所有非泛型类型
            var types = assemblies
                .SelectMany(a => a.GetTypes())
                .Where(t => t.IsClass &&
                           !t.IsAbstract &&
                           !t.IsGenericTypeDefinition)
                .ToList();

            foreach (var type in types)
            {
                // 处理实现了 IScoped 的非泛型类
                if (typeof(IScoped).IsAssignableFrom(type))
                {
                    var interfaces = type.GetInterfaces()
                        .Where(i => i != typeof(IScoped))
                        .ToList();

                    if (interfaces.Any())
                    {
                        foreach (var interfaceType in interfaces)
                        {
                            services.AddScoped(interfaceType, type);
                        }
                    }
                    else
                    {
                        services.AddScoped(type);
                    }
                }

                // 处理实现了 ITransient 的类
                if (typeof(ITransient).IsAssignableFrom(type))
                {
                    var interfaces = type.GetInterfaces()
                        .Where(i => i != typeof(ITransient))
                        .ToList();

                    if (interfaces.Any())
                    {
                        foreach (var interfaceType in interfaces)
                        {
                            services.AddTransient(interfaceType, type);
                        }
                    }
                    else
                    {
                        services.AddTransient(type);
                    }
                }

                // 处理实现了 ISingleton 的类
                if (typeof(ISingleton).IsAssignableFrom(type))
                {
                    var interfaces = type.GetInterfaces()
                        .Where(i => i != typeof(ISingleton))
                        .ToList();

                    if (interfaces.Any())
                    {
                        foreach (var interfaceType in interfaces)
                        {
                            services.AddSingleton(interfaceType, type);
                        }
                    }
                    else
                    {
                        services.AddSingleton(type);
                    }
                }
            }

            // 处理泛型服务
var genericTypes = assemblies
    .SelectMany(a => a.GetTypes())
    .Where(t => t.IsClass &&
               !t.IsAbstract &&
               t.IsGenericTypeDefinition)
    .ToList();

            foreach (var type in genericTypes)
            {
                // 获取类型实现的所有接口
                var genericInterfaces = type.GetInterfaces()
                    .Where(i => i.IsGenericType)
                    .ToList();

                foreach (var interfaceType in genericInterfaces)
                {
                    // 根据生命周期注册服务
                    if (typeof(IScoped).IsAssignableFrom(type))
                    {
                        services.AddScoped(interfaceType, type);
                    }
                    else if (typeof(ITransient).IsAssignableFrom(type))
                    {
                        services.AddTransient(interfaceType, type);
                    }
                    else if (typeof(ISingleton).IsAssignableFrom(type))
                    {
                        services.AddSingleton(interfaceType, type);
                    }
                }
            }

            return services;
        }
    }
}