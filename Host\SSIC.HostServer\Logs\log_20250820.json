{"@t":"2025-08-19T17:46:06.9524255Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:06.9866470Z","@mt":"动态端点管理器初始化完成","SourceContext":"ApplicationBuilderExtensions","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:06.9885970Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Controller.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:06.9895942Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Controller.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:10.1126951Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth.Controller","AssemblyName":"SSIC.Modules.Auth.Controller, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:10.1139146Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:10.1140244Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8255698Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth.Controller","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8265277Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8300311Z","@mt":"发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8458546Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8460012Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth.Controller","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8461917Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8463636Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8467556Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8469783Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8472697Z","@mt":"发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8475531Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8479372Z","@mt":"发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8483064Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8483766Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8484489Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth.Controller","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8490507Z","@mt":"开始刷新统一OpenAPI文档...","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8498060Z","@mt":"发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8500630Z","@mt":"发现 {Count} 个已加载的模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8504606Z","@mt":"统一OpenAPI文档刷新完成","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8505617Z","@mt":"已刷新OpenAPI文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8505949Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Services.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8506324Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Services.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8564559Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth.Services","AssemblyName":"SSIC.Modules.Auth.Services, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8566004Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8566322Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8569814Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth.Services","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8570365Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8613893Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8615184Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth.Services","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8621943Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8626075Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8626783Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8632666Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8638589Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8639303Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8639565Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8640180Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth.Services","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8641134Z","@mt":"开始刷新统一OpenAPI文档...","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8645802Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8647517Z","@mt":"发现 {Count} 个已加载的模块","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8648026Z","@mt":"统一OpenAPI文档刷新完成","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.8648298Z","@mt":"已刷新OpenAPI文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.9931772Z","@mt":"Now listening on: {address}","address":"https://localhost:55825","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:14.9937840Z","@mt":"Now listening on: {address}","address":"http://localhost:55827","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:15.0261050Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:15.0262450Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:15.0262773Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:15.8835043Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:15.8840712Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:15.8841116Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:15.8846439Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:15.8849915Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:15.8850456Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:15.8850704Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:16.3961778Z","@mt":"开始刷新统一OpenAPI文档...","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:16.3970106Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:16.3972328Z","@mt":"发现 {Count} 个已加载的模块","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:16.3972643Z","@mt":"统一OpenAPI文档刷新完成","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:16.3973806Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:16.9038938Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:16.9058466Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:16.9066750Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:16.9067738Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:18.3832169Z","@mt":"动态端点管理器初始化完成","SourceContext":"ApplicationBuilderExtensions","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:18.3862754Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:18.4257522Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Controller.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:18.4336696Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Controller.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:18.9548765Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth.Controller","AssemblyName":"SSIC.Modules.Auth.Controller, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:18.9874366Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:18.9895166Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:18.9897323Z","@mt":"发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.0173742Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth.Controller","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.0198207Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.0229914Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.0315764Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.0337515Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.0347346Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth.Controller","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.0384570Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.0406510Z","@mt":"发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.0418156Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.0436372Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.0533457Z","@mt":"发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.0557690Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.0585078Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.0603081Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth.Controller","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.0643260Z","@mt":"开始刷新统一OpenAPI文档...","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.0677436Z","@mt":"发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.0709104Z","@mt":"发现 {Count} 个已加载的模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.0747973Z","@mt":"统一OpenAPI文档刷新完成","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.0766706Z","@mt":"已刷新OpenAPI文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.0785887Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Services.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.0800051Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Services.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.1556552Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth.Services","AssemblyName":"SSIC.Modules.Auth.Services, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.1582529Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.1594020Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.1669686Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth.Services","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.1687276Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.1793330Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.1808205Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth.Services","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.1837112Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.1860497Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.1880409Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.1900194Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.1918660Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.1935427Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.1947916Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.1959372Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth.Services","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.1976754Z","@mt":"开始刷新统一OpenAPI文档...","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.1998812Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.2014024Z","@mt":"发现 {Count} 个已加载的模块","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.2024588Z","@mt":"统一OpenAPI文档刷新完成","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.2033349Z","@mt":"已刷新OpenAPI文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.3774699Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.3800128Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.4481618Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.4498031Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:19.4507728Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:20.2104000Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:20.2123485Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:20.2137760Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:20.2161202Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:20.2179246Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:20.2191788Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:20.2205213Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:20.7319903Z","@mt":"开始刷新统一OpenAPI文档...","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:20.7337906Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:20.7350344Z","@mt":"发现 {Count} 个已加载的模块","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:20.7360465Z","@mt":"统一OpenAPI文档刷新完成","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:20.7370009Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:21.2520593Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:21.2551372Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:21.2572069Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:21.2579559Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:21.4880968Z","@mt":"初始化模块热加载监控...","@tr":"c659a60c1867bc9d4aee56e0b1d2c1b5","@sp":"02f715fdec190697","SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNEV92SN5SO8:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV92SN5SO8","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:21.4928139Z","@mt":"为目录 {Directory} 设置文件监控器","@tr":"c659a60c1867bc9d4aee56e0b1d2c1b5","@sp":"02f715fdec190697","Directory":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules","SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNEV92SN5SO8:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV92SN5SO8","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:21.4940895Z","@mt":"模块热加载监控初始化完成，监控 {DirectoryCount} 个目录","@tr":"c659a60c1867bc9d4aee56e0b1d2c1b5","@sp":"02f715fdec190697","DirectoryCount":1,"SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNEV92SN5SO8:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV92SN5SO8","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:21.4964602Z","@mt":"初始化动态路由系统...","@tr":"c659a60c1867bc9d4aee56e0b1d2c1b5","@sp":"02f715fdec190697","SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNEV92SN5SO8:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV92SN5SO8","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:21.4987102Z","@mt":"发现 {Count} 个模块程序集","@tr":"c659a60c1867bc9d4aee56e0b1d2c1b5","@sp":"02f715fdec190697","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV92SN5SO8:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV92SN5SO8","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:21.5013639Z","@mt":"动态路由系统初始化完成，发现 {ModuleCount} 个模块","@tr":"c659a60c1867bc9d4aee56e0b1d2c1b5","@sp":"02f715fdec190697","ModuleCount":2,"SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNEV92SN5SO8:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV92SN5SO8","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:21.5054131Z","@mt":"发现 {Count} 个模块程序集","@tr":"c659a60c1867bc9d4aee56e0b1d2c1b5","@sp":"02f715fdec190697","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV92SN5SO8:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV92SN5SO8","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:21.5101244Z","@mt":"首次请求触发端点刷新","@tr":"c659a60c1867bc9d4aee56e0b1d2c1b5","@sp":"02f715fdec190697","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEV92SN5SO8:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV92SN5SO8","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:21.5117206Z","@mt":"发现 {Count} 个模块程序集","@tr":"c659a60c1867bc9d4aee56e0b1d2c1b5","@sp":"02f715fdec190697","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV92SN5SO8:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV92SN5SO8","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:21.5141008Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"c659a60c1867bc9d4aee56e0b1d2c1b5","@sp":"02f715fdec190697","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEV92SN5SO8:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV92SN5SO8","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:21.5161462Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"c659a60c1867bc9d4aee56e0b1d2c1b5","@sp":"02f715fdec190697","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEV92SN5SO8:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV92SN5SO8","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:21.5184466Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"c659a60c1867bc9d4aee56e0b1d2c1b5","@sp":"02f715fdec190697","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEV92SN5SO8:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV92SN5SO8","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:21.5213807Z","@mt":"发现 {Count} 个模块程序集","@tr":"c659a60c1867bc9d4aee56e0b1d2c1b5","@sp":"02f715fdec190697","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV92SN5SO8:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV92SN5SO8","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:21.5229518Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"c659a60c1867bc9d4aee56e0b1d2c1b5","@sp":"02f715fdec190697","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEV92SN5SO8:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV92SN5SO8","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:21.5252509Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"c659a60c1867bc9d4aee56e0b1d2c1b5","@sp":"02f715fdec190697","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEV92SN5SO8:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV92SN5SO8","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:21.5276134Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"c659a60c1867bc9d4aee56e0b1d2c1b5","@sp":"02f715fdec190697","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEV92SN5SO8:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV92SN5SO8","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:21.5293963Z","@mt":"请求触发的端点刷新完成，发现 {ModuleCount} 个模块，{EndpointCount} 个端点","@tr":"c659a60c1867bc9d4aee56e0b1d2c1b5","@sp":"02f715fdec190697","ModuleCount":2,"EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEV92SN5SO8:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV92SN5SO8","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:21.6348063Z","@mt":"发现 {Count} 个模块程序集","@tr":"2460a2fd9d76bbd23bbd3049f94f9d75","@sp":"cd5f48085fd018b5","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV92SN5SO8:00000007","RequestPath":"/scalar/scalar.js","ConnectionId":"0HNEV92SN5SO8","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:21.6367327Z","@mt":"发现 {Count} 个模块程序集","@tr":"27f803b485a10014fc51768e5bbd4251","@sp":"5148a8bdb5548a65","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV92SN5SO8:00000005","RequestPath":"/scalar/scalar.aspnetcore.js","ConnectionId":"0HNEV92SN5SO8","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:22.1946548Z","@mt":"发现 {Count} 个模块程序集","@tr":"7e0258b4ced36a88f3f83ad2f6b681a5","@sp":"cef307ecd39b64b1","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV92SN5SO8:0000000D","RequestPath":"/favicon.ico","ConnectionId":"0HNEV92SN5SO8","MachineName":"LAPTOP-F6SGUTN5","ThreadId":27,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.1616031Z","@mt":"动态端点管理器初始化完成","SourceContext":"ApplicationBuilderExtensions","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.1659812Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.1856798Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Controller.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.1862696Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Controller.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.2330604Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth.Controller","AssemblyName":"SSIC.Modules.Auth.Controller, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.2338359Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.2539604Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.2798055Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth.Controller","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.2804771Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3293158Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3294767Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth.Controller","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3320879Z","@mt":"发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3382795Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3393163Z","@mt":"发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3405540Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3407187Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3408652Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth.Controller","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3417031Z","@mt":"开始刷新统一OpenAPI文档...","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3422562Z","@mt":"发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3424454Z","@mt":"发现 {Count} 个已加载的模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3426893Z","@mt":"发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3427724Z","@mt":"统一OpenAPI文档刷新完成","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3428264Z","@mt":"已刷新OpenAPI文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3428537Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Services.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3428876Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Services.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3430522Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3431044Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3432477Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3435842Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3570066Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth.Services","AssemblyName":"SSIC.Modules.Auth.Services, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3571404Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3571719Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3653546Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth.Services","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3654754Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3719088Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3720386Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth.Services","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3727708Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3734778Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3735621Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3742862Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3746275Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3746698Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3746996Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3747706Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth.Services","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3748792Z","@mt":"开始刷新统一OpenAPI文档...","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3753928Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3755631Z","@mt":"发现 {Count} 个已加载的模块","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3756042Z","@mt":"统一OpenAPI文档刷新完成","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.3756278Z","@mt":"已刷新OpenAPI文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.5897429Z","@mt":"Now listening on: {address}","address":"https://localhost:56890","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.5904643Z","@mt":"Now listening on: {address}","address":"http://localhost:56898","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.6460056Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.6461414Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.6461752Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.9948977Z","@mt":"初始化模块热加载监控...","@tr":"4947e0437d96e90575d16a91b30610a7","@sp":"86a91b62de6eb82e","SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNEV937EQ1C6:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV937EQ1C6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.9969344Z","@mt":"为目录 {Directory} 设置文件监控器","@tr":"4947e0437d96e90575d16a91b30610a7","@sp":"86a91b62de6eb82e","Directory":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules","SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNEV937EQ1C6:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV937EQ1C6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.9970309Z","@mt":"模块热加载监控初始化完成，监控 {DirectoryCount} 个目录","@tr":"4947e0437d96e90575d16a91b30610a7","@sp":"86a91b62de6eb82e","DirectoryCount":1,"SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNEV937EQ1C6:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV937EQ1C6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.9980088Z","@mt":"初始化动态路由系统...","@tr":"4947e0437d96e90575d16a91b30610a7","@sp":"86a91b62de6eb82e","SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNEV937EQ1C6:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV937EQ1C6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.9987888Z","@mt":"发现 {Count} 个模块程序集","@tr":"4947e0437d96e90575d16a91b30610a7","@sp":"86a91b62de6eb82e","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV937EQ1C6:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV937EQ1C6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:55.9999214Z","@mt":"动态路由系统初始化完成，发现 {ModuleCount} 个模块","@tr":"4947e0437d96e90575d16a91b30610a7","@sp":"86a91b62de6eb82e","ModuleCount":2,"SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNEV937EQ1C6:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV937EQ1C6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:56.0010698Z","@mt":"发现 {Count} 个模块程序集","@tr":"4947e0437d96e90575d16a91b30610a7","@sp":"86a91b62de6eb82e","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV937EQ1C6:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV937EQ1C6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:56.0033743Z","@mt":"首次请求触发端点刷新","@tr":"4947e0437d96e90575d16a91b30610a7","@sp":"86a91b62de6eb82e","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEV937EQ1C6:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV937EQ1C6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:56.0039666Z","@mt":"发现 {Count} 个模块程序集","@tr":"4947e0437d96e90575d16a91b30610a7","@sp":"86a91b62de6eb82e","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV937EQ1C6:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV937EQ1C6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:56.0044671Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"4947e0437d96e90575d16a91b30610a7","@sp":"86a91b62de6eb82e","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEV937EQ1C6:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV937EQ1C6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:56.0045608Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"4947e0437d96e90575d16a91b30610a7","@sp":"86a91b62de6eb82e","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEV937EQ1C6:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV937EQ1C6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:56.0046064Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"4947e0437d96e90575d16a91b30610a7","@sp":"86a91b62de6eb82e","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEV937EQ1C6:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV937EQ1C6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:56.0053417Z","@mt":"发现 {Count} 个模块程序集","@tr":"4947e0437d96e90575d16a91b30610a7","@sp":"86a91b62de6eb82e","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV937EQ1C6:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV937EQ1C6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:56.0058187Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"4947e0437d96e90575d16a91b30610a7","@sp":"86a91b62de6eb82e","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEV937EQ1C6:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV937EQ1C6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:56.0059026Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"4947e0437d96e90575d16a91b30610a7","@sp":"86a91b62de6eb82e","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEV937EQ1C6:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV937EQ1C6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:56.0059741Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"4947e0437d96e90575d16a91b30610a7","@sp":"86a91b62de6eb82e","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEV937EQ1C6:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV937EQ1C6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:56.0062639Z","@mt":"请求触发的端点刷新完成，发现 {ModuleCount} 个模块，{EndpointCount} 个端点","@tr":"4947e0437d96e90575d16a91b30610a7","@sp":"86a91b62de6eb82e","ModuleCount":2,"EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEV937EQ1C6:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV937EQ1C6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:56.0784300Z","@mt":"发现 {Count} 个模块程序集","@tr":"494d9f1bdb4482a410aa2c8240f32e93","@sp":"c6c16a28f34b1498","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV937EQ1C6:00000003","RequestPath":"/scalar/scalar.aspnetcore.js","ConnectionId":"0HNEV937EQ1C6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:56.0784299Z","@mt":"发现 {Count} 个模块程序集","@tr":"0aca77f9b8ef7202e3fe5b62e5cac91c","@sp":"06cfad748e1cbdd5","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV937EQ1C6:00000007","RequestPath":"/scalar/scalar.js","ConnectionId":"0HNEV937EQ1C6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:56.3926078Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:56.3931002Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:56.3931611Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:56.3937075Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:56.3939873Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:56.3941239Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:56.3941557Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:56.5098383Z","@mt":"发现 {Count} 个模块程序集","@tr":"4dbc99f294ed1475733616b8f67ac65a","@sp":"d4927c2137d0dfd3","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV937EQ1C6:0000000D","RequestPath":"/favicon.ico","ConnectionId":"0HNEV937EQ1C6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:56.9013176Z","@mt":"开始刷新统一OpenAPI文档...","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:56.9022084Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:56.9024607Z","@mt":"发现 {Count} 个已加载的模块","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:56.9025183Z","@mt":"统一OpenAPI文档刷新完成","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:56.9026652Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:57.4124722Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:57.4146934Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:57.4153567Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:57.4154331Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:50:05.6666475Z","@mt":"发现 {Count} 个模块程序集","@tr":"528a54e7b3318b4094b00258e7b04f47","@sp":"61d6da8e42dc825a","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV937EQ1C6:0000000F","RequestPath":"/api/auth/permission/health","ConnectionId":"0HNEV937EQ1C6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:50:05.6915864Z","@mt":"健康检查被调用: {ControllerName}","@tr":"528a54e7b3318b4094b00258e7b04f47","@sp":"61d6da8e42dc825a","ControllerName":"PermissionController","SourceContext":"SSIC.Modules.Auth.Controllers.PermissionController","ActionId":"5c32577a-1b7b-4d34-bbd3-6f52f925dbfb","ActionName":"SSIC.Modules.Auth.Controllers.PermissionController.Health (SSIC.Modules.Auth.Controller)","RequestId":"0HNEV937EQ1C6:0000000F","RequestPath":"/api/auth/permission/health","ConnectionId":"0HNEV937EQ1C6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:50:11.9530412Z","@mt":"发现 {Count} 个模块程序集","@tr":"54870c360ee6e03eb9ff919e66995d46","@sp":"dbb87fb3edc904f8","Count":3,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV937EQ1C6:00000011","RequestPath":"/api/auth/role/QueryRoleDetails","ConnectionId":"0HNEV937EQ1C6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":31,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:58.8403026Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.3201747Z","@mt":"动态端点管理器初始化完成","SourceContext":"ApplicationBuilderExtensions","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.3269787Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Controller.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.3277550Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Controller.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.4358542Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth.Controller","AssemblyName":"SSIC.Modules.Auth.Controller, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.4402160Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.4501586Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.4798550Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth.Controller","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.4805353Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.4893476Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.4895536Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth.Controller","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.4929785Z","@mt":"发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.4995827Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.5006588Z","@mt":"发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.5016040Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.5017260Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.5018704Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth.Controller","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.5028987Z","@mt":"开始刷新OpenAPI文档...","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.5035959Z","@mt":"发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.5038419Z","@mt":"发现 {Count} 个已加载的模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.5041816Z","@mt":"文档更新通知: 当前加载了 {ModuleCount} 个模块","ModuleCount":1,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.5042373Z","@mt":"已刷新OpenAPI文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.5042647Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Services.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.5042960Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Services.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.5089701Z","@mt":"发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.5095757Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.5096760Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.5099815Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.5103775Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.5234522Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth.Services","AssemblyName":"SSIC.Modules.Auth.Services, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.5922416Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.5922952Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.5990124Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth.Services","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.5991396Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.6044826Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.6046475Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth.Services","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.6055330Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.6064129Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.6065151Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.6072609Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.6077684Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.6078664Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.6079065Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.6079943Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth.Services","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.6080935Z","@mt":"开始刷新OpenAPI文档...","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.6087700Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.6091921Z","@mt":"发现 {Count} 个已加载的模块","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.6092544Z","@mt":"文档更新通知: 当前加载了 {ModuleCount} 个模块","ModuleCount":2,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.6092872Z","@mt":"已刷新OpenAPI文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.9626612Z","@mt":"Now listening on: {address}","address":"https://localhost:63172","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:59.9637747Z","@mt":"Now listening on: {address}","address":"http://localhost:63174","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:00.0511263Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:00.0513150Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:00.0513686Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:00.6275126Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:00.6284498Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:00.6285958Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:00.6292577Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:00.6295022Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:00.6295917Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:00.6296364Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:00.9843566Z","@mt":"初始化模块热加载监控...","@tr":"8d22d3348d791b588b1f6d2bc156910a","@sp":"6f4ee1cbdcd10fbd","SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNEVVPMILUFL:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVPMILUFL","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:00.9870659Z","@mt":"为目录 {Directory} 设置文件监控器","@tr":"8d22d3348d791b588b1f6d2bc156910a","@sp":"6f4ee1cbdcd10fbd","Directory":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules","SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNEVVPMILUFL:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVPMILUFL","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:00.9872100Z","@mt":"模块热加载监控初始化完成，监控 {DirectoryCount} 个目录","@tr":"8d22d3348d791b588b1f6d2bc156910a","@sp":"6f4ee1cbdcd10fbd","DirectoryCount":1,"SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNEVVPMILUFL:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVPMILUFL","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:00.9885527Z","@mt":"初始化动态路由系统...","@tr":"8d22d3348d791b588b1f6d2bc156910a","@sp":"6f4ee1cbdcd10fbd","SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNEVVPMILUFL:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVPMILUFL","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:00.9895605Z","@mt":"发现 {Count} 个模块程序集","@tr":"8d22d3348d791b588b1f6d2bc156910a","@sp":"6f4ee1cbdcd10fbd","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVVPMILUFL:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVPMILUFL","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:00.9910039Z","@mt":"动态路由系统初始化完成，发现 {ModuleCount} 个模块","@tr":"8d22d3348d791b588b1f6d2bc156910a","@sp":"6f4ee1cbdcd10fbd","ModuleCount":2,"SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNEVVPMILUFL:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVPMILUFL","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:00.9928178Z","@mt":"发现 {Count} 个模块程序集","@tr":"8d22d3348d791b588b1f6d2bc156910a","@sp":"6f4ee1cbdcd10fbd","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVVPMILUFL:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVPMILUFL","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:00.9962636Z","@mt":"首次请求触发端点刷新","@tr":"8d22d3348d791b588b1f6d2bc156910a","@sp":"6f4ee1cbdcd10fbd","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEVVPMILUFL:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVPMILUFL","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:00.9972032Z","@mt":"发现 {Count} 个模块程序集","@tr":"8d22d3348d791b588b1f6d2bc156910a","@sp":"6f4ee1cbdcd10fbd","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVVPMILUFL:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVPMILUFL","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:00.9977149Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"8d22d3348d791b588b1f6d2bc156910a","@sp":"6f4ee1cbdcd10fbd","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVVPMILUFL:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVPMILUFL","MachineName":"LAPTOP-F6SGUTN5","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:00.9978295Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"8d22d3348d791b588b1f6d2bc156910a","@sp":"6f4ee1cbdcd10fbd","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVVPMILUFL:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVPMILUFL","MachineName":"LAPTOP-F6SGUTN5","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:00.9978821Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"8d22d3348d791b588b1f6d2bc156910a","@sp":"6f4ee1cbdcd10fbd","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVVPMILUFL:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVPMILUFL","MachineName":"LAPTOP-F6SGUTN5","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:00.9988035Z","@mt":"发现 {Count} 个模块程序集","@tr":"8d22d3348d791b588b1f6d2bc156910a","@sp":"6f4ee1cbdcd10fbd","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVVPMILUFL:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVPMILUFL","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:00.9992148Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"8d22d3348d791b588b1f6d2bc156910a","@sp":"6f4ee1cbdcd10fbd","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVVPMILUFL:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVPMILUFL","MachineName":"LAPTOP-F6SGUTN5","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:00.9992930Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"8d22d3348d791b588b1f6d2bc156910a","@sp":"6f4ee1cbdcd10fbd","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVVPMILUFL:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVPMILUFL","MachineName":"LAPTOP-F6SGUTN5","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:00.9993600Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"8d22d3348d791b588b1f6d2bc156910a","@sp":"6f4ee1cbdcd10fbd","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVVPMILUFL:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVPMILUFL","MachineName":"LAPTOP-F6SGUTN5","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:00.9994541Z","@mt":"请求触发的端点刷新完成，发现 {ModuleCount} 个模块，{EndpointCount} 个端点","@tr":"8d22d3348d791b588b1f6d2bc156910a","@sp":"6f4ee1cbdcd10fbd","ModuleCount":2,"EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEVVPMILUFL:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVPMILUFL","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:01.0082890Z","@mt":"发现 {Count} 个模块程序集","@tr":"f455bb4017dcac3904bdf82f340727d6","@sp":"fdb2d47efd0242f2","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVVPMILUFL:00000002","RequestPath":"/dapr/subscribe","ConnectionId":"0HNEVVPMILUFL","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:01.1512831Z","@mt":"开始刷新OpenAPI文档...","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:01.1529760Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:01.1537168Z","@mt":"发现 {Count} 个已加载的模块","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:01.1538545Z","@mt":"文档更新通知: 当前加载了 {ModuleCount} 个模块","ModuleCount":2,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:01.1543048Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:01.6582072Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:01.6609077Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:01.6618243Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:30:01.6619216Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.3068821Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.3387076Z","@mt":"动态端点管理器初始化完成","SourceContext":"ApplicationBuilderExtensions","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.3439629Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Controller.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.3445119Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Controller.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.4333286Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth.Controller","AssemblyName":"SSIC.Modules.Auth.Controller, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.4345297Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.4413936Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.4664198Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth.Controller","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.4669647Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.4750730Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.4752311Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth.Controller","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.4818364Z","@mt":"发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.4870250Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.4879767Z","@mt":"发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.4896462Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.4898106Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.4899257Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth.Controller","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.4907784Z","@mt":"开始刷新OpenAPI文档...","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.4914122Z","@mt":"发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.4916281Z","@mt":"发现 {Count} 个已加载的模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.4920664Z","@mt":"文档更新通知: 当前加载了 {ModuleCount} 个模块","ModuleCount":1,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.4921396Z","@mt":"已刷新OpenAPI文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.4922203Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Services.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.4922642Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Services.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.4928165Z","@mt":"发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.4931123Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.4931778Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.4934560Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.4937866Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.5079450Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth.Services","AssemblyName":"SSIC.Modules.Auth.Services, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.5387867Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.5388633Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.5445924Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth.Services","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.5447286Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.5495870Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.5497445Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth.Services","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.5504063Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.5510457Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.5511514Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.5517082Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.5519986Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.5520779Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.5521051Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.5521653Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth.Services","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.5522482Z","@mt":"开始刷新OpenAPI文档...","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.5526002Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.5527471Z","@mt":"发现 {Count} 个已加载的模块","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.5527840Z","@mt":"文档更新通知: 当前加载了 {ModuleCount} 个模块","ModuleCount":2,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.5528077Z","@mt":"已刷新OpenAPI文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.8837861Z","@mt":"Now listening on: {address}","address":"https://localhost:55344","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.8843632Z","@mt":"Now listening on: {address}","address":"http://localhost:55345","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.9286804Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.9288314Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.9288779Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:41.5602047Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:41.5610638Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:41.5611543Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:41.5618221Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:41.5621506Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:41.5622373Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:41.5622728Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:41.8904130Z","@mt":"初始化模块热加载监控...","@tr":"8ec6991bd0d9d8c0644901e42a624cfe","@sp":"1f6377e9105b80d7","SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNEVVSAAB6K9:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVSAAB6K9","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:41.8925266Z","@mt":"为目录 {Directory} 设置文件监控器","@tr":"8ec6991bd0d9d8c0644901e42a624cfe","@sp":"1f6377e9105b80d7","Directory":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules","SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNEVVSAAB6K9:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVSAAB6K9","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:41.8926134Z","@mt":"模块热加载监控初始化完成，监控 {DirectoryCount} 个目录","@tr":"8ec6991bd0d9d8c0644901e42a624cfe","@sp":"1f6377e9105b80d7","DirectoryCount":1,"SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNEVVSAAB6K9:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVSAAB6K9","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:41.8936809Z","@mt":"初始化动态路由系统...","@tr":"8ec6991bd0d9d8c0644901e42a624cfe","@sp":"1f6377e9105b80d7","SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNEVVSAAB6K9:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVSAAB6K9","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:41.8943667Z","@mt":"发现 {Count} 个模块程序集","@tr":"8ec6991bd0d9d8c0644901e42a624cfe","@sp":"1f6377e9105b80d7","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVVSAAB6K9:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVSAAB6K9","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:41.8952682Z","@mt":"动态路由系统初始化完成，发现 {ModuleCount} 个模块","@tr":"8ec6991bd0d9d8c0644901e42a624cfe","@sp":"1f6377e9105b80d7","ModuleCount":2,"SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNEVVSAAB6K9:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVSAAB6K9","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:41.8965089Z","@mt":"发现 {Count} 个模块程序集","@tr":"8ec6991bd0d9d8c0644901e42a624cfe","@sp":"1f6377e9105b80d7","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVVSAAB6K9:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVSAAB6K9","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:41.8988235Z","@mt":"首次请求触发端点刷新","@tr":"8ec6991bd0d9d8c0644901e42a624cfe","@sp":"1f6377e9105b80d7","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEVVSAAB6K9:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVSAAB6K9","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:41.8994611Z","@mt":"发现 {Count} 个模块程序集","@tr":"8ec6991bd0d9d8c0644901e42a624cfe","@sp":"1f6377e9105b80d7","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVVSAAB6K9:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVSAAB6K9","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:41.8998219Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"8ec6991bd0d9d8c0644901e42a624cfe","@sp":"1f6377e9105b80d7","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVVSAAB6K9:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVSAAB6K9","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:41.8998997Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"8ec6991bd0d9d8c0644901e42a624cfe","@sp":"1f6377e9105b80d7","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVVSAAB6K9:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVSAAB6K9","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:41.8999378Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"8ec6991bd0d9d8c0644901e42a624cfe","@sp":"1f6377e9105b80d7","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVVSAAB6K9:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVSAAB6K9","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:41.9005039Z","@mt":"发现 {Count} 个模块程序集","@tr":"8ec6991bd0d9d8c0644901e42a624cfe","@sp":"1f6377e9105b80d7","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVVSAAB6K9:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVSAAB6K9","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:41.9007876Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"8ec6991bd0d9d8c0644901e42a624cfe","@sp":"1f6377e9105b80d7","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVVSAAB6K9:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVSAAB6K9","MachineName":"LAPTOP-F6SGUTN5","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:41.9008529Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"8ec6991bd0d9d8c0644901e42a624cfe","@sp":"1f6377e9105b80d7","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVVSAAB6K9:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVSAAB6K9","MachineName":"LAPTOP-F6SGUTN5","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:41.9009545Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"8ec6991bd0d9d8c0644901e42a624cfe","@sp":"1f6377e9105b80d7","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVVSAAB6K9:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVSAAB6K9","MachineName":"LAPTOP-F6SGUTN5","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:41.9011048Z","@mt":"请求触发的端点刷新完成，发现 {ModuleCount} 个模块，{EndpointCount} 个端点","@tr":"8ec6991bd0d9d8c0644901e42a624cfe","@sp":"1f6377e9105b80d7","ModuleCount":2,"EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEVVSAAB6K9:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVSAAB6K9","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:41.9119463Z","@mt":"发现 {Count} 个模块程序集","@tr":"4027500375be094b4ecfcfe1216d7f14","@sp":"c9bd6ab86689303e","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVVSAAB6K9:00000002","RequestPath":"/dapr/subscribe","ConnectionId":"0HNEVVSAAB6K9","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:42.0699105Z","@mt":"开始刷新OpenAPI文档...","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:42.0708145Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:42.0711082Z","@mt":"发现 {Count} 个已加载的模块","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:42.0711450Z","@mt":"文档更新通知: 当前加载了 {ModuleCount} 个模块","ModuleCount":2,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:42.0713033Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:42.5815881Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:42.5833797Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:42.5840411Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:42.5841065Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:37:19.1437745Z","@mt":"发现 {Count} 个模块程序集","@tr":"ce229c1227c3776a024bbdfc2986fdc8","@sp":"971e8168b2fdcfc9","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVVSAAB6KB:********","RequestPath":"/favicon.ico","ConnectionId":"0HNEVVSAAB6KB","MachineName":"LAPTOP-F6SGUTN5","ThreadId":28,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:37:19.7113822Z","@mt":"发现 {Count} 个模块程序集","@tr":"0e4ac79c8516e099551c7af79fba6456","@sp":"8b9520e02a98d95d","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVVSAAB6KB:00000003","RequestPath":"/favicon.ico","ConnectionId":"0HNEVVSAAB6KB","MachineName":"LAPTOP-F6SGUTN5","ThreadId":29,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:37:20.3103062Z","@mt":"发现 {Count} 个模块程序集","@tr":"236907b913565353256b5b0d4c91f40a","@sp":"051219cc831a614e","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVVSAAB6KB:00000005","RequestPath":"/favicon.ico","ConnectionId":"0HNEVVSAAB6KB","MachineName":"LAPTOP-F6SGUTN5","ThreadId":28,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:37:20.9256879Z","@mt":"发现 {Count} 个模块程序集","@tr":"bbc70d4651dcbe6b3444fd9f66d0231f","@sp":"9c752986a2676b58","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVVSAAB6KB:00000007","RequestPath":"/favicon.ico","ConnectionId":"0HNEVVSAAB6KB","MachineName":"LAPTOP-F6SGUTN5","ThreadId":30,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:37:21.5283054Z","@mt":"发现 {Count} 个模块程序集","@tr":"3b9b8b11927a324e1076f5d0a8d9f760","@sp":"ce03da4f63c2a719","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVVSAAB6KB:00000009","RequestPath":"/favicon.ico","ConnectionId":"0HNEVVSAAB6KB","MachineName":"LAPTOP-F6SGUTN5","ThreadId":28,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:01.6300753Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.1519684Z","@mt":"动态端点管理器初始化完成","SourceContext":"ApplicationBuilderExtensions","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.1588490Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Controller.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.1598159Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Controller.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.2262229Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth.Controller","AssemblyName":"SSIC.Modules.Auth.Controller, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.2274332Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.2538741Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.2869138Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth.Controller","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.2876122Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3058444Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3060573Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth.Controller","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3089477Z","@mt":"发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3170164Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3182168Z","@mt":"发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3193881Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3195277Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3196499Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth.Controller","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3207190Z","@mt":"开始刷新OpenAPI文档...","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3214579Z","@mt":"发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3217941Z","@mt":"发现 {Count} 个已加载的模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3222375Z","@mt":"文档更新通知: 当前加载了 {ModuleCount} 个模块","ModuleCount":1,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3223104Z","@mt":"已刷新OpenAPI文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3223512Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Services.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3223975Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Services.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3227768Z","@mt":"发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3230791Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3231671Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3233667Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3236920Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3430192Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth.Services","AssemblyName":"SSIC.Modules.Auth.Services, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3432086Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3432607Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3505639Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth.Services","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3507283Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3569878Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3571407Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth.Services","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3577729Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3582772Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3583320Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3588754Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3592738Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3594128Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3594442Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3595282Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth.Services","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3596433Z","@mt":"开始刷新OpenAPI文档...","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3601300Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3603241Z","@mt":"发现 {Count} 个已加载的模块","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3603702Z","@mt":"文档更新通知: 当前加载了 {ModuleCount} 个模块","ModuleCount":2,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.3604043Z","@mt":"已刷新OpenAPI文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.6206688Z","@mt":"Now listening on: {address}","address":"https://localhost:63665","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.6215017Z","@mt":"Now listening on: {address}","address":"http://localhost:63667","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.6954236Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.6956039Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:02.6956390Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:03.3889120Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:03.3901532Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:03.3902910Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:03.3911703Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:03.3917385Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:03.3918233Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:03.3918827Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:03.5933883Z","@mt":"初始化模块热加载监控...","@tr":"32ee9bec0c04c53da25956c7cd8bed52","@sp":"aff48458759f62ba","SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNF002LEJSB5:********","RequestPath":"/dapr/config","ConnectionId":"0HNF002LEJSB5","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:03.5960902Z","@mt":"为目录 {Directory} 设置文件监控器","@tr":"32ee9bec0c04c53da25956c7cd8bed52","@sp":"aff48458759f62ba","Directory":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules","SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNF002LEJSB5:********","RequestPath":"/dapr/config","ConnectionId":"0HNF002LEJSB5","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:03.5962183Z","@mt":"模块热加载监控初始化完成，监控 {DirectoryCount} 个目录","@tr":"32ee9bec0c04c53da25956c7cd8bed52","@sp":"aff48458759f62ba","DirectoryCount":1,"SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNF002LEJSB5:********","RequestPath":"/dapr/config","ConnectionId":"0HNF002LEJSB5","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:03.5974490Z","@mt":"初始化动态路由系统...","@tr":"32ee9bec0c04c53da25956c7cd8bed52","@sp":"aff48458759f62ba","SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNF002LEJSB5:********","RequestPath":"/dapr/config","ConnectionId":"0HNF002LEJSB5","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:03.5983311Z","@mt":"发现 {Count} 个模块程序集","@tr":"32ee9bec0c04c53da25956c7cd8bed52","@sp":"aff48458759f62ba","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNF002LEJSB5:********","RequestPath":"/dapr/config","ConnectionId":"0HNF002LEJSB5","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:03.5996428Z","@mt":"动态路由系统初始化完成，发现 {ModuleCount} 个模块","@tr":"32ee9bec0c04c53da25956c7cd8bed52","@sp":"aff48458759f62ba","ModuleCount":2,"SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNF002LEJSB5:********","RequestPath":"/dapr/config","ConnectionId":"0HNF002LEJSB5","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:03.6012297Z","@mt":"发现 {Count} 个模块程序集","@tr":"32ee9bec0c04c53da25956c7cd8bed52","@sp":"aff48458759f62ba","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNF002LEJSB5:********","RequestPath":"/dapr/config","ConnectionId":"0HNF002LEJSB5","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:03.6042760Z","@mt":"首次请求触发端点刷新","@tr":"32ee9bec0c04c53da25956c7cd8bed52","@sp":"aff48458759f62ba","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNF002LEJSB5:********","RequestPath":"/dapr/config","ConnectionId":"0HNF002LEJSB5","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:03.6051307Z","@mt":"发现 {Count} 个模块程序集","@tr":"32ee9bec0c04c53da25956c7cd8bed52","@sp":"aff48458759f62ba","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNF002LEJSB5:********","RequestPath":"/dapr/config","ConnectionId":"0HNF002LEJSB5","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:03.6055784Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"32ee9bec0c04c53da25956c7cd8bed52","@sp":"aff48458759f62ba","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF002LEJSB5:********","RequestPath":"/dapr/config","ConnectionId":"0HNF002LEJSB5","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:03.6056745Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"32ee9bec0c04c53da25956c7cd8bed52","@sp":"aff48458759f62ba","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF002LEJSB5:********","RequestPath":"/dapr/config","ConnectionId":"0HNF002LEJSB5","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:03.6057203Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"32ee9bec0c04c53da25956c7cd8bed52","@sp":"aff48458759f62ba","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF002LEJSB5:********","RequestPath":"/dapr/config","ConnectionId":"0HNF002LEJSB5","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:03.6065220Z","@mt":"发现 {Count} 个模块程序集","@tr":"32ee9bec0c04c53da25956c7cd8bed52","@sp":"aff48458759f62ba","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNF002LEJSB5:********","RequestPath":"/dapr/config","ConnectionId":"0HNF002LEJSB5","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:03.6068754Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"32ee9bec0c04c53da25956c7cd8bed52","@sp":"aff48458759f62ba","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF002LEJSB5:********","RequestPath":"/dapr/config","ConnectionId":"0HNF002LEJSB5","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:03.6069544Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"32ee9bec0c04c53da25956c7cd8bed52","@sp":"aff48458759f62ba","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF002LEJSB5:********","RequestPath":"/dapr/config","ConnectionId":"0HNF002LEJSB5","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:03.6070160Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"32ee9bec0c04c53da25956c7cd8bed52","@sp":"aff48458759f62ba","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF002LEJSB5:********","RequestPath":"/dapr/config","ConnectionId":"0HNF002LEJSB5","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:03.6071020Z","@mt":"请求触发的端点刷新完成，发现 {ModuleCount} 个模块，{EndpointCount} 个端点","@tr":"32ee9bec0c04c53da25956c7cd8bed52","@sp":"aff48458759f62ba","ModuleCount":2,"EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNF002LEJSB5:********","RequestPath":"/dapr/config","ConnectionId":"0HNF002LEJSB5","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:03.6179141Z","@mt":"发现 {Count} 个模块程序集","@tr":"5e844dca80b534991ed47a80f467bba2","@sp":"bace4efdf330e589","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNF002LEJSB5:00000002","RequestPath":"/dapr/subscribe","ConnectionId":"0HNF002LEJSB5","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:03.8979429Z","@mt":"开始刷新OpenAPI文档...","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:03.8987932Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:03.8991621Z","@mt":"发现 {Count} 个已加载的模块","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:03.8992429Z","@mt":"文档更新通知: 当前加载了 {ModuleCount} 个模块","ModuleCount":2,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:03.8994025Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:04.4087420Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:04.4112600Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:04.4119528Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:46:04.4120132Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.5233083Z","@mt":"动态端点管理器初始化完成","SourceContext":"ApplicationBuilderExtensions","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.5279050Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.5558544Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Controller.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.5568237Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Controller.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.6032168Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth.Controller","AssemblyName":"SSIC.Modules.Auth.Controller, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.6041342Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.6260455Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.7107957Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth.Controller","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.7115735Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.7202418Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.7204211Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth.Controller","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.7232401Z","@mt":"发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.7300119Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.7310429Z","@mt":"发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.7324669Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.7326249Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.7327586Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth.Controller","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.7339313Z","@mt":"开始刷新OpenAPI文档...","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.7346785Z","@mt":"发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.7349919Z","@mt":"发现 {Count} 个已加载的模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.7353255Z","@mt":"文档更新通知: 当前加载了 {ModuleCount} 个模块","ModuleCount":1,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.7353752Z","@mt":"已刷新OpenAPI文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.7354053Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Services.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.7354757Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Services.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.7377203Z","@mt":"发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.7382535Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.7383295Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.7385477Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.7388952Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.7503259Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth.Services","AssemblyName":"SSIC.Modules.Auth.Services, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.8226472Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.8227017Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.8333959Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth.Services","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.8335337Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.8390388Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.8392370Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth.Services","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.8399521Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.8407118Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.8409718Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.8418031Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.8422331Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.8423530Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.8428222Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.8429757Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth.Services","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.8430509Z","@mt":"开始刷新OpenAPI文档...","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.8436529Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.8438799Z","@mt":"发现 {Count} 个已加载的模块","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.8440782Z","@mt":"文档更新通知: 当前加载了 {ModuleCount} 个模块","ModuleCount":2,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.8441587Z","@mt":"已刷新OpenAPI文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:06.1094958Z","@mt":"Now listening on: {address}","address":"https://localhost:50896","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:06.1105242Z","@mt":"Now listening on: {address}","address":"http://localhost:50902","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:06.1543557Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:06.1545045Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:06.1545443Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:06.8491898Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:06.8501098Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:06.8501941Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:06.8507473Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:06.8510431Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:06.8511392Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:06.8512179Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:07.3598551Z","@mt":"开始刷新OpenAPI文档...","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:07.3610199Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:07.3614666Z","@mt":"发现 {Count} 个已加载的模块","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:07.3615414Z","@mt":"文档更新通知: 当前加载了 {ModuleCount} 个模块","ModuleCount":2,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:07.3617296Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:07.8712046Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:07.8741225Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:07.8748233Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:07.8749586Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
