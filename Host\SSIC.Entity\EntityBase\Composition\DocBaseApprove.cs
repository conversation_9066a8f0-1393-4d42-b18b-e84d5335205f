﻿using FreeSql.DataAnnotations;
using SSIC.Entity.EntityBase;
using System;

namespace SSIC.Entity.EntityBase.Composition
{
    public class DocBaseApprove : EntityBasic, IEntityDoc, IEntityApprove
    {
        /// <summary>
        /// 单据号
        /// </summary>
        [Column(StringLength = 50)]
        public string docno { get; set; } //50

        /// <summary>
        /// 单据类型
        /// </summary>
        public int? doctype { get; set; } = 0;//3

        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTime? docdate { get; set; } = DateTime.Now;

        /// <summary>
        /// 岗位id
        /// </summary>
        public Guid? pid { get; set; }

        /// <summary>
        /// 员工ID
        /// </summary>
        public Guid? staffid { get; set; }



        /// <summary>
        /// 审核(0.提交,1审核中,2.反审核,3.已审核,4.结案，5手动结案)
        /// </summary>
        public int? check { get; set; } = 0;

        /// <summary>
        /// 审核人
        /// </summary>
        public Guid? checkid { get; set; }

        /// <summary>
        /// 审核时间
        /// </summary>
        public DateTime? checktime { get; set; }
    }
}