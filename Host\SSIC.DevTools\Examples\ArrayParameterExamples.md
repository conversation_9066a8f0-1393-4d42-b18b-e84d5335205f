# 数组参数使用示例

## 🎯 概述

代码生成器现在支持以数组形式传递服务层和控制器的名字，提供更精确的代码生成控制。

## 🚀 使用方式

### 1. Web界面使用

访问 `http://localhost:5000/codegen`，在生成配置区域：

- **模块名称**: 指定要生成的模块，如 `Auth`
- **服务名称**: 指定要生成的服务，如 `User,Role,Permission`
- **控制器名称**: 指定要生成的控制器，如 `User,Role`

### 2. API调用示例

#### 生成所有模块的所有文件
```bash
curl -X POST http://localhost:5000/api/CodeGenerator/generate-modules \
  -H "Content-Type: application/json" \
  -d '{}'
```

#### 生成指定模块的所有文件
```bash
curl -X POST http://localhost:5000/api/CodeGenerator/generate-modules \
  -H "Content-Type: application/json" \
  -d '{
    "moduleName": "Auth"
  }'
```

#### 生成指定服务和控制器
```bash
curl -X POST http://localhost:5000/api/CodeGenerator/generate-modules \
  -H "Content-Type: application/json" \
  -d '{
    "moduleName": "Auth",
    "serviceNames": ["User", "Role", "Permission"],
    "controllerNames": ["User", "Role"]
  }'
```

#### 只生成服务，不生成控制器
```bash
curl -X POST http://localhost:5000/api/CodeGenerator/generate-modules \
  -H "Content-Type: application/json" \
  -d '{
    "moduleName": "Auth",
    "serviceNames": ["User", "Role", "Permission"],
    "controllerNames": []
  }'
```

#### 只生成控制器，不生成服务
```bash
curl -X POST http://localhost:5000/api/CodeGenerator/generate-modules \
  -H "Content-Type: application/json" \
  -d '{
    "moduleName": "Auth",
    "serviceNames": [],
    "controllerNames": ["User", "Role"]
  }'
```

### 3. 代码调用示例

#### 生成所有模块的所有文件
```csharp
var generator = new CreateModuleProject();
var result = generator.CreateModuleFiles();
```

#### 生成指定模块的所有文件
```csharp
var generator = new CreateModuleProject();
var result = generator.CreateModuleFiles("Auth");
```

#### 生成指定服务和控制器
```csharp
var generator = new CreateModuleProject();
var serviceNames = new[] { "User", "Role", "Permission" };
var controllerNames = new[] { "User", "Role" };
var result = generator.CreateModuleFiles("Auth", serviceNames, controllerNames);
```

#### 只生成服务
```csharp
var generator = new CreateModuleProject();
var serviceNames = new[] { "User", "Role", "Permission" };
var result = generator.CreateModuleFiles("Auth", serviceNames, new string[0]);
```

#### 只生成控制器
```csharp
var generator = new CreateModuleProject();
var controllerNames = new[] { "User", "Role" };
var result = generator.CreateModuleFiles("Auth", new string[0], controllerNames);
```

## 📋 参数说明

### GenerateModulesRequest 参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `moduleName` | string? | 否 | 模块名称，为空则生成所有模块 |
| `serviceNames` | string[]? | 否 | 要生成的服务名称数组，为空则生成所有实体对应的服务 |
| `controllerNames` | string[]? | 否 | 要生成的控制器名称数组，为空则生成所有实体对应的控制器 |

### 参数逻辑

1. **null 或 空数组**: 生成所有对应的文件
2. **包含元素的数组**: 只生成数组中指定的文件
3. **模块名称**: 指定模块时只处理该模块，否则处理所有模块

## 🎯 使用场景

### 场景1: 增量开发
只生成新增的实体对应的服务和控制器：
```json
{
  "moduleName": "Auth",
  "serviceNames": ["NewEntity"],
  "controllerNames": ["NewEntity"]
}
```

### 场景2: 只更新服务层
业务逻辑变更，只重新生成服务层：
```json
{
  "moduleName": "Auth",
  "serviceNames": ["User", "Role"],
  "controllerNames": []
}
```

### 场景3: 只更新控制器
API接口变更，只重新生成控制器：
```json
{
  "moduleName": "Auth",
  "serviceNames": [],
  "controllerNames": ["User", "Role"]
}
```

### 场景4: 分模块开发
团队分工，每个人负责不同的模块：
```json
{
  "moduleName": "Auth"
}
```

### 场景5: 选择性生成
只生成核心实体的代码：
```json
{
  "serviceNames": ["User", "Role", "Permission"],
  "controllerNames": ["User", "Role", "Permission"]
}
```

## 🔍 生成结果示例

### 请求参数
```json
{
  "moduleName": "Auth",
  "serviceNames": ["User", "Role"],
  "controllerNames": ["User"]
}
```

### 生成的文件结构
```
SSIC.Modules.Auth/
├── SSIC.Modules.Auth.Services/
│   ├── Interfaces/
│   │   ├── IUserService.cs          ✅ 生成
│   │   ├── IRoleService.cs          ✅ 生成
│   │   └── Expands/
│   │       ├── IUserService.cs      ✅ 生成
│   │       └── IRoleService.cs      ✅ 生成
│   ├── Implementations/
│   │   ├── UserService.cs           ✅ 生成
│   │   ├── RoleService.cs           ✅ 生成
│   │   └── Expands/
│   │       ├── UserService.cs       ✅ 生成
│   │       └── RoleService.cs       ✅ 生成
│   └── SSIC.Modules.Auth.Services.csproj
├── SSIC.Modules.Auth.Controllers/
│   ├── Controllers/
│   │   ├── UserController.cs        ✅ 生成
│   │   ├── RoleController.cs        ❌ 跳过
│   │   └── Expands/
│   │       ├── UserController.cs    ✅ 生成
│   │       └── RoleController.cs    ❌ 跳过
│   ├── Program.cs
│   └── SSIC.Modules.Auth.Controllers.csproj
└── SSIC.Modules.Auth.sln
```

## 📊 API响应示例

```json
{
  "success": true,
  "message": "模块 Auth 生成完成!",
  "timestamp": "2024-01-01T12:00:00Z",
  "request": {
    "moduleName": "Auth",
    "serviceNames": ["User", "Role"],
    "controllerNames": ["User"],
    "serviceCount": 2,
    "controllerCount": 1
  },
  "generatedStructure": {
    "description": "生成的模块化项目结构",
    "structure": [
      "SSIC.Modules/{ModuleName}/",
      "├── SSIC.Modules.{ModuleName}.Services/",
      "│   ├── Interfaces/",
      "│   │   ├── I{EntityName}Service.cs",
      "│   │   └── Expands/I{EntityName}Service.cs",
      "│   ├── Implementations/",
      "│   │   ├── {EntityName}Service.cs",
      "│   │   └── Expands/{EntityName}Service.cs",
      "│   └── SSIC.Modules.{ModuleName}.Services.csproj",
      "├── SSIC.Modules.{ModuleName}.Controllers/",
      "│   ├── Controllers/",
      "│   │   ├── {EntityName}Controller.cs",
      "│   │   └── Expands/{EntityName}Controller.cs",
      "│   ├── Program.cs",
      "│   └── SSIC.Modules.{ModuleName}.Controllers.csproj",
      "└── SSIC.Modules.{ModuleName}.sln"
    ]
  }
}
```

## 🎉 总结

通过数组参数支持，代码生成器现在提供了：

1. **精确控制**: 可以指定生成哪些服务和控制器
2. **增量开发**: 支持只生成新增或修改的文件
3. **团队协作**: 不同团队成员可以生成不同的模块
4. **灵活配置**: 支持多种生成场景和需求
5. **向后兼容**: 不传参数时保持原有的全量生成行为

这种设计大大提高了代码生成的灵活性和实用性！
