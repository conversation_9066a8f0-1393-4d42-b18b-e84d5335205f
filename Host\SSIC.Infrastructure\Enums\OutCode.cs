﻿namespace SSIC.Infrastructure.Enums
{
    /// <summary>
    /// 状态码
    /// </summary>
    public enum OutCode
    {/// <summary>
     /// 返回成功
     /// </summary>
        Success = 200,

        /// <summary>
        /// 返回失败
        /// </summary>
        Fail = 400,

        /// <summary>
        /// 未授权
        /// </summary>
        NoAuthority = 401,

        /// <summary>
        /// 权限不足
        /// </summary>
        Unauthorized = 403,

        /// <summary>
        /// 无效地址/资源未找到
        /// </summary>
        NoHttp = 404,

        /// <summary>
        /// 资源未找到
        /// </summary>
        NotFound = 404,

        /// <summary>
        /// 数据验证失败
        /// </summary>
        ValidationError = 422,

        /// <summary>
        /// 禁止访问
        /// </summary>
        Forbidden = 403,

        /// <summary>
        /// 服务器错误
        /// </summary>
        ServerError = 500
    }
}