/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *Service提供业务逻辑操作，如果要增加业务逻辑请在当前目录下Expands文件夹PermissionService编写代码
*/
using SSIC.Modules.Auth.Services.Interfaces;
using SSIC.Infrastructure.BaseProvider;
using SSIC.Entity.Auth;
using FreeSql;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SSIC.Modules.Auth.Services.Implementations
{
    /// <summary>
    /// Permission服务实现类（权限管理）
    /// 实现Permission实体的业务逻辑操作
    /// </summary>
    public partial class PermissionService : ServiceBase, IPermissionService
    {
        /// <summary>
        /// 初始化Permission服务
        /// </summary>
        /// <param name="fsql">FreeSql实例</param>
        /// <param name="logger">日志记录器</param>
        public PermissionService(IFreeSql fsql, ILogger<PermissionService> logger) : base(fsql, logger)
        {
        }

        // 在此处添加Permission特定的业务方法实现
    }
}
