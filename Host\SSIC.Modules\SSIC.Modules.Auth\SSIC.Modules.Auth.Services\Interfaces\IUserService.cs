/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *Service提供业务逻辑操作，如果要增加业务逻辑请在当前目录下Expands文件夹IUserService编写接口
 */
using SSIC.Infrastructure.BaseProvider;
using SSIC.Infrastructure.DependencyInjection;
using SSIC.Infrastructure.DependencyInjection.Interface;
using SSIC.Entity.Auth;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SSIC.Modules.Auth.Services.Interfaces
{
    /// <summary>
    /// User服务接口（用户管理）
    /// 提供User实体的业务逻辑操作
    /// </summary>
    public partial interface IUserService : IServiceBase, IScoped
    {
        // 在此处添加User特定的业务方法
    }
}
