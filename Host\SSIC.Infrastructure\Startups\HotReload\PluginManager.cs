﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Mvc.ApplicationParts;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SSIC.Infrastructure.Startups.Endpoints;
using SSIC.Infrastructure.Startups.HotReload;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.IO.MemoryMappedFiles;
using System.Linq;
using System.Reflection;
using System.Runtime.Loader;
using System.Threading;
using System.Threading.Tasks;
using SSIC.Infrastructure.Startups.Extensions;
using SSIC.Infrastructure.Startups.OpenAPI;

namespace SSIC.Infrastructure.Startups.HotReload
{
    /// <summary>
    /// 插件管理器接口
    /// </summary>
    public interface IPluginManager
    {
        /// <summary>
        /// 初始化插件管理器
        /// </summary>
        Task InitializeAsync();

        /// <summary>
        /// 加载插件
        /// </summary>
        Task<Assembly> LoadPluginAsync(string pluginPath);

        /// <summary>
        /// 卸载插件
        /// </summary>
        Task UnloadPluginAsync(string pluginPath);

        /// <summary>
        /// 重新加载插件
        /// </summary>
        Task<Assembly> ReloadPluginAsync(string pluginPath);

        /// <summary>
        /// 获取已加载的插件列表
        /// </summary>
        IEnumerable<Assembly> GetLoadedPlugins();

        /// <summary>
        /// 强制释放模块相关的所有资源，用于确保模块文件夹可以被删除
        /// </summary>
        Task ForceReleaseModuleResourcesAsync(string moduleName);
    }

    /// <summary>
    /// 插件管理器实现
    /// </summary>
    public class PluginManager : IPluginManager, IDisposable
    {
        private readonly ILogger<PluginManager> _logger;
        private readonly HotReloadOptions _options;
        private readonly IServiceProvider _serviceProvider;
        private readonly ApplicationPartManager _partManager;
        private readonly IModuleScanner _moduleScanner;
        private readonly ConcurrentDictionary<string, AssemblyLoadContext> _loadContexts;
        private readonly ConcurrentDictionary<string, Assembly> _loadedPlugins;
        private readonly ConcurrentDictionary<string, MemoryMappedFile> _mappedFiles;
        private readonly ConcurrentDictionary<string, string> _tempFilePaths;
        private readonly string _tempDirectory;
        private readonly ConcurrentBag<string> _pendingDeleteFiles;
        private readonly Timer _cleanupTimer;
        private bool _isDisposed;

        public PluginManager(
            ILogger<PluginManager> logger,
            HotReloadOptions options,
            IServiceProvider serviceProvider,
            ApplicationPartManager partManager,
            IModuleScanner moduleScanner)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _options = options ?? throw new ArgumentNullException(nameof(options));
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _partManager = partManager ?? throw new ArgumentNullException(nameof(partManager));
            _moduleScanner = moduleScanner ?? throw new ArgumentNullException(nameof(moduleScanner));
            _loadContexts = new ConcurrentDictionary<string, AssemblyLoadContext>(StringComparer.OrdinalIgnoreCase);
            _loadedPlugins = new ConcurrentDictionary<string, Assembly>(StringComparer.OrdinalIgnoreCase);
            _mappedFiles = new ConcurrentDictionary<string, MemoryMappedFile>(StringComparer.OrdinalIgnoreCase);
            _tempFilePaths = new ConcurrentDictionary<string, string>(StringComparer.OrdinalIgnoreCase);
            _pendingDeleteFiles = new ConcurrentBag<string>();
            
            // 创建临时目录
            _tempDirectory = Path.Combine(Path.GetTempPath(), "SSIC_Modules_" + Guid.NewGuid().ToString("N"));
            if (!Directory.Exists(_tempDirectory))
            {
                Directory.CreateDirectory(_tempDirectory);
            }
            
            // 创建定时清理任务
            _cleanupTimer = new Timer(CleanupTempFiles, null, TimeSpan.FromMinutes(2), TimeSpan.FromMinutes(5));
        }

        /// <summary>
        /// 初始化插件管理器
        /// </summary>
        public async Task InitializeAsync()
        {
            try
            {
                // 搜索并加载所有模块
                var moduleFiles = await _moduleScanner.ScanModulesAsync();
                _logger.LogInformation("发现 {Count} 个模块文件", moduleFiles.Count);

                foreach (var modulePath in moduleFiles)
                {
                    try
                    {
                        await LoadPluginAsync(modulePath);
                        }
                        catch (Exception ex)
                        {
                        _logger.LogError(ex, "加载模块失败: {Path}", modulePath);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化插件管理器失败");
            }
        }

        /// <summary>
        /// 加载插件
        /// </summary>
        public async Task<Assembly> LoadPluginAsync(string pluginPath)
        {
            try
            {
                var pluginName = Path.GetFileNameWithoutExtension(pluginPath);

                // 检查是否启用重复注册检查
                if (_options.PreventDuplicateRegistration)
                {
                    // 如果插件已经加载，先卸载
                    if (_loadedPlugins.ContainsKey(pluginName))
                    {
                        _logger.LogInformation("检测到模块 {PluginName} 已加载，先执行卸载操作", pluginName);

                        if (_options.ForceUnloadBeforeReload)
                        {
                            await UnloadPluginAsync(pluginPath);

                            // 等待卸载完成
                            await Task.Delay(_options.RetryIntervalMs);
                        }
                    }
                }

                _logger.LogInformation("开始使用AssemblyLoadContext加载插件: {PluginPath}", pluginPath);

                // 简化的程序集加载
                var assembly = LoadAssemblyFromFile(pluginPath);

                // 检查版本跟踪
                if (_options.EnableVersionTracking)
                {
                    var assemblyVersion = assembly.GetName().Version?.ToString() ?? "Unknown";
                    var versionKey = $"{pluginName}_{assemblyVersion}";

                    if (_loadedPlugins.Values.Any(a =>
                        a.GetName().Name == assembly.GetName().Name &&
                        a.GetName().Version?.ToString() == assemblyVersion))
                    {
                        _logger.LogWarning("模块 {PluginName} 版本 {Version} 已存在，跳过加载", pluginName, assemblyVersion);
                        return assembly;
                    }
                }

                // 配置模块服务
                await ConfigureModuleServicesAsync(assembly);

                // 添加到已加载插件字典
                _loadedPlugins[pluginName] = assembly;

                _logger.LogInformation("成功加载插件: {Name}", pluginName);

                // 获取程序集名称
                var assemblyName = assembly.GetName().Name;

                // 尝试重建路由
                try
                {
                    var dynamicEndpointManager = SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointManager.Instance;
                    if (dynamicEndpointManager != null)
                    {
                        // 从已卸载记录中移除当前模块
                        dynamicEndpointManager.RecordModuleLoaded(assemblyName);

                        // 使用RebuildEndpoints方法
                        dynamicEndpointManager.RebuildEndpoints(HotReloadExtensions.GlobalServiceProvider, GetLoadedPlugins());

                        // 使用RefreshEndpoints方法刷新路由
                        dynamicEndpointManager.RefreshEndpoints(HotReloadExtensions.GlobalServiceProvider, GetLoadedPlugins());

                        _logger.LogInformation("已重建路由端点，模块 {AssemblyName} 已加载", assemblyName);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "重建路由端点失败，请稍后手动触发更新");
                }

                // 尝试刷新OpenAPI文档
                try
                {
                    var openApiRefreshService = HotReloadExtensions.GlobalServiceProvider?.GetService<IUnifiedOpenApiRefreshService>();
                    if (openApiRefreshService != null)
                    {
                        await openApiRefreshService.RefreshAsync();
                        _logger.LogInformation("已刷新OpenAPI文档");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "刷新OpenAPI文档失败: {Error}", ex.Message);
                }

                return assembly;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载插件失败: {Path}", pluginPath);
                throw;
            }
        }

        /// <summary>
        /// 使用内存映射文件加载程序集
        /// </summary>
        private async Task<Assembly> LoadAssemblyFromMemoryMappedFileAsync(string filePath)
        {
            // 读取文件内容
            byte[] fileContent;
            using (var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.Read))
            {
                fileContent = new byte[fileStream.Length];
                await fileStream.ReadAsync(fileContent, 0, fileContent.Length);
            }
            
            // 创建内存映射文件
            var mappedFileName = Guid.NewGuid().ToString("N");
            var mappedFile = MemoryMappedFile.CreateNew(mappedFileName, fileContent.Length);
            
            // 将文件内容写入内存映射文件
            using (var accessor = mappedFile.CreateViewAccessor(0, fileContent.Length, MemoryMappedFileAccess.ReadWrite))
            {
                accessor.WriteArray(0, fileContent, 0, fileContent.Length);
            }
            
            // 保存内存映射文件引用
            _mappedFiles[Path.GetFileNameWithoutExtension(filePath)] = mappedFile;
            
            // 创建自定义加载上下文
            var moduleDirectory = Path.GetDirectoryName(filePath) ?? throw new InvalidOperationException($"无法获取模块目录: {filePath}");
            var loadContext = new MemoryMappedAssemblyLoadContext(mappedFile, fileContent.Length, moduleDirectory, _logger);
            
            // 从内存映射文件加载程序集
            var assembly = loadContext.LoadFromMemoryMappedFile();
            
            // 保存加载上下文引用
            _loadContexts[Path.GetFileNameWithoutExtension(filePath)] = loadContext;
            
            return assembly;
        }
  
        /// <summary>
        /// 卸载插件
        /// </summary>
        public async Task UnloadPluginAsync(string pluginPath)
        {
            try
            {
                var pluginName = Path.GetFileNameWithoutExtension(pluginPath);

                // 检查插件是否已加载
                if (!_loadedPlugins.TryGetValue(pluginName, out var assembly))
                {
                    _logger.LogWarning("插件未加载: {Name}", pluginName);
                    return;
                }

                _logger.LogInformation("开始卸载插件: {Name}", pluginName);

                // 获取程序集名称用于后续操作
                var assemblyName = assembly.GetName().Name;

                // 强制释放模块资源
                if (_options.ForceUnloadBeforeReload)
                {
                    _logger.LogInformation("已强制释放模块资源: {Name}", pluginName);
                    await ForceReleaseModuleResourcesAsync(pluginName);
                }

                // 移除程序集部件 - 移除所有相同名称的程序集
                var partsToRemove = _partManager.ApplicationParts
                    .OfType<AssemblyPart>()
                    .Where(p => p.Assembly.GetName().Name == assemblyName)
                    .ToList();

                foreach (var part in partsToRemove)
                {
                    _partManager.ApplicationParts.Remove(part);
                    _logger.LogInformation("已从ApplicationPartManager移除程序集部件: {AssemblyName}", assemblyName);
                }

                if (partsToRemove.Any())
                {
                    _logger.LogInformation("共移除了 {Count} 个程序集部件: {Name}", partsToRemove.Count, pluginName);
                }
                else
                {
                    _logger.LogWarning("未找到要移除的程序集部件: {Name}", pluginName);
                }

                // 先释放内存映射文件
                if (_mappedFiles.TryRemove(pluginName, out var mappedFile))
                {
                    try
                    {
                        mappedFile.Dispose();
                        _logger.LogDebug("已释放内存映射文件: {Name}", pluginName);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "释放内存映射文件失败: {Name}", pluginName);
                    }
                }

                // 移除程序集引用
                _loadedPlugins.TryRemove(pluginName, out _);

                // 卸载程序集加载上下文
                if (_loadContexts.TryRemove(pluginName, out var loadContext))
                {
                    try
                    {
                        loadContext.Unload();
                        _logger.LogDebug("已卸载程序集加载上下文: {Name}", pluginName);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "卸载程序集加载上下文失败: {Name}", pluginName);
                    }
                }

                // 强制垃圾回收，确保资源完全释放
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                // 等待一段时间确保文件句柄完全释放
                await Task.Delay(100);

                _logger.LogInformation("成功卸载插件: {Name}", pluginName);

                // 尝试重建路由
                try
                {
                    var dynamicEndpointManager = SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointManager.Instance;
                    if (dynamicEndpointManager != null)
                    {
                        // 记录已卸载的模块
                        dynamicEndpointManager.RecordUnloadedModule(pluginName);

                        // 使用RebuildEndpoints方法
                        dynamicEndpointManager.RebuildEndpoints(HotReloadExtensions.GlobalServiceProvider, GetLoadedPlugins());

                        // 使用RefreshEndpoints方法刷新路由
                        dynamicEndpointManager.RefreshEndpoints(HotReloadExtensions.GlobalServiceProvider, GetLoadedPlugins());

                        _logger.LogInformation("已重建路由端点");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "重建路由端点失败，请稍后手动触发更新");
                }

                // 尝试刷新OpenAPI文档
                try
                {
                    var openApiRefreshService = HotReloadExtensions.GlobalServiceProvider?.GetService<IUnifiedOpenApiRefreshService>();
                    if (openApiRefreshService != null)
                    {
                        await openApiRefreshService.RefreshAsync();
                        _logger.LogInformation("已刷新OpenAPI文档");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "刷新OpenAPI文档失败: {Error}", ex.Message);
                }

                // 最后再次尝试释放资源，确保文件可以被删除
                await Task.Delay(200); // 额外等待时间
                GC.Collect();
                GC.WaitForPendingFinalizers();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "卸载插件失败: {Path}", pluginPath);
                throw;
            }
        }
        
        /// <summary>
        /// 重新加载插件
        /// </summary>
        public async Task<Assembly> ReloadPluginAsync(string pluginPath)
        {
            var pluginName = Path.GetFileNameWithoutExtension(pluginPath);
            var retryCount = 0;

            while (retryCount < _options.MaxRetryCount)
            {
                try
                {
                    // 如果启用了强制卸载，先执行卸载
                    if (_options.ForceUnloadBeforeReload)
                    {
                        await UnloadPluginAsync(pluginPath);

                        // 等待卸载完成
                        await Task.Delay(_options.RetryIntervalMs);
                    }

                    // 重新加载
                    return await LoadPluginAsync(pluginPath);
                }
                catch (Exception ex)
                {
                    retryCount++;
                    _logger.LogWarning(ex, "重新加载插件 {PluginName} 失败，重试次数: {RetryCount}/{MaxRetry}",
                        pluginName, retryCount, _options.MaxRetryCount);

                    if (retryCount >= _options.MaxRetryCount)
                    {
                        _logger.LogError("重新加载插件 {PluginName} 达到最大重试次数，放弃操作", pluginName);
                        throw;
                    }

                    // 等待后重试
                    await Task.Delay(_options.RetryIntervalMs * retryCount);
                }
            }

            throw new InvalidOperationException($"无法重新加载插件 {pluginName}");
        }

        /// <summary>
        /// 强制释放模块相关的所有资源，用于确保模块文件夹可以被删除
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        public async Task ForceReleaseModuleResourcesAsync(string moduleName)
        {
            try
            {
                _logger.LogInformation("强制释放模块资源: {ModuleName}", moduleName);

                // 卸载插件（如果已加载）
                if (_loadedPlugins.ContainsKey(moduleName))
                {
                    await UnloadPluginAsync(moduleName + ".dll");
                }

                // 确保内存映射文件被释放
                if (_mappedFiles.TryRemove(moduleName, out var mappedFile))
                {
                    try
                    {
                        mappedFile.Dispose();
                        _logger.LogDebug("强制释放内存映射文件: {ModuleName}", moduleName);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "强制释放内存映射文件失败: {ModuleName}", moduleName);
                    }
                }

                // 确保加载上下文被卸载
                if (_loadContexts.TryRemove(moduleName, out var loadContext))
                {
                    try
                    {
                        loadContext.Unload();
                        _logger.LogDebug("强制卸载加载上下文: {ModuleName}", moduleName);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "强制卸载加载上下文失败: {ModuleName}", moduleName);
                    }
                }

                // 多次垃圾回收，确保资源完全释放
                for (int i = 0; i < 3; i++)
                {
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                    await Task.Delay(50);
                }

                _logger.LogInformation("已强制释放模块资源: {ModuleName}", moduleName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "强制释放模块资源失败: {ModuleName}", moduleName);
            }
        }

        /// <summary>
        /// 获取已加载的插件列表
        /// </summary>
        public IEnumerable<Assembly> GetLoadedPlugins()
        {
            return _loadedPlugins.Values;
        }
        
        /// <summary>
        /// 配置模块服务
        /// </summary>
        private async Task ConfigureModuleServicesAsync(Assembly assembly)
        {
            try
            {
                // 不再尝试从GlobalServiceProvider获取IServiceCollection
                // 而是直接创建一个新的ServiceCollection实例
                var services = new ServiceCollection();
                
                // 注册当前ServiceProvider，以便插件可以访问现有服务
                services.AddSingleton(HotReloadExtensions.GlobalServiceProvider);
                
                // 使用自定义方法配置模块服务，避免命名冲突
                services.AddModuleServices(assembly);

                // 构建临时ServiceProvider并使用它
                using (var tempProvider = services.BuildServiceProvider())
                {
                    // 这里可以执行一些使用tempProvider的操作
                    // 例如获取并调用模块的初始化方法等
                }

                _logger.LogInformation("已配置模块服务: {Name}", assembly.GetName().Name);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "配置模块服务失败: {Name}", assembly.GetName().Name);
            }
        }
        
        
        /// <summary>
        /// 定期清理临时文件
        /// </summary>
        private void CleanupTempFiles(object state)
        {
            // 收集所有需要删除的文件
            var filesToDelete = new List<string>();
            while (_pendingDeleteFiles.TryTake(out var filePath))
            {
                filesToDelete.Add(filePath);
            }
            
            if (filesToDelete.Count == 0)
                return;
                
            _logger.LogInformation("开始清理临时文件，共 {Count} 个文件", filesToDelete.Count);
            
            // 尝试删除文件
            foreach (var filePath in filesToDelete)
            {
                try
                {
                    if (File.Exists(filePath))
                    {
                        File.Delete(filePath);
                        _logger.LogDebug("已删除临时文件: {Path}", filePath);
                    }
            }
            catch (Exception ex)
            {
                    // 如果失败，重新添加到待删除队列
                    _pendingDeleteFiles.Add(filePath);
                    _logger.LogWarning(ex, "删除临时文件失败，已重新标记: {Path}", filePath);
                }
            }
        }
        
        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        protected virtual void Dispose(bool disposing)
        {
            if (_isDisposed)
                return;
                
            if (disposing)
            {
                // 停止定时器
                _cleanupTimer?.Dispose();
                
                // 执行最后一次清理
                CleanupTempFiles(null);
                
                // 释放所有内存映射文件
                foreach (var mappedFile in _mappedFiles.Values)
                {
                    try
                    {
                        mappedFile.Dispose();
                    }
                    catch { }
                }
                _mappedFiles.Clear();
                
                // 尝试删除临时目录
                try
                {
                    if (Directory.Exists(_tempDirectory))
                    {
                        Directory.Delete(_tempDirectory, true);
                        _logger.LogInformation("已删除临时目录: {Path}", _tempDirectory);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "删除临时目录失败: {Path}", _tempDirectory);
                }
            }
            
            _isDisposed = true;
        }

        /// <summary>
        /// 简化的程序集加载方法
        /// </summary>
        private Assembly LoadAssemblyFromFile(string filePath)
        {
            try
            {
                // 读取文件字节
                var assemblyBytes = File.ReadAllBytes(filePath);

                // 创建可收集的程序集加载上下文
                var loadContext = new AssemblyLoadContext($"Plugin_{Path.GetFileNameWithoutExtension(filePath)}", isCollectible: true);

                // 从字节数组加载程序集
                var assembly = loadContext.LoadFromStream(new MemoryStream(assemblyBytes));

                // 记录加载上下文
                _loadContexts[filePath] = loadContext;
                _loadedPlugins[filePath] = assembly;

                return assembly;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载程序集失败: {FilePath}", filePath);
                throw;
            }
        }
    }

    /// <summary>
    /// 内存映射文件程序集加载上下文
    /// </summary>
    public class MemoryMappedAssemblyLoadContext : AssemblyLoadContext
    {
        private readonly MemoryMappedFile _mappedFile;
        private readonly long _fileSize;
        private readonly string _moduleDirectory;
        private readonly ILogger _logger;

        public MemoryMappedAssemblyLoadContext(MemoryMappedFile mappedFile, long fileSize, string moduleDirectory, ILogger logger)
            : base(isCollectible: true)
        {
            _mappedFile = mappedFile ?? throw new ArgumentNullException(nameof(mappedFile));
            _fileSize = fileSize;
            _moduleDirectory = moduleDirectory ?? throw new ArgumentNullException(nameof(moduleDirectory));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }
        
        /// <summary>
        /// 从内存映射文件加载程序集
        /// </summary>
        public Assembly LoadFromMemoryMappedFile()
        {
            // 创建内存映射视图
            using (var accessor = _mappedFile.CreateViewAccessor(0, _fileSize, MemoryMappedFileAccess.Read))
            {
                // 创建字节数组
                byte[] assemblyBytes = new byte[_fileSize];
                
                // 读取内存映射文件内容
                accessor.ReadArray(0, assemblyBytes, 0, assemblyBytes.Length);
                
                // 从字节数组加载程序集
                return LoadFromStream(new MemoryStream(assemblyBytes));
            }
        }

        protected override Assembly? Load(AssemblyName assemblyName)
        {
            try
            {
                // 首先尝试从默认上下文加载（系统程序集和已加载的程序集）
                try
                {
                    return Default.LoadFromAssemblyName(assemblyName);
                }
                catch
                {
                    // 如果默认上下文加载失败，尝试从模块目录加载
                }

                // 尝试从模块目录加载依赖程序集
                var assemblyFileName = assemblyName.Name + ".dll";
                var assemblyPath = Path.Combine(_moduleDirectory, assemblyFileName);

                if (File.Exists(assemblyPath))
                {
                    _logger.LogDebug("从模块目录加载依赖程序集: {AssemblyPath}", assemblyPath);
                    return LoadFromAssemblyPath(assemblyPath);
                }

                _logger.LogWarning("无法找到依赖程序集: {AssemblyName}", assemblyName.FullName);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载依赖程序集失败: {AssemblyName}", assemblyName.FullName);
                return null;
            }
        }

        protected override IntPtr LoadUnmanagedDll(string unmanagedDllName)
        {
            // 尝试从模块目录加载非托管DLL
            var dllPath = Path.Combine(_moduleDirectory, unmanagedDllName);
            if (File.Exists(dllPath))
            {
                return LoadUnmanagedDllFromPath(dllPath);
            }

            return IntPtr.Zero;
        }
    }
}