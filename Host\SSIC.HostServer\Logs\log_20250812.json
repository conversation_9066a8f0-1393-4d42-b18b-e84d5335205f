{"@t":"2025-08-12T10:32:46.7690280Z","@mt":"发现 {Count} 个模块文件","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:32:46.9655664Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:32:46.9684949Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:32:47.0654662Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:32:47.0655683Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:32:47.0720449Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:32:47.4844834Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:32:47.4878133Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:32:47.5165602Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:32:47.5177732Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:32:47.5187744Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:32:48.5955953Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:32:48.5983315Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:32:48.5999254Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:32:48.6009394Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:32:48.6025420Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:32:49.1152708Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:32:49.1189071Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:32:49.1222783Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:32:49.1240273Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:34:03.1266280Z","@mt":"发现 {Count} 个模块文件","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:34:03.1627287Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:34:03.1791359Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:34:03.2313159Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:34:03.2328212Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:34:03.3464354Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:34:03.5114373Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:34:03.5188885Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:34:03.5529698Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:34:03.5548245Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:34:03.5559744Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:34:04.6752704Z","@mt":"首次请求触发端点刷新","@tr":"9fbf0732e3e38bc9c4c53155c4ca3a99","@sp":"0e24e04649f38b8a","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEPHF105TT7:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEPHF105TT7","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:34:04.6790959Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"9fbf0732e3e38bc9c4c53155c4ca3a99","@sp":"0e24e04649f38b8a","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEPHF105TT7:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEPHF105TT7","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:34:04.8762273Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:34:04.8791389Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:34:04.8818024Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:34:04.8833196Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:34:04.8857276Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:34:05.0654005Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"0d43e409ed9c8568aa55440696813925","@sp":"4d89c5ef03934175","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEPHF105TT7:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEPHF105TT7","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:34:05.0678516Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"0d43e409ed9c8568aa55440696813925","@sp":"4d89c5ef03934175","Count":1,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEPHF105TT7:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEPHF105TT7","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:34:05.0761012Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"0d43e409ed9c8568aa55440696813925","@sp":"4d89c5ef03934175","PathCount":16,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEPHF105TT7:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEPHF105TT7","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:34:05.3955091Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:34:05.3989109Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:34:05.4007411Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-12T10:34:05.4014525Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
