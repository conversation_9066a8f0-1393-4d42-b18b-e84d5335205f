{"@t":"2025-07-30T01:30:17.9250129Z","@mt":"发现 {Count} 个模块文件","Count":3,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T01:30:17.9331361Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T01:30:17.9593408Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T01:30:17.9689893Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T01:30:17.9700663Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T01:30:17.9990282Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T01:30:18.0039366Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T01:30:18.0054306Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T01:30:18.0084832Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T01:30:18.0117790Z","@mt":"插件已经加载: {Name}","@l":"Warning","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T01:30:18.1262893Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T01:30:18.1295277Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T01:30:18.1306057Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T01:30:18.1321065Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T01:30:19.5326515Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T01:30:19.5344190Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T01:30:19.5366784Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T01:30:19.5374833Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T01:30:19.5385726Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T01:30:20.0447151Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T01:30:20.0505367Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T01:30:20.0539118Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T01:30:20.0549664Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T01:30:20.0560493Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:29.8893998Z","@mt":"开始配置HostBuilder服务...","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:29.9478353Z","@mt":"开始强制加载模块程序集...","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:29.9571964Z","@mt":"发现 {Count} 个模块文件: {Files}","Count":2,"Files":"SSIC.Modules.Auth.dll, SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:29.9787215Z","@mt":"已加载模块程序集: {Assembly}","Assembly":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:29.9900462Z","@mt":"模块 {Assembly} 包含 {Count} 个控制器: {Controllers}","Assembly":"SSIC.Modules.Auth","Count":2,"Controllers":"AuthController, RoleController","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:30.0035493Z","@mt":"已加载模块程序集: {Assembly}","Assembly":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:30.0071699Z","@mt":"模块 {Assembly} 包含 {Count} 个控制器: {Controllers}","Assembly":"SSIC.Modules.Sys","Count":3,"Controllers":"SystemConfigController, SystemLogController, WeatherForecastController","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:30.0743768Z","@mt":"配置ApplicationPartManager...","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:30.0792569Z","@mt":"已添加模块到ApplicationPartManager: {Assembly}","Assembly":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:30.0817234Z","@mt":"已添加模块到ApplicationPartManager: {Assembly}","Assembly":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:30.0855340Z","@mt":"ApplicationPartManager配置完成，共 {Count} 个部件","Count":8,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:53.7857984Z","@mt":"开始配置HostBuilder...","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:53.8211688Z","@mt":"开始配置端点...","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:53.9756894Z","@mt":"已调用 endpoints.MapControllers()","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:53.9821643Z","@mt":"在UseEndpoints中发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:53.9861911Z","@mt":"模块 {Assembly} 包含控制器: {Controllers}","Assembly":"SSIC.Modules.Auth","Controllers":"AuthController, RoleController","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:53.9900587Z","@mt":"模块 {Assembly} 包含控制器: {Controllers}","Assembly":"SSIC.Modules.Sys","Controllers":"SystemConfigController, SystemLogController, WeatherForecastController","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:54.0796804Z","@mt":"开始强化路由刷新机制...","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:54.0838483Z","@mt":"发现 {Count} 个模块程序集: {Modules}","Count":2,"Modules":"SSIC.Modules.Auth, SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:54.0878094Z","@mt":"已强制刷新ActionDescriptorCollectionProvider","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:54.0912825Z","@mt":"发现 {Count} 个模块控制器动作","Count":29,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:54.0950795Z","@mt":"控制器动作: {Controller}.{Action} - 路由: {Route}","Controller":"Auth","Action":"Login","Route":"api/auth/login","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:54.1007079Z","@mt":"控制器动作: {Controller}.{Action} - 路由: {Route}","Controller":"Auth","Action":"Register","Route":"api/auth/register","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:54.1056320Z","@mt":"控制器动作: {Controller}.{Action} - 路由: {Route}","Controller":"Auth","Action":"GetProfile","Route":"api/auth/profile","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:54.1104741Z","@mt":"控制器动作: {Controller}.{Action} - 路由: {Route}","Controller":"Auth","Action":"Logout","Route":"api/auth/logout","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:54.1155873Z","@mt":"控制器动作: {Controller}.{Action} - 路由: {Route}","Controller":"Auth","Action":"ChangePassword","Route":"api/auth/change-password","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:54.1202836Z","@mt":"控制器动作: {Controller}.{Action} - 路由: {Route}","Controller":"Role","Action":"GetRoles","Route":"api/auth/role","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:54.1268303Z","@mt":"控制器动作: {Controller}.{Action} - 路由: {Route}","Controller":"Role","Action":"Test","Route":"api/auth/role/test","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:54.1311141Z","@mt":"控制器动作: {Controller}.{Action} - 路由: {Route}","Controller":"Role","Action":"GetRole","Route":"api/auth/role/{id}","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:54.1361798Z","@mt":"控制器动作: {Controller}.{Action} - 路由: {Route}","Controller":"Role","Action":"CreateRole","Route":"api/auth/role","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:54.1411373Z","@mt":"控制器动作: {Controller}.{Action} - 路由: {Route}","Controller":"Role","Action":"UpdateRole","Route":"api/auth/role/{id}","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:54.1606054Z","@mt":"已刷新DynamicEndpointManager","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:54.1634405Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:54.1658617Z","@mt":"强化路由刷新机制完成","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:54.2088274Z","@mt":"开始同步初始化插件管理器...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:54.2945286Z","@mt":"发现 {Count} 个模块文件","Count":3,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:54.3367664Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:54.3398509Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:54.4175054Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:54.4337972Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:54.4360153Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:54.4444265Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:54.4475790Z","@mt":"插件已经加载: {Name}","@l":"Warning","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:54.4499202Z","@mt":"插件管理器初始化完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:54.4548417Z","@mt":"路由刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:55.0863313Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:55.0955350Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:55.4375098Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:55.4423265Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:55.4450036Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:55.5853107Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:55.5907215Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:55.5945413Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:55.6004273Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:55.6029623Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:56.1057318Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:56.1155764Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:56.1234962Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:56.1274435Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:56.1328961Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:56.5649321Z","@mt":"首次请求触发端点刷新","@tr":"c297cee8d78bfc98aefabce1b8f07b60","@sp":"fedc2324160a5367","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEF3FEV4UMM:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEF3FEV4UMM","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:56.5815706Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"c297cee8d78bfc98aefabce1b8f07b60","@sp":"fedc2324160a5367","Count":4,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEF3FEV4UMM:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEF3FEV4UMM","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:57.2914711Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"cef003764a8d2649d00aed42042b982b","@sp":"a3f6fa38ae242cf0","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEF3FEV4UMM:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEF3FEV4UMM","MachineName":"DESKTOP-O211UJ1","ThreadId":27,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:57.3015112Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"cef003764a8d2649d00aed42042b982b","@sp":"a3f6fa38ae242cf0","Count":4,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEF3FEV4UMM:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEF3FEV4UMM","MachineName":"DESKTOP-O211UJ1","ThreadId":27,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:57.3271650Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"cef003764a8d2649d00aed42042b982b","@sp":"a3f6fa38ae242cf0","PathCount":33,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEF3FEV4UMM:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEF3FEV4UMM","MachineName":"DESKTOP-O211UJ1","ThreadId":27,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:47:57.6523767Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: \r\n\r\nSSIC.Modules.Sys.Controllers.SystemLogController.GetLog (SSIC.Modules.Sys)\r\nSSIC.Modules.Sys.Controllers.SystemConfigController.GetConfig (SSIC.Modules.Sys)\r\n   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)\r\n   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)\r\n   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)\r\n   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)\r\n   at Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware.Invoke(HttpContext context)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"58c2aeafec60cf0c9c38d9cb476bf2a7","@sp":"c8f34d76849aa278","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNEF3FEV4UMM:0000000D","RequestPath":"/favicon.ico","ConnectionId":"0HNEF3FEV4UMM","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:48:01.0997955Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: \r\n\r\nSSIC.Modules.Sys.Controllers.SystemLogController.GetLog (SSIC.Modules.Sys)\r\nSSIC.Modules.Sys.Controllers.SystemConfigController.GetConfig (SSIC.Modules.Sys)\r\n   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)\r\n   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)\r\n   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)\r\n   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)\r\n   at Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware.Invoke(HttpContext context)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"f9b65872a747142924e5e89a85c59b70","@sp":"8b68a1cf95db017d","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNEF3FEV4UMM:0000000F","RequestPath":"/favicon.ico","ConnectionId":"0HNEF3FEV4UMM","MachineName":"DESKTOP-O211UJ1","ThreadId":29,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:48:15.3839204Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Dapr.Client.InvocationException: An exception occurred while invoking method: 'WeatherForecast/Get123/123' on app-id: 'ssic-devtools'\r\n ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:3500)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)\r\n   at Dapr.Client.DaprClientGrpc.InvokeMethodWithResponseAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at Dapr.Client.DaprClientGrpc.InvokeMethodWithResponseAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Dapr.Client.DaprClientGrpc.InvokeMethodAsync[TResponse](HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at SSIC.Infrastructure.Dapr.DaprClientProvider.InvokeServiceWithLoadBalancingAsync[TRequest,TResponse](HttpMethod httpMethod, String appId, String methodName, TRequest data) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Dapr\\DaprClientProvider.cs:line 34\r\n   at SSIC.HostServer.Controllers.WeatherForecast2Controller.Get() in F:\\SSIC\\Host\\SSIC.HostServer\\Controllers\\WeatherForecastController.cs:line 46\r\n   at lambda_method15(Closure, Object)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 94\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"ae671213a9c713a29a38c99a4bd14a7c","@sp":"66c47e153ea9510a","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNEF3FEV4UMM:********","RequestPath":"/WeatherForecast2","ConnectionId":"0HNEF3FEV4UMM","MachineName":"DESKTOP-O211UJ1","ThreadId":28,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:48:32.6287642Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: \r\n\r\nSSIC.Modules.Sys.Controllers.SystemLogController.GetLog (SSIC.Modules.Sys)\r\nSSIC.Modules.Sys.Controllers.SystemConfigController.GetConfig (SSIC.Modules.Sys)\r\n   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)\r\n   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)\r\n   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)\r\n   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)\r\n   at Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware.Invoke(HttpContext context)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"3894546cffb4f5046e25e0829a6b1a0e","@sp":"a4a344e1296b9596","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNEF3FEV4UMM:********","RequestPath":"/favicon.ico","ConnectionId":"0HNEF3FEV4UMM","MachineName":"DESKTOP-O211UJ1","ThreadId":29,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:48:33.8179707Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: \r\n\r\nSSIC.Modules.Sys.Controllers.SystemLogController.GetLog (SSIC.Modules.Sys)\r\nSSIC.Modules.Sys.Controllers.SystemConfigController.GetConfig (SSIC.Modules.Sys)\r\n   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)\r\n   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)\r\n   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)\r\n   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)\r\n   at Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware.Invoke(HttpContext context)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"5dffec207bfe00c7d56899ff161a3fcc","@sp":"1033f05b71ba86a2","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNEF3FEV4UMM:********","RequestPath":"/favicon.ico","ConnectionId":"0HNEF3FEV4UMM","MachineName":"DESKTOP-O211UJ1","ThreadId":29,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:48:34.1101089Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: \r\n\r\nSSIC.Modules.Sys.Controllers.SystemLogController.GetLog (SSIC.Modules.Sys)\r\nSSIC.Modules.Sys.Controllers.SystemConfigController.GetConfig (SSIC.Modules.Sys)\r\n   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)\r\n   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)\r\n   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)\r\n   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)\r\n   at Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware.Invoke(HttpContext context)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"637690a716139f63ee2b26dc89b7e85b","@sp":"2d37d4796f7a2df8","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNEF3FEV4UMM:********","RequestPath":"/favicon.ico","ConnectionId":"0HNEF3FEV4UMM","MachineName":"DESKTOP-O211UJ1","ThreadId":28,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:48:37.4877454Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: \r\n\r\nSSIC.Modules.Sys.Controllers.SystemLogController.GetLog (SSIC.Modules.Sys)\r\nSSIC.Modules.Sys.Controllers.SystemConfigController.GetConfig (SSIC.Modules.Sys)\r\n   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)\r\n   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)\r\n   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)\r\n   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)\r\n   at Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware.Invoke(HttpContext context)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"7603a91a0b4d1e55913cf44c7a22e027","@sp":"c2764643e82def61","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNEF3FEV4UMM:********","RequestPath":"/favicon.ico","ConnectionId":"0HNEF3FEV4UMM","MachineName":"DESKTOP-O211UJ1","ThreadId":27,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:48:38.3005242Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: \r\n\r\nSSIC.Modules.Sys.Controllers.SystemLogController.GetLog (SSIC.Modules.Sys)\r\nSSIC.Modules.Sys.Controllers.SystemConfigController.GetConfig (SSIC.Modules.Sys)\r\n   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)\r\n   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)\r\n   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)\r\n   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)\r\n   at Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware.Invoke(HttpContext context)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"24d7232abbc85f489cd4e977b0d6219e","@sp":"56a1a8b4b29ccc5a","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNEF3FEV4UMM:0000001B","RequestPath":"/favicon.ico","ConnectionId":"0HNEF3FEV4UMM","MachineName":"DESKTOP-O211UJ1","ThreadId":29,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:48:40.7373759Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: \r\n\r\nSSIC.Modules.Sys.Controllers.SystemLogController.GetLog (SSIC.Modules.Sys)\r\nSSIC.Modules.Sys.Controllers.SystemConfigController.GetConfig (SSIC.Modules.Sys)\r\n   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)\r\n   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)\r\n   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)\r\n   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)\r\n   at Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware.Invoke(HttpContext context)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"db816563426e5008096cf91ba66dee1b","@sp":"22d01fcf7ed15086","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNEF3FEV4UMM:0000001D","RequestPath":"/favicon.ico","ConnectionId":"0HNEF3FEV4UMM","MachineName":"DESKTOP-O211UJ1","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:48:42.0836379Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: \r\n\r\nSSIC.Modules.Sys.Controllers.SystemLogController.GetLog (SSIC.Modules.Sys)\r\nSSIC.Modules.Sys.Controllers.SystemConfigController.GetConfig (SSIC.Modules.Sys)\r\n   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)\r\n   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)\r\n   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)\r\n   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)\r\n   at Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware.Invoke(HttpContext context)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"03bfce73bda6a7e659f6c607bd5bc9de","@sp":"07e3780517698a2c","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNEF3FEV4UMM:0000001F","RequestPath":"/favicon.ico","ConnectionId":"0HNEF3FEV4UMM","MachineName":"DESKTOP-O211UJ1","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:48:42.4034626Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: \r\n\r\nSSIC.Modules.Sys.Controllers.SystemLogController.GetLog (SSIC.Modules.Sys)\r\nSSIC.Modules.Sys.Controllers.SystemConfigController.GetConfig (SSIC.Modules.Sys)\r\n   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)\r\n   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)\r\n   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)\r\n   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)\r\n   at Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware.Invoke(HttpContext context)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"1e3bb5b1fa07649d5292c06571673348","@sp":"34ad7ab6e4b0fbeb","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNEF3FEV4UMM:********","RequestPath":"/favicon.ico","ConnectionId":"0HNEF3FEV4UMM","MachineName":"DESKTOP-O211UJ1","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:50:43.0317915Z","@mt":"发现 {Count} 个模块文件","Count":3,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:50:43.0814073Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:50:43.1157803Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:50:43.1855351Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:50:43.1904175Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:50:43.3507145Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:50:43.3880473Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:50:43.3930875Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:50:43.4008762Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:50:43.4146761Z","@mt":"插件已经加载: {Name}","@l":"Warning","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:50:43.9543373Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:50:43.9612504Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:50:44.1008890Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:50:44.1065227Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:50:44.2267504Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:50:45.1706722Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:50:45.1749373Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:50:45.1792403Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:50:45.1822551Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:50:45.1850325Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:50:45.6920963Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:50:45.6991653Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:50:45.7038966Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:50:45.7064344Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:50:45.7110563Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:50:50.9027744Z","@mt":"首次请求触发端点刷新","@tr":"d4205176cbaa91471874f636d2af1b73","@sp":"f5fcb7bd9e735ce5","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEF3H1A3OSQ:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEF3H1A3OSQ","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:50:50.9195580Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"d4205176cbaa91471874f636d2af1b73","@sp":"f5fcb7bd9e735ce5","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEF3H1A3OSQ:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEF3H1A3OSQ","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:50:51.6176455Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"249512e7b6a0c09daf4843ca32db175f","@sp":"ef140bec007bca1e","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEF3H1A3OSQ:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEF3H1A3OSQ","MachineName":"DESKTOP-O211UJ1","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:50:51.6374231Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"249512e7b6a0c09daf4843ca32db175f","@sp":"ef140bec007bca1e","Count":2,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEF3H1A3OSQ:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEF3H1A3OSQ","MachineName":"DESKTOP-O211UJ1","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-30T03:50:51.6740536Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"249512e7b6a0c09daf4843ca32db175f","@sp":"ef140bec007bca1e","PathCount":33,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEF3H1A3OSQ:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEF3H1A3OSQ","MachineName":"DESKTOP-O211UJ1","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
