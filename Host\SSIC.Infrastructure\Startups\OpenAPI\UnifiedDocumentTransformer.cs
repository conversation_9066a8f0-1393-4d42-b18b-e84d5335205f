using Microsoft.AspNetCore.OpenApi;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi;
using SSIC.Infrastructure.Startups.Common;
using SSIC.Infrastructure.Startups.ModuleManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;

namespace SSIC.Infrastructure.Startups.OpenAPI
{
    /// <summary>
    /// 统一的OpenAPI文档转换器
    /// 合并了DynamicModuleDocumentTransformer和HotReloadOpenApiDocumentTransformer的功能
    /// </summary>
    public class UnifiedDocumentTransformer : IOpenApiDocumentTransformer
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<UnifiedDocumentTransformer> _logger;
        private static readonly HashSet<string> _processedPaths = new HashSet<string>();

        public UnifiedDocumentTransformer(
            IServiceProvider serviceProvider,
            ILogger<UnifiedDocumentTransformer> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        public async Task TransformAsync(Microsoft.OpenApi.Models.OpenApiDocument document, OpenApiDocumentTransformerContext context, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("开始统一OpenAPI文档转换...");

                // 清除之前处理的路径记录
                _processedPaths.Clear();

                // 获取活跃的模块程序集
                var activeModules = ModuleUtilities.GetModuleAssemblies();
                _logger.LogInformation("发现 {Count} 个活跃模块程序集", activeModules.Count);

                // 清除现有的动态模块路径（保留系统路径）
                OpenApiUtilities.ClearDynamicModulePaths(document);

                // 初始化标签集合
                var moduleControllerTags = new Dictionary<string, HashSet<string>>();

                // 处理每个模块程序集
                foreach (var assembly in activeModules)
                {
                    await ProcessModuleAssembly(document, assembly, moduleControllerTags, cancellationToken);
                }

                // 更新文档信息
                UpdateDocumentInfo(document, moduleControllerTags);

                // 添加模块标签
                AddModuleTags(document, moduleControllerTags);

                // 添加热重载扩展信息
                AddHotReloadExtension(document, moduleControllerTags);

                _logger.LogInformation("统一OpenAPI文档转换完成，包含 {ModuleCount} 个模块", moduleControllerTags.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "统一OpenAPI文档转换时发生错误");
            }
        }

        /// <summary>
        /// 处理模块程序集
        /// </summary>
        private async Task ProcessModuleAssembly(Microsoft.OpenApi.Models.OpenApiDocument document, Assembly assembly,
            Dictionary<string, HashSet<string>> moduleControllerTags, CancellationToken cancellationToken)
        {
            try
            {
                var assemblyName = assembly.GetName().Name;
                var moduleName = ModuleUtilities.ExtractModuleName(assemblyName);

                if (string.IsNullOrEmpty(moduleName))
                {
                    _logger.LogWarning("无法从程序集 {AssemblyName} 提取模块名称", assemblyName);
                    return;
                }

                _logger.LogDebug("处理模块程序集: {AssemblyName} -> {ModuleName}", assemblyName, moduleName);

                // 初始化模块标签集合
                if (!moduleControllerTags.ContainsKey(moduleName))
                {
                    moduleControllerTags[moduleName] = new HashSet<string>();
                }

                // 获取所有控制器类型
                var controllerTypes = assembly.GetTypes()
                    .Where(ModuleUtilities.IsControllerType)
                    .ToList();

                _logger.LogDebug("在模块 {ModuleName} 中发现 {Count} 个控制器", moduleName, controllerTypes.Count);

                // 处理每个控制器
                foreach (var controllerType in controllerTypes)
                {
                    await ProcessController(document, controllerType, moduleName, moduleControllerTags[moduleName], cancellationToken);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理模块程序集 {AssemblyName} 时出错", assembly.GetName().Name);
            }
        }

        /// <summary>
        /// 处理控制器
        /// </summary>
        private async Task ProcessController(Microsoft.OpenApi.Models.OpenApiDocument document, Type controllerType, string moduleName,
            HashSet<string> controllerTags, CancellationToken cancellationToken)
        {
            try
            {
                var controllerName = controllerType.Name.Replace("Controller", "");
                controllerTags.Add(controllerName);

                _logger.LogDebug("处理控制器: {ControllerType} -> {ControllerName}", controllerType.Name, controllerName);

                // 构建控制器路由
                var controllerRoute = $"api/{moduleName.ToLower()}/{controllerName.ToLower()}";

                // 获取所有公共动作方法
                var actionMethods = controllerType.GetMethods(BindingFlags.Public | BindingFlags.Instance)
                    .Where(m => m.IsPublic && 
                               !m.IsSpecialName && 
                               m.DeclaringType == controllerType &&
                               ModuleUtilities.IsActionMethod(m))
                    .ToList();

                foreach (var method in actionMethods)
                {
                    await ProcessAction(document, method, controllerRoute, moduleName, controllerName, cancellationToken);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理控制器 {ControllerType} 时出错", controllerType.Name);
            }
        }

        /// <summary>
        /// 处理动作方法
        /// </summary>
        private async Task ProcessAction(Microsoft.OpenApi.Models.OpenApiDocument document, MethodInfo method, string controllerRoute,
            string moduleName, string controllerName, CancellationToken cancellationToken)
        {
            try
            {
                // 使用与Swagger相同的路由构建逻辑
                var routeInfo = ModuleUtilities.GetMethodRouteInfo(method, method.Name);
                var routeTemplate = $"{controllerRoute.TrimEnd('/')}/{routeInfo.Template.ToLowerInvariant()}";

                _logger.LogDebug("处理动作: {MethodName} -> {Route} [{HttpMethods}]",
                    method.Name, routeTemplate, string.Join(",", routeInfo.HttpMethods));

                foreach (var httpMethod in routeInfo.HttpMethods)
                {
                    await AddOperationToDocument(document, routeTemplate, httpMethod, method, moduleName, controllerName, cancellationToken);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理动作方法 {MethodName} 时出错", method.Name);
            }
        }

        /// <summary>
        /// 添加操作到文档
        /// </summary>
        private async Task AddOperationToDocument(Microsoft.OpenApi.Models.OpenApiDocument document, string routeTemplate, string httpMethod,
            MethodInfo method, string moduleName, string controllerName, CancellationToken cancellationToken)
        {
            try
            {
                var fullPath = $"/{routeTemplate}";
                var pathKey = $"{fullPath}:{httpMethod}";

                // 检查是否已经处理过这个路径和方法的组合
                if (_processedPaths.Contains(pathKey))
                {
                    _logger.LogDebug("跳过重复路径: {HttpMethod} {Route}", httpMethod, routeTemplate);
                    return;
                }

                // 记录已处理的路径
                _processedPaths.Add(pathKey);

                // 确保路径存在
                if (!document.Paths.ContainsKey(fullPath))
                {
                    document.Paths[fullPath] = new Microsoft.OpenApi.Models.OpenApiPathItem();
                }

                // 创建操作
                var operation = OpenApiUtilities.CreateOpenApiOperation(method, moduleName, controllerName);

                // 根据HTTP方法添加操作
                switch (httpMethod.ToUpper())
                {
                    case "GET":
                        document.Paths[fullPath].Operations[Microsoft.OpenApi.Models.OperationType.Get] = operation;
                        break;
                    case "POST":
                        document.Paths[fullPath].Operations[Microsoft.OpenApi.Models.OperationType.Post] = operation;
                        break;
                    case "PUT":
                        document.Paths[fullPath].Operations[Microsoft.OpenApi.Models.OperationType.Put] = operation;
                        break;
                    case "DELETE":
                        document.Paths[fullPath].Operations[Microsoft.OpenApi.Models.OperationType.Delete] = operation;
                        break;
                    case "PATCH":
                        document.Paths[fullPath].Operations[Microsoft.OpenApi.Models.OperationType.Patch] = operation;
                        break;
                    default:
                        _logger.LogWarning("不支持的HTTP方法: {HttpMethod}", httpMethod);
                        break;
                }

                _logger.LogDebug("已添加操作: {HttpMethod} {Route}", httpMethod, routeTemplate);

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加操作到文档时出错: {HttpMethod} {Route}", httpMethod, routeTemplate);
            }
        }

        /// <summary>
        /// 更新文档信息
        /// </summary>
        private void UpdateDocumentInfo(Microsoft.OpenApi.Models.OpenApiDocument document, Dictionary<string, HashSet<string>> moduleControllerTags)
        {
            document.Info.Title = "SSIC 统一模块化API";
            document.Info.Description = $"包含 {moduleControllerTags.Count} 个动态加载模块的API文档，支持热重载";
            document.Info.Version = "v1.0";
        }

        /// <summary>
        /// 添加模块标签
        /// </summary>
        private void AddModuleTags(Microsoft.OpenApi.Models.OpenApiDocument document, Dictionary<string, HashSet<string>> moduleControllerTags)
        {
            foreach (var moduleGroup in moduleControllerTags)
            {
                OpenApiUtilities.AddModuleTag(document, moduleGroup.Key, moduleGroup.Value.Count);
            }
        }

        /// <summary>
        /// 添加热重载扩展信息
        /// </summary>
        private void AddHotReloadExtension(Microsoft.OpenApi.Models.OpenApiDocument document, Dictionary<string, HashSet<string>> moduleControllerTags)
        {
            try
            {
                var hotReloadInfo = new Microsoft.OpenApi.Any.OpenApiObject
                {
                    ["enabled"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                    ["lastUpdate"] = new Microsoft.OpenApi.Any.OpenApiString(DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ")),
                    ["moduleCount"] = new Microsoft.OpenApi.Any.OpenApiInteger(moduleControllerTags.Count),
                    ["modules"] = new Microsoft.OpenApi.Any.OpenApiArray()
                };

                var modulesArray = (Microsoft.OpenApi.Any.OpenApiArray)hotReloadInfo["modules"];
                foreach (var moduleGroup in moduleControllerTags)
                {
                    var moduleInfo = new Microsoft.OpenApi.Any.OpenApiObject
                    {
                        ["name"] = new Microsoft.OpenApi.Any.OpenApiString(moduleGroup.Key),
                        ["controllerCount"] = new Microsoft.OpenApi.Any.OpenApiInteger(moduleGroup.Value.Count)
                    };
                    modulesArray.Add(moduleInfo);
                }

                document.Extensions["x-hot-reload"] = hotReloadInfo;

                _logger.LogDebug("已添加热重载扩展信息");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加热重载扩展信息时出错");
            }
        }
    }
}
