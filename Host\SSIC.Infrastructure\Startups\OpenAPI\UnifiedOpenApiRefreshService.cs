using Microsoft.AspNetCore.OpenApi;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using SSIC.Infrastructure.Startups.ModuleManager;
using Swashbuckle.AspNetCore.SwaggerGen;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace SSIC.Infrastructure.Startups.OpenAPI
{
    /// <summary>
    /// 统一的OpenAPI刷新服务实现
    /// 合并了HotReloadOpenApiRefreshService和OpenApiRefreshService的功能
    /// </summary>
    public class UnifiedOpenApiRefreshService : IUnifiedOpenApiRefreshService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<UnifiedOpenApiRefreshService> _logger;

        public UnifiedOpenApiRefreshService(
            IServiceProvider serviceProvider,
            ILogger<UnifiedOpenApiRefreshService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        /// <summary>
        /// 刷新OpenAPI文档
        /// </summary>
        public async Task RefreshAsync()
        {
            try
            {
                _logger.LogInformation("开始刷新OpenAPI文档...");

                // 使用服务定位器模式获取模块发现服务（避免生命周期问题）
                using var scope = _serviceProvider.CreateScope();
                var moduleDiscoveryService = scope.ServiceProvider.GetService<IModuleDiscoveryService>();
                if (moduleDiscoveryService != null)
                {
                    var modules = moduleDiscoveryService.DiscoverLoadedModules();
                    _logger.LogInformation("发现 {Count} 个模块程序集", modules.Count());

                    // 过滤掉已卸载的模块
                    var activeModules = FilterActiveModules(modules);
                    _logger.LogInformation("过滤后发现 {Count} 个活跃模块程序集", activeModules.Count());

                    // 实际刷新 OpenAPI 文档生成器
                    await RefreshOpenApiDocuments(scope, activeModules);

                    await NotifyDocumentUpdate(activeModules.Count());
                }
                else
                {
                    _logger.LogWarning("无法获取模块发现服务");
                }

                _logger.LogInformation("已刷新OpenAPI文档");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新OpenAPI文档时发生错误");
            }
        }

        /// <summary>
        /// 过滤活跃的模块（排除已卸载的模块）
        /// </summary>
        private IEnumerable<ModuleManager.ModuleInfo> FilterActiveModules(IEnumerable<ModuleManager.ModuleInfo> modules)
        {
            try
            {
                var dynamicEndpointManager = SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointManager.Instance;
                if (dynamicEndpointManager == null)
                {
                    return modules;
                }

                var activeModules = modules.Where(module =>
                {
                    var assemblyName = module.Assembly.GetName().Name;
                    var isUnloaded = dynamicEndpointManager.IsModuleUnloaded(assemblyName);

                    if (isUnloaded)
                    {
                        _logger.LogInformation("跳过已卸载的模块: {ModuleName}", assemblyName);
                        return false;
                    }

                    return true;
                }).ToList();

                return activeModules;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "过滤活跃模块时出错");
                return modules;
            }
        }

        /// <summary>
        /// 实际刷新 OpenAPI 文档生成器
        /// </summary>
        private async Task RefreshOpenApiDocuments(IServiceScope scope, IEnumerable<ModuleManager.ModuleInfo> modules)
        {
            try
            {
                // 获取 SwaggerGen 服务并刷新
                var swaggerGenOptions = scope.ServiceProvider.GetService<IConfigureOptions<SwaggerGenOptions>>();
                if (swaggerGenOptions != null)
                {
                    _logger.LogDebug("正在刷新 SwaggerGen 配置...");

                    // 为每个模块添加 XML 注释文档
                    foreach (var module in modules)
                    {
                        await AddModuleXmlComments(module);
                    }
                }

                // 强制刷新 OpenAPI 组件
                var openApiOptions = scope.ServiceProvider.GetService<IConfigureOptions<OpenApiOptions>>();
                if (openApiOptions != null)
                {
                    _logger.LogDebug("正在刷新 OpenAPI 配置...");
                }

                _logger.LogInformation("初始化完成，已刷新OpenAPI组件");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新OpenAPI文档生成器时出错");
            }
        }

        /// <summary>
        /// 为模块添加 XML 注释文档
        /// </summary>
        private async Task AddModuleXmlComments(ModuleManager.ModuleInfo module)
        {
            try
            {
                var assemblyLocation = module.Assembly.Location;
                if (!string.IsNullOrEmpty(assemblyLocation))
                {
                    var xmlPath = Path.ChangeExtension(assemblyLocation, ".xml");
                    if (File.Exists(xmlPath))
                    {
                        _logger.LogDebug("发现模块XML文档: {XmlPath}", xmlPath);
                        // 这里可以动态添加 XML 注释，但需要重新配置 SwaggerGen
                    }
                }
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "处理模块 {ModuleName} 的XML注释时出错", module.Name);
            }
        }

        /// <summary>
        /// 获取模块信息
        /// </summary>
        public async Task<ModuleInfo[]> GetModuleInfoAsync()
        {
            try
            {
                _logger.LogDebug("获取模块信息...");

                // 使用服务定位器模式获取模块发现服务（避免生命周期问题）
                using var scope = _serviceProvider.CreateScope();
                var moduleDiscoveryService = scope.ServiceProvider.GetService<IModuleDiscoveryService>();
                if (moduleDiscoveryService != null)
                {
                    var modules = moduleDiscoveryService.DiscoverLoadedModules()
                        .Select(m => new ModuleInfo
                        {
                            Name = m.Name,
                            AssemblyName = m.AssemblyName,
                            ControllerCount = m.Controllers.Count,
                            ActionCount = m.Controllers.Sum(c => c.Actions.Count),
                            LoadTime = m.LoadTime,
                            Controllers = m.Controllers.Select(c => c.Name).ToArray()
                        })
                        .ToArray();

                    _logger.LogDebug("获取到 {Count} 个模块信息", modules.Length);
                    return modules;
                }

                _logger.LogWarning("无法获取模块发现服务");
                return Array.Empty<ModuleInfo>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取模块信息时发生错误");
                return Array.Empty<ModuleInfo>();
            }
        }

        /// <summary>
        /// 通知文档更新
        /// </summary>
        private async Task NotifyDocumentUpdate(int moduleCount)
        {
            try
            {
                _logger.LogInformation("文档更新通知: 当前加载了 {ModuleCount} 个模块", moduleCount);
                
                // 这里可以添加实际的通知逻辑，比如：
                // 1. 发送WebSocket消息
                // 2. 更新缓存
                // 3. 触发事件
                
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送文档更新通知时出错");
            }
        }
    }

    /// <summary>
    /// 简单的OpenAPI刷新服务实现
    /// </summary>
    public class SimpleOpenApiRefreshService : ISimpleOpenApiRefreshService
    {
        private readonly IUnifiedOpenApiRefreshService _unifiedService;
        private readonly ILogger<SimpleOpenApiRefreshService> _logger;

        public SimpleOpenApiRefreshService(
            IUnifiedOpenApiRefreshService unifiedService,
            ILogger<SimpleOpenApiRefreshService> logger)
        {
            _unifiedService = unifiedService;
            _logger = logger;
        }

        /// <summary>
        /// 简单刷新
        /// </summary>
        public async Task RefreshAsync()
        {
            try
            {
                _logger.LogDebug("执行简单OpenAPI刷新...");
                await _unifiedService.RefreshAsync();
                _logger.LogDebug("简单OpenAPI刷新完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "简单OpenAPI刷新时发生错误");
            }
        }
    }
}
