using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace SSIC.Infrastructure.Startups.Common
{
    /// <summary>
    /// 模块相关的公共工具类
    /// 统一处理模块名称提取、HTTP方法提取、控制器类型判断等公共逻辑
    /// </summary>
    public static class ModuleUtilities
    {
        /// <summary>
        /// 从程序集名称中提取模块名称
        /// 支持新的项目结构：SSIC.Modules.Auth.Controller -> Auth
        /// 支持旧的项目结构：SSIC.Modules.Auth -> Auth
        /// </summary>
        /// <param name="assemblyName">程序集名称</param>
        /// <returns>模块名称，如果无法提取则返回null</returns>
        public static string? ExtractModuleName(string? assemblyName)
        {
            if (string.IsNullOrEmpty(assemblyName))
                return null;

            // 处理新的项目结构：SSIC.Modules.Auth.Controller
            if (assemblyName.StartsWith("SSIC.Modules.") && assemblyName.EndsWith(".Controller"))
            {
                var moduleName = assemblyName.Replace("SSIC.Modules.", "").Replace(".Controller", "");
                return moduleName;
            }

            // 处理旧的项目结构：SSIC.Modules.Auth
            if (assemblyName.StartsWith("SSIC.Modules."))
            {
                var moduleName = assemblyName.Replace("SSIC.Modules.", "");
                // 如果包含其他后缀（如.Services），去掉它们
                var dotIndex = moduleName.IndexOf('.');
                if (dotIndex > 0)
                {
                    moduleName = moduleName.Substring(0, dotIndex);
                }
                return moduleName;
            }

            return null;
        }

        /// <summary>
        /// 从特性名称提取HTTP方法
        /// </summary>
        /// <param name="attributeName">特性名称</param>
        /// <returns>HTTP方法名称</returns>
        public static string ExtractHttpMethod(string attributeName)
        {
            return attributeName switch
            {
                "HttpGetAttribute" => "GET",
                "HttpPostAttribute" => "POST",
                "HttpPutAttribute" => "PUT",
                "HttpDeleteAttribute" => "DELETE",
                "HttpPatchAttribute" => "PATCH",
                "HttpHeadAttribute" => "HEAD",
                "HttpOptionsAttribute" => "OPTIONS",
                _ => ""
            };
        }

        /// <summary>
        /// 判断类型是否为控制器类型
        /// </summary>
        /// <param name="type">要检查的类型</param>
        /// <returns>如果是控制器类型返回true</returns>
        public static bool IsControllerType(Type type)
        {
            return !type.IsAbstract &&
                   type.IsPublic &&
                   (type.Name.EndsWith("Controller") ||
                    type.IsSubclassOf(typeof(ControllerBase)) ||
                    type.GetCustomAttributes<ControllerAttribute>().Any());
        }

        /// <summary>
        /// 从控制器类型名称中提取控制器名称（去掉Controller后缀）
        /// </summary>
        /// <param name="controllerTypeName">控制器类型名称</param>
        /// <returns>控制器名称</returns>
        public static string ExtractControllerName(string controllerTypeName)
        {
            if (controllerTypeName.EndsWith("Controller", StringComparison.OrdinalIgnoreCase))
            {
                return controllerTypeName.Substring(0, controllerTypeName.Length - 10);
            }
            return controllerTypeName;
        }

        /// <summary>
        /// 判断方法是否为动作方法
        /// </summary>
        /// <param name="method">要检查的方法</param>
        /// <returns>如果是动作方法返回true</returns>
        public static bool IsActionMethod(MethodInfo method)
        {
            // 排除基类方法
            if (IsBaseControllerMethod(method))
                return false;

            // 如果有HTTP动词特性，则是动作方法
            if (HasHttpMethodAttribute(method))
                return true;

            // 检查常见的动作方法名称
            var commonActionNames = new[] { 
                "Get", "Post", "Put", "Delete", "Patch", 
                "Head", "Options", "Index", "Create", "Edit", 
                "Update", "Remove", "Query", "Find" 
            };

            return commonActionNames.Any(prefix => 
                method.Name.StartsWith(prefix, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 判断方法是否为基类控制器方法
        /// </summary>
        /// <param name="method">要检查的方法</param>
        /// <returns>如果是基类方法返回true</returns>
        public static bool IsBaseControllerMethod(MethodInfo method)
        {
            var declaringType = method.DeclaringType;
            return declaringType != null && 
                   (declaringType == typeof(Controller) || 
                    declaringType == typeof(ControllerBase) ||
                    declaringType.IsAssignableFrom(typeof(Controller)) ||
                    declaringType.IsAssignableFrom(typeof(ControllerBase)));
        }

        /// <summary>
        /// 判断方法是否有HTTP方法特性
        /// </summary>
        /// <param name="method">要检查的方法</param>
        /// <returns>如果有HTTP方法特性返回true</returns>
        public static bool HasHttpMethodAttribute(MethodInfo method)
        {
            return method.GetCustomAttributes()
                .Any(attr => attr.GetType().Name.StartsWith("Http") && 
                           attr.GetType().Name.EndsWith("Attribute"));
        }

        /// <summary>
        /// 获取方法的HTTP方法列表
        /// </summary>
        /// <param name="method">要检查的方法</param>
        /// <returns>HTTP方法列表</returns>
        public static List<string> GetHttpMethods(MethodInfo method)
        {
            var httpMethods = new List<string>();
            
            var httpMethodAttributes = method.GetCustomAttributes()
                .Where(a => a.GetType().Name.StartsWith("Http") && 
                          a.GetType().Name.EndsWith("Attribute"))
                .ToList();

            foreach (var attr in httpMethodAttributes)
            {
                var httpMethod = ExtractHttpMethod(attr.GetType().Name);
                if (!string.IsNullOrEmpty(httpMethod))
                {
                    httpMethods.Add(httpMethod);
                }
            }

            return httpMethods.Any() ? httpMethods : new List<string> { "GET", "POST" };
        }

        /// <summary>
        /// 获取所有模块程序集（排除已卸载的模块）
        /// </summary>
        /// <returns>模块程序集列表</returns>
        public static List<Assembly> GetModuleAssemblies()
        {
            var allModuleAssemblies = AppDomain.CurrentDomain.GetAssemblies()
                .Where(a => a.GetName().Name?.StartsWith("SSIC.Modules.") == true)
                .ToList();

            // 过滤掉已卸载的模块
            var dynamicEndpointManager = SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointManager.Instance;
            if (dynamicEndpointManager != null)
            {
                return allModuleAssemblies.Where(assembly =>
                {
                    var assemblyName = assembly.GetName().Name;
                    return !dynamicEndpointManager.IsModuleUnloaded(assemblyName);
                }).ToList();
            }

            return allModuleAssemblies;
        }

        /// <summary>
        /// 路由信息类
        /// </summary>
        public class ActionRouteInfo
        {
            public string Template { get; set; } = "";
            public List<string> HttpMethods { get; set; } = new List<string>();
        }

        /// <summary>
        /// 获取方法的路由信息
        /// </summary>
        /// <param name="method">方法信息</param>
        /// <param name="actionName">动作名称</param>
        /// <returns>路由信息</returns>
        public static ActionRouteInfo GetMethodRouteInfo(MethodInfo method, string actionName)
        {
            var routeInfo = new ActionRouteInfo();

            // 获取HTTP方法特性
            var httpMethodAttributes = method.GetCustomAttributes()
                .Where(a => a.GetType().Name.StartsWith("Http") && 
                          a.GetType().Name.EndsWith("Attribute"))
                .ToList();

            if (httpMethodAttributes.Any())
            {
                // 有HTTP特性，提取HTTP方法和路由模板
                foreach (var attr in httpMethodAttributes)
                {
                    // 提取HTTP方法
                    var httpMethod = ExtractHttpMethod(attr.GetType().Name);
                    if (!string.IsNullOrEmpty(httpMethod))
                    {
                        routeInfo.HttpMethods.Add(httpMethod);
                    }

                    // 尝试获取路由模板（如 [HttpGet("list")] 中的 "list"）
                    var templateProperty = attr.GetType().GetProperty("Template");
                    if (templateProperty != null)
                    {
                        var template = templateProperty.GetValue(attr) as string;
                        if (!string.IsNullOrEmpty(template))
                        {
                            // 如果有模板（如 "list"），直接使用
                            routeInfo.Template = template;
                            return routeInfo;
                        }
                    }
                }

                // 有HTTP特性但没有模板，使用方法名
                routeInfo.Template = actionName;
            }
            else
            {
                // 没有HTTP特性，使用方法名，支持POST和GET
                routeInfo.Template = actionName;
                routeInfo.HttpMethods.Add("GET");
                routeInfo.HttpMethods.Add("POST");
            }

            return routeInfo;
        }

        /// <summary>
        /// 构建路由模板
        /// </summary>
        /// <param name="method">方法信息</param>
        /// <param name="controllerRoute">控制器路由</param>
        /// <param name="actionName">动作名称</param>
        /// <param name="moduleName">模块名称</param>
        /// <returns>完整的路由模板</returns>
        public static string BuildRouteTemplate(MethodInfo method, string controllerRoute, string actionName, string moduleName)
        {
            // 1. 构建基础控制器路由：api/{模块名}/{控制器名}
            var baseRoute = $"api/{moduleName.ToLowerInvariant()}/{controllerRoute.ToLowerInvariant()}";

            // 2. 获取方法的路由信息
            var routeInfo = GetMethodRouteInfo(method, actionName);

            // 3. 构建完整路由：基础路由 + 模板
            return $"{baseRoute}/{routeInfo.Template.ToLowerInvariant()}";
        }
    }
}
