{"@t":"2025-08-14T05:51:19.3604892Z","@mt":"发现 {Count} 个模块文件","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T05:51:20.1178999Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T05:51:20.1193031Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T05:51:20.1424051Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T05:51:20.1436802Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T05:51:20.2255886Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T05:51:20.4602011Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T05:51:20.4623563Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T05:51:20.5048985Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T05:51:20.5060882Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T05:51:20.5076141Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T05:51:21.7536612Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T05:51:21.7561831Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T05:51:21.7579705Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T05:51:21.7594356Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T05:51:21.7603429Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T05:51:22.2647844Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T05:51:22.2677646Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T05:51:22.2695891Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T05:51:22.2708243Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T05:51:24.3411356Z","@mt":"首次请求触发端点刷新","@tr":"c825e11aa384d7625f8ea0c81903901d","@sp":"e361afcc649c91a7","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEQUQC13MDL:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEQUQC13MDL","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T05:51:24.3543299Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"c825e11aa384d7625f8ea0c81903901d","@sp":"e361afcc649c91a7","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEQUQC13MDL:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEQUQC13MDL","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T05:51:24.7378592Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"0eef5480d4d67533f4bd5f55a27e9edd","@sp":"0d0ceea8a82ad648","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEQUQC13MDL:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEQUQC13MDL","MachineName":"LAPTOP-F6SGUTN5","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T05:51:24.7411149Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"0eef5480d4d67533f4bd5f55a27e9edd","@sp":"0d0ceea8a82ad648","Count":1,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEQUQC13MDL:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEQUQC13MDL","MachineName":"LAPTOP-F6SGUTN5","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T05:51:24.7476496Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"0eef5480d4d67533f4bd5f55a27e9edd","@sp":"0d0ceea8a82ad648","PathCount":16,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEQUQC13MDL:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEQUQC13MDL","MachineName":"LAPTOP-F6SGUTN5","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:50:15.1525765Z","@mt":"发现 {Count} 个模块文件","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:50:15.1944312Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:50:15.1956932Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:50:15.2208983Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:50:15.2220996Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:50:15.2436816Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:50:15.3121363Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:50:15.3134221Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:50:15.3141073Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:50:15.3146897Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:50:15.3154553Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:50:16.7684593Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:50:16.7705431Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:50:16.7724475Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:50:16.7739598Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:50:16.7754511Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:50:17.2893241Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:50:17.2920996Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:50:17.2950203Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:50:17.2959795Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:50:34.4257793Z","@mt":"首次请求触发端点刷新","@tr":"ced25d8462f46d73a7a9400f07fe21e4","@sp":"aa8e1b470cd88141","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER0SVS68K2:********","RequestPath":"/api/auth/login","ConnectionId":"0HNER0SVS68K2","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:50:34.4285641Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"ced25d8462f46d73a7a9400f07fe21e4","@sp":"aa8e1b470cd88141","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER0SVS68K2:********","RequestPath":"/api/auth/login","ConnectionId":"0HNER0SVS68K2","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:50:51.9192304Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"2cb2e89ba194a6f644e3fad1f4f979fa","@sp":"0dfefea1c36fdb03","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER0SVS68K4:00000007","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER0SVS68K4","MachineName":"LAPTOP-F6SGUTN5","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:50:51.9212498Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"2cb2e89ba194a6f644e3fad1f4f979fa","@sp":"0dfefea1c36fdb03","Count":1,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER0SVS68K4:00000007","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER0SVS68K4","MachineName":"LAPTOP-F6SGUTN5","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:50:51.9277268Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"2cb2e89ba194a6f644e3fad1f4f979fa","@sp":"0dfefea1c36fdb03","PathCount":16,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER0SVS68K4:00000007","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER0SVS68K4","MachineName":"LAPTOP-F6SGUTN5","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:51:34.8853109Z","@mt":"发现 {Count} 个模块文件","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:51:34.8926223Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:51:34.9087404Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:51:34.9362672Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:51:34.9373110Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:51:34.9575277Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:51:35.0264349Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:51:35.0276951Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:51:35.0283337Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:51:35.0290687Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:51:35.0299315Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:51:36.4735509Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:51:36.4758430Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:51:36.4768640Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:51:36.4774411Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:51:36.4779661Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:51:36.9792837Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:51:36.9815943Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:51:36.9839810Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:51:36.9850365Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:52:10.6533139Z","@mt":"首次请求触发端点刷新","@tr":"50b5c659bb2d8a73a7e2067dd01caa42","@sp":"e297e8e4a234e6e6","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER0TSNN4LT:********","RequestPath":"/api/auth/login","ConnectionId":"0HNER0TSNN4LT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:52:10.6583185Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"50b5c659bb2d8a73a7e2067dd01caa42","@sp":"e297e8e4a234e6e6","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER0TSNN4LT:********","RequestPath":"/api/auth/login","ConnectionId":"0HNER0TSNN4LT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:52:58.2193978Z","@mt":"手动API触发端点刷新完成，发现 {Count} 个模块","@tr":"add6aee236fdefb5376c74edd5d66324","@sp":"b37a241befa2dfd6","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HNER0TSNN4M0:********","RequestPath":"/api/endpoints/refresh","ConnectionId":"0HNER0TSNN4M0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":27,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:54:01.5147509Z","@mt":"手动API触发端点刷新完成，发现 {Count} 个模块","@tr":"476a216f31e6c67a184d9fe00bf50a16","@sp":"f7db7201dd3f70de","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HNER0TSNN4M3:********","RequestPath":"/api/endpoints/refresh","ConnectionId":"0HNER0TSNN4M3","MachineName":"LAPTOP-F6SGUTN5","ThreadId":27,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:55:08.9972493Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"5fb641a96986c5497b813ec01ea9a38d","@sp":"f1c4fec3f8c79562","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER0TSNN4M6:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER0TSNN4M6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:55:08.9993718Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"5fb641a96986c5497b813ec01ea9a38d","@sp":"f1c4fec3f8c79562","Count":1,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER0TSNN4M6:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER0TSNN4M6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:55:09.0031307Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"5fb641a96986c5497b813ec01ea9a38d","@sp":"f1c4fec3f8c79562","PathCount":16,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER0TSNN4M6:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER0TSNN4M6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:59:12.4000744Z","@mt":"发现 {Count} 个模块文件","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:59:12.4145916Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:59:12.4272040Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:59:12.4967668Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:59:12.4975036Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:59:12.5161188Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:59:12.5601866Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:59:12.5614106Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:59:12.5622770Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:59:12.5628674Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:59:12.5633921Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:59:14.0297267Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:59:14.0320318Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:59:14.0336037Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:59:14.0341042Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:59:14.0348359Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:59:14.5384575Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:59:14.5409529Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:59:14.5420434Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:59:14.5424623Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:59:43.6265588Z","@mt":"首次请求触发端点刷新","@tr":"f0efb7fb08cefaab5d5ba74f3b702669","@sp":"16baae9d5b714500","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER123NJDC0:********","RequestPath":"/api/auth/login","ConnectionId":"0HNER123NJDC0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T07:59:43.6312806Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"f0efb7fb08cefaab5d5ba74f3b702669","@sp":"16baae9d5b714500","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER123NJDC0:********","RequestPath":"/api/auth/login","ConnectionId":"0HNER123NJDC0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:00:49.4814566Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Dapr.Client.InvocationException: An exception occurred while invoking method: 'WeatherForecast/Get123/123' on app-id: 'ssic-devtools'\r\n ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:3500)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.<SendAsync>g__CreateHandlerAndSendAsync|115_0(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)\r\n   at Dapr.Client.DaprClientGrpc.InvokeMethodWithResponseAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at Dapr.Client.DaprClientGrpc.InvokeMethodWithResponseAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Dapr.Client.DaprClientGrpc.InvokeMethodAsync[TResponse](HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at SSIC.Infrastructure.Dapr.DaprClientProvider.InvokeServiceWithLoadBalancingAsync[TRequest,TResponse](HttpMethod httpMethod, String appId, String methodName, TRequest data) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Dapr\\DaprClientProvider.cs:line 34\r\n   at SSIC.HostServer.Controllers.WeatherForecast2Controller.Get() in F:\\SSIC\\Host\\SSIC.HostServer\\Controllers\\WeatherForecastController.cs:line 46\r\n   at lambda_method11(Closure, Object)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 94\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"b7fa4f478b15e23c0f8f712fe47384f8","@sp":"2db4d554c4d78a3c","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNER123NJDC3:********","RequestPath":"/WeatherForecast2","ConnectionId":"0HNER123NJDC3","MachineName":"LAPTOP-F6SGUTN5","ThreadId":27,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:01:54.3249357Z","@mt":"收到OpenAPI文档刷新请求","@tr":"b6998debbcff4c52998e9865df6375b7","@sp":"be85b895497236ca","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HNER123NJDC4:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER123NJDC4","MachineName":"LAPTOP-F6SGUTN5","ThreadId":27,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:01:54.3273974Z","@mt":"开始刷新OpenAPI相关组件...","@tr":"b6998debbcff4c52998e9865df6375b7","@sp":"be85b895497236ca","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","RequestId":"0HNER123NJDC4:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER123NJDC4","MachineName":"LAPTOP-F6SGUTN5","ThreadId":27,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:01:54.3287639Z","@mt":"ActionDescriptor刷新完成","@tr":"b6998debbcff4c52998e9865df6375b7","@sp":"be85b895497236ca","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","RequestId":"0HNER123NJDC4:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER123NJDC4","MachineName":"LAPTOP-F6SGUTN5","ThreadId":27,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:01:54.3294433Z","@mt":"已通知控制器变更","@tr":"b6998debbcff4c52998e9865df6375b7","@sp":"be85b895497236ca","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","RequestId":"0HNER123NJDC4:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER123NJDC4","MachineName":"LAPTOP-F6SGUTN5","ThreadId":27,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:01:54.3299661Z","@mt":"OpenAPI组件刷新完成","@tr":"b6998debbcff4c52998e9865df6375b7","@sp":"be85b895497236ca","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","RequestId":"0HNER123NJDC4:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER123NJDC4","MachineName":"LAPTOP-F6SGUTN5","ThreadId":27,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:01:54.3305052Z","@mt":"已使用简化服务刷新OpenAPI组件","@tr":"b6998debbcff4c52998e9865df6375b7","@sp":"be85b895497236ca","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HNER123NJDC4:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER123NJDC4","MachineName":"LAPTOP-F6SGUTN5","ThreadId":27,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:05:03.1672712Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:05:03.1561485Z","@mt":"发现 {Count} 个模块文件","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:05:03.1820565Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:05:03.2116928Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:05:03.2130153Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:05:03.2341802Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:05:03.2980254Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:05:03.2995840Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:05:03.3004434Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:05:03.3010571Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:05:03.3015464Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:05:04.7477345Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:05:04.7490183Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:05:04.7499452Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:05:04.7503666Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:05:04.7508583Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:05:05.2545209Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:05:05.2568998Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:05:05.2582907Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:05:05.2606309Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:05:45.2848017Z","@mt":"首次请求触发端点刷新","@tr":"499d4841f6f8791424ee20dbd905e7aa","@sp":"f3de76d220246b01","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER15FGK9UU:********","RequestPath":"/api/debug/controllers","ConnectionId":"0HNER15FGK9UU","MachineName":"LAPTOP-F6SGUTN5","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:05:45.2879077Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"499d4841f6f8791424ee20dbd905e7aa","@sp":"f3de76d220246b01","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER15FGK9UU:********","RequestPath":"/api/debug/controllers","ConnectionId":"0HNER15FGK9UU","MachineName":"LAPTOP-F6SGUTN5","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:06:27.1098602Z","@mt":"收到OpenAPI文档刷新请求","@tr":"b12844cab2e329bcb42d284a49fe34ff","@sp":"b9131c6d5ab9b31a","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HNER15FGK9V1:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER15FGK9V1","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:06:27.1126704Z","@mt":"开始刷新OpenAPI相关组件...","@tr":"b12844cab2e329bcb42d284a49fe34ff","@sp":"b9131c6d5ab9b31a","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","RequestId":"0HNER15FGK9V1:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER15FGK9V1","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:06:27.1143128Z","@mt":"ActionDescriptor刷新完成","@tr":"b12844cab2e329bcb42d284a49fe34ff","@sp":"b9131c6d5ab9b31a","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","RequestId":"0HNER15FGK9V1:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER15FGK9V1","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:06:27.1150867Z","@mt":"已通知控制器变更","@tr":"b12844cab2e329bcb42d284a49fe34ff","@sp":"b9131c6d5ab9b31a","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","RequestId":"0HNER15FGK9V1:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER15FGK9V1","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:06:27.1158587Z","@mt":"OpenAPI组件刷新完成","@tr":"b12844cab2e329bcb42d284a49fe34ff","@sp":"b9131c6d5ab9b31a","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","RequestId":"0HNER15FGK9V1:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER15FGK9V1","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:06:27.1164791Z","@mt":"已使用简化服务刷新OpenAPI组件","@tr":"b12844cab2e329bcb42d284a49fe34ff","@sp":"b9131c6d5ab9b31a","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HNER15FGK9V1:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER15FGK9V1","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:15:09.8734747Z","@mt":"发现 {Count} 个模块文件","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:15:09.8734746Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:15:09.8997424Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:15:09.9383965Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:15:09.9392856Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:15:09.9672128Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:15:10.0582727Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:15:10.0595927Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:15:10.0603461Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:15:10.0608651Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:15:10.0614313Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:15:11.4830738Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:15:11.4853952Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:15:11.4873688Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:15:11.4888484Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:15:11.4904573Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:15:11.9974094Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:15:12.0002108Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:15:12.0026110Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:15:12.0038795Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:15:54.0019460Z","@mt":"首次请求触发端点刷新","@tr":"695423afd99206aea580742918508e37","@sp":"6aaa692f8abe054c","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER1B4TUOG5:********","RequestPath":"/api/auth/login","ConnectionId":"0HNER1B4TUOG5","MachineName":"LAPTOP-F6SGUTN5","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:15:54.0058141Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"695423afd99206aea580742918508e37","@sp":"6aaa692f8abe054c","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER1B4TUOG5:********","RequestPath":"/api/auth/login","ConnectionId":"0HNER1B4TUOG5","MachineName":"LAPTOP-F6SGUTN5","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:16:15.3128199Z","@mt":"收到OpenAPI文档刷新请求","@tr":"8d91350db3d0dcd8cc9de907ea63fd36","@sp":"a02c638d2fc19d1e","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HNER1B4TUOG7:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER1B4TUOG7","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:16:15.3146979Z","@mt":"开始刷新OpenAPI相关组件...","@tr":"8d91350db3d0dcd8cc9de907ea63fd36","@sp":"a02c638d2fc19d1e","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","RequestId":"0HNER1B4TUOG7:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER1B4TUOG7","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:16:15.3172719Z","@mt":"ActionDescriptor刷新完成","@tr":"8d91350db3d0dcd8cc9de907ea63fd36","@sp":"a02c638d2fc19d1e","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","RequestId":"0HNER1B4TUOG7:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER1B4TUOG7","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:16:15.3179887Z","@mt":"已通知控制器变更","@tr":"8d91350db3d0dcd8cc9de907ea63fd36","@sp":"a02c638d2fc19d1e","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","RequestId":"0HNER1B4TUOG7:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER1B4TUOG7","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:16:15.3185281Z","@mt":"OpenAPI组件刷新完成","@tr":"8d91350db3d0dcd8cc9de907ea63fd36","@sp":"a02c638d2fc19d1e","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","RequestId":"0HNER1B4TUOG7:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER1B4TUOG7","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:16:15.3191608Z","@mt":"已使用简化服务刷新OpenAPI组件","@tr":"8d91350db3d0dcd8cc9de907ea63fd36","@sp":"a02c638d2fc19d1e","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HNER1B4TUOG7:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER1B4TUOG7","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:18:52.8375000Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:18:52.8340221Z","@mt":"发现 {Count} 个模块文件","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:18:52.8576349Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:18:52.9059353Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:18:52.9065379Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:18:52.9381422Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:18:53.0087299Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:18:53.0103352Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:18:53.0112140Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:18:53.0117325Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:18:53.0124281Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:18:54.4685847Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:18:54.4702081Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:18:54.4715503Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:18:54.4724618Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:18:54.4731209Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:18:54.9826845Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:18:54.9863161Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:18:54.9882902Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:18:54.9890679Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:19:35.3442939Z","@mt":"首次请求触发端点刷新","@tr":"ed0102d1d182f5fd7ee39d735b3ee683","@sp":"9f1ec26659abc0cc","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER1D6SMEEN:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER1D6SMEEN","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:19:35.3498263Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"ed0102d1d182f5fd7ee39d735b3ee683","@sp":"9f1ec26659abc0cc","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER1D6SMEEN:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER1D6SMEEN","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:19:35.3750630Z","@mt":"收到OpenAPI文档刷新请求","@tr":"ed0102d1d182f5fd7ee39d735b3ee683","@sp":"9f1ec26659abc0cc","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HNER1D6SMEEN:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER1D6SMEEN","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:19:35.3774486Z","@mt":"开始刷新OpenAPI相关组件...","@tr":"ed0102d1d182f5fd7ee39d735b3ee683","@sp":"9f1ec26659abc0cc","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","RequestId":"0HNER1D6SMEEN:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER1D6SMEEN","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:19:35.3786530Z","@mt":"ActionDescriptor刷新完成","@tr":"ed0102d1d182f5fd7ee39d735b3ee683","@sp":"9f1ec26659abc0cc","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","RequestId":"0HNER1D6SMEEN:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER1D6SMEEN","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:19:35.3795260Z","@mt":"已通知控制器变更","@tr":"ed0102d1d182f5fd7ee39d735b3ee683","@sp":"9f1ec26659abc0cc","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","RequestId":"0HNER1D6SMEEN:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER1D6SMEEN","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:19:35.3801713Z","@mt":"OpenAPI组件刷新完成","@tr":"ed0102d1d182f5fd7ee39d735b3ee683","@sp":"9f1ec26659abc0cc","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","RequestId":"0HNER1D6SMEEN:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER1D6SMEEN","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:19:35.3808478Z","@mt":"已使用简化服务刷新OpenAPI组件","@tr":"ed0102d1d182f5fd7ee39d735b3ee683","@sp":"9f1ec26659abc0cc","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HNER1D6SMEEN:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER1D6SMEEN","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:23:38.7875027Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:23:38.7836936Z","@mt":"发现 {Count} 个模块文件","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:23:38.8097176Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:23:38.8435778Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:23:38.8443046Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:23:38.8657035Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:23:38.9492345Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:23:38.9516400Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:23:38.9524806Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:23:38.9532612Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:23:38.9538708Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:23:40.3783628Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:23:40.3869699Z","@mt":"刷新ActionDescriptor时出错","@l":"Error","@x":"System.InvalidOperationException: Cannot resolve scoped service 'SSIC.Infrastructure.Startups.HotReload.IActionDescriptorRefreshService' from root provider.\r\n   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteValidator.ValidateResolution(ServiceCallSite callSite, IServiceScope scope, IServiceScope rootScope)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetService[T](IServiceProvider provider)\r\n   at SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService.RefreshActionDescriptors() in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\HotReload\\SimpleOpenApiRefreshService.cs:line 69","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:23:40.3933367Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:23:40.3939616Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:23:40.3945757Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:23:40.9080624Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:23:40.9111249Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:23:40.9125942Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:23:40.9135069Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:23:58.9325964Z","@mt":"首次请求触发端点刷新","@tr":"25a8a63a5efe28f852e46f0ec5c750fc","@sp":"7258cdf5d6c94f0c","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER1FLEGDOG:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER1FLEGDOG","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:23:58.9365696Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"25a8a63a5efe28f852e46f0ec5c750fc","@sp":"7258cdf5d6c94f0c","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER1FLEGDOG:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER1FLEGDOG","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:23:58.9628709Z","@mt":"收到OpenAPI文档刷新请求","@tr":"25a8a63a5efe28f852e46f0ec5c750fc","@sp":"7258cdf5d6c94f0c","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HNER1FLEGDOG:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER1FLEGDOG","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:23:58.9652682Z","@mt":"开始刷新OpenAPI相关组件...","@tr":"25a8a63a5efe28f852e46f0ec5c750fc","@sp":"7258cdf5d6c94f0c","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","RequestId":"0HNER1FLEGDOG:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER1FLEGDOG","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:23:58.9663553Z","@mt":"刷新ActionDescriptor时出错","@l":"Error","@x":"System.InvalidOperationException: Cannot resolve scoped service 'SSIC.Infrastructure.Startups.HotReload.IActionDescriptorRefreshService' from root provider.\r\n   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteValidator.ValidateResolution(ServiceCallSite callSite, IServiceScope scope, IServiceScope rootScope)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetService[T](IServiceProvider provider)\r\n   at SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService.RefreshActionDescriptors() in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\HotReload\\SimpleOpenApiRefreshService.cs:line 69","@tr":"25a8a63a5efe28f852e46f0ec5c750fc","@sp":"7258cdf5d6c94f0c","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","RequestId":"0HNER1FLEGDOG:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER1FLEGDOG","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:23:58.9694487Z","@mt":"已通知控制器变更","@tr":"25a8a63a5efe28f852e46f0ec5c750fc","@sp":"7258cdf5d6c94f0c","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","RequestId":"0HNER1FLEGDOG:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER1FLEGDOG","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:23:58.9705264Z","@mt":"OpenAPI组件刷新完成","@tr":"25a8a63a5efe28f852e46f0ec5c750fc","@sp":"7258cdf5d6c94f0c","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","RequestId":"0HNER1FLEGDOG:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER1FLEGDOG","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T08:23:58.9711231Z","@mt":"已使用简化服务刷新OpenAPI组件","@tr":"25a8a63a5efe28f852e46f0ec5c750fc","@sp":"7258cdf5d6c94f0c","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HNER1FLEGDOG:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER1FLEGDOG","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:47:52.5294957Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:47:52.5194827Z","@mt":"发现 {Count} 个模块文件","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:47:52.5450918Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:47:52.5461423Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:47:52.5474734Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:47:53.0091924Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth","AssemblyName":"SSIC.Modules.Auth, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:47:53.0120132Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:47:53.0131452Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:47:53.0165473Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:47:53.0182585Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:47:53.0290601Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:47:53.0302190Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:47:53.0467173Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:47:53.1328116Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:47:53.1340708Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:47:53.1347427Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:47:53.1351950Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:47:53.1356483Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:47:54.5689693Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:47:54.5711114Z","@mt":"刷新ActionDescriptor时出错","@l":"Error","@x":"System.InvalidOperationException: Cannot resolve scoped service 'SSIC.Infrastructure.Startups.HotReload.IActionDescriptorRefreshService' from root provider.\r\n   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteValidator.ValidateResolution(ServiceCallSite callSite, IServiceScope scope, IServiceScope rootScope)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetService[T](IServiceProvider provider)\r\n   at SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService.RefreshActionDescriptors() in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\HotReload\\SimpleOpenApiRefreshService.cs:line 69","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:47:54.5743299Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:47:54.5751434Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:47:54.5760658Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:47:55.0822230Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:47:55.0843638Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:47:55.0856133Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:47:55.0860313Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:48:12.8702189Z","@mt":"首次请求触发端点刷新","@tr":"6936eb8e43009fb8ecace61c9382b0cf","@sp":"aac9e4a0b325a8de","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER2UNKJLTN:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER2UNKJLTN","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:48:12.8758803Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"6936eb8e43009fb8ecace61c9382b0cf","@sp":"aac9e4a0b325a8de","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER2UNKJLTN:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER2UNKJLTN","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:48:12.8817614Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.InvalidOperationException: Action 'SSIC.Modules.Auth.Controllers.RoleController.CreateRole (SSIC.Modules.Auth)' does not have an attribute route. Action methods on controllers annotated with ApiControllerAttribute must be attribute routed.\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.EnsureActionIsAttributeRouted(ActionModel actionModel)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.OnProvidersExecuting(ApplicationModelProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApplicationModelFactory.CreateApplicationModel(IEnumerable`1 controllerTypes)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.GetDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.OnProvidersExecuting(ActionDescriptorProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.UpdateCollection()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.get_ActionDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.UpdateEndpoints()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.GetChangeToken()\r\n   at Microsoft.Extensions.Primitives.ChangeToken.ChangeTokenRegistration`1..ctor(Func`1 changeTokenProducer, Action`1 changeTokenConsumer, TState state)\r\n   at Microsoft.Extensions.Primitives.ChangeToken.OnChange(Func`1 changeTokenProducer, Action changeTokenConsumer)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.CreateChangeTokenUnsynchronized(Boolean collectionChanged)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.EnsureChangeTokenInitialized()\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.GetChangeToken()\r\n   at Microsoft.AspNetCore.Routing.DataSourceDependentCache`1.Initialize()\r\n   at System.Threading.LazyInitializer.EnsureInitializedCore[T](T& target, Boolean& initialized, Object& syncLock, Func`1 valueFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DataSourceDependentMatcher..ctor(EndpointDataSource dataSource, Lifetime lifetime, Func`1 matcherBuilderFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DfaMatcherFactory.CreateMatcher(EndpointDataSource dataSource)\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.InitializeCoreAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatcher|10_0(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task`1 matcherTask)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"6936eb8e43009fb8ecace61c9382b0cf","@sp":"aac9e4a0b325a8de","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNER2UNKJLTN:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER2UNKJLTN","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:48:35.0492554Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.InvalidOperationException: Action 'SSIC.Modules.Auth.Controllers.RoleController.CreateRole (SSIC.Modules.Auth)' does not have an attribute route. Action methods on controllers annotated with ApiControllerAttribute must be attribute routed.\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.EnsureActionIsAttributeRouted(ActionModel actionModel)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.OnProvidersExecuting(ApplicationModelProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApplicationModelFactory.CreateApplicationModel(IEnumerable`1 controllerTypes)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.GetDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.OnProvidersExecuting(ActionDescriptorProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.UpdateCollection()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.get_ActionDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.UpdateEndpoints()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.GetChangeToken()\r\n   at Microsoft.Extensions.Primitives.ChangeToken.ChangeTokenRegistration`1..ctor(Func`1 changeTokenProducer, Action`1 changeTokenConsumer, TState state)\r\n   at Microsoft.Extensions.Primitives.ChangeToken.OnChange(Func`1 changeTokenProducer, Action changeTokenConsumer)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.CreateChangeTokenUnsynchronized(Boolean collectionChanged)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.EnsureChangeTokenInitialized()\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.GetChangeToken()\r\n   at Microsoft.AspNetCore.Routing.DataSourceDependentCache`1.Initialize()\r\n   at System.Threading.LazyInitializer.EnsureInitializedCore[T](T& target, Boolean& initialized, Object& syncLock, Func`1 valueFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DataSourceDependentMatcher..ctor(EndpointDataSource dataSource, Lifetime lifetime, Func`1 matcherBuilderFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DfaMatcherFactory.CreateMatcher(EndpointDataSource dataSource)\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.InitializeCoreAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatcher|10_0(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task`1 matcherTask)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"434e64b26d67c73201dcccaeb17bfaa7","@sp":"f67619681b6440f4","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNER2UNKJLTO:********","RequestPath":"/api/debug/controllers","ConnectionId":"0HNER2UNKJLTO","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:51:49.7998627Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:51:49.7948233Z","@mt":"发现 {Count} 个模块文件","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:51:49.8184207Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:51:49.8198815Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:51:49.8218293Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:51:49.8264248Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth","AssemblyName":"SSIC.Modules.Auth, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:51:49.8281415Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:51:49.8292137Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:51:49.8323664Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:51:49.8337602Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:51:49.8474001Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:51:49.8529595Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:51:49.8654813Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:51:49.9544015Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:51:49.9560657Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:51:49.9572325Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:51:49.9578271Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:51:49.9590553Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:51:51.3756342Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:51:51.3782818Z","@mt":"刷新ActionDescriptor时出错","@l":"Error","@x":"System.InvalidOperationException: Cannot resolve scoped service 'SSIC.Infrastructure.Startups.HotReload.IActionDescriptorRefreshService' from root provider.\r\n   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteValidator.ValidateResolution(ServiceCallSite callSite, IServiceScope scope, IServiceScope rootScope)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetService[T](IServiceProvider provider)\r\n   at SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService.RefreshActionDescriptors() in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\HotReload\\SimpleOpenApiRefreshService.cs:line 69","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:51:51.3820968Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:51:51.3834692Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:51:51.3845627Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:51:51.8944664Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:51:51.8973458Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:51:51.8999151Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:51:51.9013556Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:52:12.1010517Z","@mt":"首次请求触发端点刷新","@tr":"db833fa7a011e748d006adc7b60e2b44","@sp":"20315a7bb4d4b696","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER30UTV5E5:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER30UTV5E5","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:52:12.1080117Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"db833fa7a011e748d006adc7b60e2b44","@sp":"20315a7bb4d4b696","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER30UTV5E5:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER30UTV5E5","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:52:12.1139456Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.InvalidOperationException: Action 'SSIC.Modules.Auth.Controllers.RoleController.CreateRole (SSIC.Modules.Auth)' does not have an attribute route. Action methods on controllers annotated with ApiControllerAttribute must be attribute routed.\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.EnsureActionIsAttributeRouted(ActionModel actionModel)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.OnProvidersExecuting(ApplicationModelProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApplicationModelFactory.CreateApplicationModel(IEnumerable`1 controllerTypes)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.GetDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.OnProvidersExecuting(ActionDescriptorProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.UpdateCollection()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.get_ActionDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.UpdateEndpoints()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.GetChangeToken()\r\n   at Microsoft.Extensions.Primitives.ChangeToken.ChangeTokenRegistration`1..ctor(Func`1 changeTokenProducer, Action`1 changeTokenConsumer, TState state)\r\n   at Microsoft.Extensions.Primitives.ChangeToken.OnChange(Func`1 changeTokenProducer, Action changeTokenConsumer)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.CreateChangeTokenUnsynchronized(Boolean collectionChanged)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.EnsureChangeTokenInitialized()\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.GetChangeToken()\r\n   at Microsoft.AspNetCore.Routing.DataSourceDependentCache`1.Initialize()\r\n   at System.Threading.LazyInitializer.EnsureInitializedCore[T](T& target, Boolean& initialized, Object& syncLock, Func`1 valueFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DataSourceDependentMatcher..ctor(EndpointDataSource dataSource, Lifetime lifetime, Func`1 matcherBuilderFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DfaMatcherFactory.CreateMatcher(EndpointDataSource dataSource)\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.InitializeCoreAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatcher|10_0(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task`1 matcherTask)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"db833fa7a011e748d006adc7b60e2b44","@sp":"20315a7bb4d4b696","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNER30UTV5E5:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER30UTV5E5","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T09:52:24.8051665Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.InvalidOperationException: Action 'SSIC.Modules.Auth.Controllers.RoleController.CreateRole (SSIC.Modules.Auth)' does not have an attribute route. Action methods on controllers annotated with ApiControllerAttribute must be attribute routed.\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.EnsureActionIsAttributeRouted(ActionModel actionModel)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.OnProvidersExecuting(ApplicationModelProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApplicationModelFactory.CreateApplicationModel(IEnumerable`1 controllerTypes)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.GetDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.OnProvidersExecuting(ActionDescriptorProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.UpdateCollection()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.get_ActionDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.UpdateEndpoints()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.GetChangeToken()\r\n   at Microsoft.Extensions.Primitives.ChangeToken.ChangeTokenRegistration`1..ctor(Func`1 changeTokenProducer, Action`1 changeTokenConsumer, TState state)\r\n   at Microsoft.Extensions.Primitives.ChangeToken.OnChange(Func`1 changeTokenProducer, Action changeTokenConsumer)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.CreateChangeTokenUnsynchronized(Boolean collectionChanged)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.EnsureChangeTokenInitialized()\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.GetChangeToken()\r\n   at Microsoft.AspNetCore.Routing.DataSourceDependentCache`1.Initialize()\r\n   at System.Threading.LazyInitializer.EnsureInitializedCore[T](T& target, Boolean& initialized, Object& syncLock, Func`1 valueFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DataSourceDependentMatcher..ctor(EndpointDataSource dataSource, Lifetime lifetime, Func`1 matcherBuilderFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DfaMatcherFactory.CreateMatcher(EndpointDataSource dataSource)\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.InitializeCoreAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatcher|10_0(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task`1 matcherTask)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"4df5de5a854949cc74f08f17ee6a0e8a","@sp":"c2adfac6669e21f7","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNER30UTV5E6:********","RequestPath":"/api/auth/login","ConnectionId":"0HNER30UTV5E6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:00:00.2856723Z","@mt":"发现 {Count} 个模块文件","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:00:00.2960011Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:00:00.3083235Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:00:00.3084443Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:00:00.3107850Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:00:00.3178381Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth","AssemblyName":"SSIC.Modules.Auth, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:00:00.3193800Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:00:00.3202632Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:00:00.3227721Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:00:00.3241189Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:00:00.3324016Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:00:00.3331148Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:00:00.3433048Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:00:00.4261769Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:00:00.4284484Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:00:00.4290693Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:00:00.4295499Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:00:00.4300360Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:00:01.8611372Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:00:01.8653440Z","@mt":"刷新ActionDescriptor时出错","@l":"Error","@x":"System.InvalidOperationException: Cannot resolve scoped service 'SSIC.Infrastructure.Startups.HotReload.IActionDescriptorRefreshService' from root provider.\r\n   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteValidator.ValidateResolution(ServiceCallSite callSite, IServiceScope scope, IServiceScope rootScope)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetService[T](IServiceProvider provider)\r\n   at SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService.RefreshActionDescriptors() in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\HotReload\\SimpleOpenApiRefreshService.cs:line 69","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:00:01.8691564Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:00:01.8702685Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:00:01.8712667Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:00:02.3778383Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:00:02.3808109Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:00:02.3823802Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:00:02.3828581Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:00:23.3938458Z","@mt":"首次请求触发端点刷新","@tr":"9893011e548a32c0b8cfe33acd9b970b","@sp":"16a50f38641ba1d6","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER35HBFP60:********","RequestPath":"/api/openapi/modules","ConnectionId":"0HNER35HBFP60","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:00:23.3997904Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"9893011e548a32c0b8cfe33acd9b970b","@sp":"16a50f38641ba1d6","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER35HBFP60:********","RequestPath":"/api/openapi/modules","ConnectionId":"0HNER35HBFP60","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:00:23.4030959Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.InvalidOperationException: Action 'SSIC.Modules.Auth.Controllers.RoleController.CreateRole (SSIC.Modules.Auth)' does not have an attribute route. Action methods on controllers annotated with ApiControllerAttribute must be attribute routed.\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.EnsureActionIsAttributeRouted(ActionModel actionModel)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.OnProvidersExecuting(ApplicationModelProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApplicationModelFactory.CreateApplicationModel(IEnumerable`1 controllerTypes)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.GetDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.OnProvidersExecuting(ActionDescriptorProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.UpdateCollection()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.get_ActionDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.UpdateEndpoints()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.GetChangeToken()\r\n   at Microsoft.Extensions.Primitives.ChangeToken.ChangeTokenRegistration`1..ctor(Func`1 changeTokenProducer, Action`1 changeTokenConsumer, TState state)\r\n   at Microsoft.Extensions.Primitives.ChangeToken.OnChange(Func`1 changeTokenProducer, Action changeTokenConsumer)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.CreateChangeTokenUnsynchronized(Boolean collectionChanged)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.EnsureChangeTokenInitialized()\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.GetChangeToken()\r\n   at Microsoft.AspNetCore.Routing.DataSourceDependentCache`1.Initialize()\r\n   at System.Threading.LazyInitializer.EnsureInitializedCore[T](T& target, Boolean& initialized, Object& syncLock, Func`1 valueFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DataSourceDependentMatcher..ctor(EndpointDataSource dataSource, Lifetime lifetime, Func`1 matcherBuilderFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DfaMatcherFactory.CreateMatcher(EndpointDataSource dataSource)\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.InitializeCoreAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatcher|10_0(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task`1 matcherTask)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"9893011e548a32c0b8cfe33acd9b970b","@sp":"16a50f38641ba1d6","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNER35HBFP60:********","RequestPath":"/api/openapi/modules","ConnectionId":"0HNER35HBFP60","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:00:30.7980852Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.InvalidOperationException: Action 'SSIC.Modules.Auth.Controllers.RoleController.CreateRole (SSIC.Modules.Auth)' does not have an attribute route. Action methods on controllers annotated with ApiControllerAttribute must be attribute routed.\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.EnsureActionIsAttributeRouted(ActionModel actionModel)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.OnProvidersExecuting(ApplicationModelProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApplicationModelFactory.CreateApplicationModel(IEnumerable`1 controllerTypes)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.GetDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.OnProvidersExecuting(ActionDescriptorProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.UpdateCollection()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.get_ActionDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.UpdateEndpoints()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.GetChangeToken()\r\n   at Microsoft.Extensions.Primitives.ChangeToken.ChangeTokenRegistration`1..ctor(Func`1 changeTokenProducer, Action`1 changeTokenConsumer, TState state)\r\n   at Microsoft.Extensions.Primitives.ChangeToken.OnChange(Func`1 changeTokenProducer, Action changeTokenConsumer)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.CreateChangeTokenUnsynchronized(Boolean collectionChanged)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.EnsureChangeTokenInitialized()\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.GetChangeToken()\r\n   at Microsoft.AspNetCore.Routing.DataSourceDependentCache`1.Initialize()\r\n   at System.Threading.LazyInitializer.EnsureInitializedCore[T](T& target, Boolean& initialized, Object& syncLock, Func`1 valueFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DataSourceDependentMatcher..ctor(EndpointDataSource dataSource, Lifetime lifetime, Func`1 matcherBuilderFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DfaMatcherFactory.CreateMatcher(EndpointDataSource dataSource)\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.InitializeCoreAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatcher|10_0(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task`1 matcherTask)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"07a0161b1986784215938443af46cdde","@sp":"bd8b8103fe92b0c4","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNER35HBFP61:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER35HBFP61","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:00:41.1747797Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.InvalidOperationException: Action 'SSIC.Modules.Auth.Controllers.RoleController.CreateRole (SSIC.Modules.Auth)' does not have an attribute route. Action methods on controllers annotated with ApiControllerAttribute must be attribute routed.\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.EnsureActionIsAttributeRouted(ActionModel actionModel)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.OnProvidersExecuting(ApplicationModelProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApplicationModelFactory.CreateApplicationModel(IEnumerable`1 controllerTypes)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.GetDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.OnProvidersExecuting(ActionDescriptorProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.UpdateCollection()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.get_ActionDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.UpdateEndpoints()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.GetChangeToken()\r\n   at Microsoft.Extensions.Primitives.ChangeToken.ChangeTokenRegistration`1..ctor(Func`1 changeTokenProducer, Action`1 changeTokenConsumer, TState state)\r\n   at Microsoft.Extensions.Primitives.ChangeToken.OnChange(Func`1 changeTokenProducer, Action changeTokenConsumer)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.CreateChangeTokenUnsynchronized(Boolean collectionChanged)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.EnsureChangeTokenInitialized()\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.GetChangeToken()\r\n   at Microsoft.AspNetCore.Routing.DataSourceDependentCache`1.Initialize()\r\n   at System.Threading.LazyInitializer.EnsureInitializedCore[T](T& target, Boolean& initialized, Object& syncLock, Func`1 valueFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DataSourceDependentMatcher..ctor(EndpointDataSource dataSource, Lifetime lifetime, Func`1 matcherBuilderFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DfaMatcherFactory.CreateMatcher(EndpointDataSource dataSource)\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.InitializeCoreAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatcher|10_0(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task`1 matcherTask)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"1c8461c109d0c3d1b539f34c525ecaae","@sp":"98a62073ca5100a3","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNER35HBFP62:********","RequestPath":"/api/auth/login","ConnectionId":"0HNER35HBFP62","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:04:42.7399621Z","@mt":"发现 {Count} 个模块文件","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:04:42.7495053Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:04:42.7663742Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:04:42.7669022Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:04:42.7684483Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:04:42.7737034Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth","AssemblyName":"SSIC.Modules.Auth, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:04:42.7750140Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:04:42.7755385Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:04:42.7782206Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:04:42.7796212Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:04:42.7883141Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:04:42.7894039Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:04:42.8020696Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:04:42.8891542Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:04:42.8904381Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:04:42.8910122Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:04:42.8914255Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:04:42.8918992Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:04:44.3247706Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:04:44.3264694Z","@mt":"开始强制刷新ActionDescriptorCollectionProvider...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:04:44.3274372Z","@mt":"ActionDescriptorCollectionProvider刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:04:44.3278822Z","@mt":"已使用ActionDescriptorRefreshService刷新ActionDescriptorCollectionProvider","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:04:44.3286052Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:04:44.3290163Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:04:44.3295256Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:04:44.8301573Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:04:44.8332969Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:04:44.8355912Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:04:44.8371907Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:05:15.5309503Z","@mt":"首次请求触发端点刷新","@tr":"a4dc54a3530f3d2b5080f26988c9bccc","@sp":"032947b4e09ce9b3","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER388DGPCG:********","RequestPath":"/api/openapi/modules","ConnectionId":"0HNER388DGPCG","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:05:15.5362942Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"a4dc54a3530f3d2b5080f26988c9bccc","@sp":"032947b4e09ce9b3","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER388DGPCG:********","RequestPath":"/api/openapi/modules","ConnectionId":"0HNER388DGPCG","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:05:15.5411646Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.InvalidOperationException: Action 'SSIC.Modules.Auth.Controllers.RoleController.CreateRole (SSIC.Modules.Auth)' does not have an attribute route. Action methods on controllers annotated with ApiControllerAttribute must be attribute routed.\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.EnsureActionIsAttributeRouted(ActionModel actionModel)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.OnProvidersExecuting(ApplicationModelProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApplicationModelFactory.CreateApplicationModel(IEnumerable`1 controllerTypes)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.GetDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.OnProvidersExecuting(ActionDescriptorProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.UpdateCollection()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.get_ActionDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.UpdateEndpoints()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.GetChangeToken()\r\n   at Microsoft.Extensions.Primitives.ChangeToken.ChangeTokenRegistration`1..ctor(Func`1 changeTokenProducer, Action`1 changeTokenConsumer, TState state)\r\n   at Microsoft.Extensions.Primitives.ChangeToken.OnChange(Func`1 changeTokenProducer, Action changeTokenConsumer)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.CreateChangeTokenUnsynchronized(Boolean collectionChanged)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.EnsureChangeTokenInitialized()\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.GetChangeToken()\r\n   at Microsoft.AspNetCore.Routing.DataSourceDependentCache`1.Initialize()\r\n   at System.Threading.LazyInitializer.EnsureInitializedCore[T](T& target, Boolean& initialized, Object& syncLock, Func`1 valueFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DataSourceDependentMatcher..ctor(EndpointDataSource dataSource, Lifetime lifetime, Func`1 matcherBuilderFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DfaMatcherFactory.CreateMatcher(EndpointDataSource dataSource)\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.InitializeCoreAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatcher|10_0(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task`1 matcherTask)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"a4dc54a3530f3d2b5080f26988c9bccc","@sp":"032947b4e09ce9b3","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNER388DGPCG:********","RequestPath":"/api/openapi/modules","ConnectionId":"0HNER388DGPCG","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:03.1680594Z","@mt":"发现 {Count} 个模块文件","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:03.1682499Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:03.1954584Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:03.1960397Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:03.1980704Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:03.7476524Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth","AssemblyName":"SSIC.Modules.Auth, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:03.7494956Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:03.7503577Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:03.7540544Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:03.7555644Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:03.7630974Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:03.7642753Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:03.7918336Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:03.8667022Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:03.8684284Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:03.8692288Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:03.8697485Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:03.8706549Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:05.3224557Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:05.3246802Z","@mt":"开始强制刷新ActionDescriptorCollectionProvider...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:05.3261328Z","@mt":"ActionDescriptorCollectionProvider刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:05.3267442Z","@mt":"已使用ActionDescriptorRefreshService刷新ActionDescriptorCollectionProvider","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:05.3276659Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:05.3282206Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:05.3288535Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:05.8380360Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:05.8406366Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:05.8422061Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:05.8430336Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:26.6815391Z","@mt":"首次请求触发端点刷新","@tr":"09ecf2ae6e4c37266bbca1d3ec08535e","@sp":"d3c45479309780c5","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER38TK0K1F:********","RequestPath":"/api/openapi/modules","ConnectionId":"0HNER38TK0K1F","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:26.6876753Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"09ecf2ae6e4c37266bbca1d3ec08535e","@sp":"d3c45479309780c5","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER38TK0K1F:********","RequestPath":"/api/openapi/modules","ConnectionId":"0HNER38TK0K1F","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:26.7153685Z","@mt":"收到模块信息查询请求","@tr":"09ecf2ae6e4c37266bbca1d3ec08535e","@sp":"d3c45479309780c5","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HNER38TK0K1F:********","RequestPath":"/api/openapi/modules","ConnectionId":"0HNER38TK0K1F","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:36.3950071Z","@mt":"收到OpenAPI文档刷新请求","@tr":"bfbe6c7d34d329d1cd20803144def221","@sp":"5077711fbbc85db4","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HNER38TK0K1G:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER38TK0K1G","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:36.3978433Z","@mt":"开始刷新热加载OpenAPI文档...","@tr":"bfbe6c7d34d329d1cd20803144def221","@sp":"5077711fbbc85db4","SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiRefreshService","RequestId":"0HNER38TK0K1G:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER38TK0K1G","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:36.3998619Z","@mt":"发现 {Count} 个已加载的模块","@tr":"bfbe6c7d34d329d1cd20803144def221","@sp":"5077711fbbc85db4","Count":1,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiRefreshService","RequestId":"0HNER38TK0K1G:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER38TK0K1G","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:36.4006775Z","@mt":"模块: {Name}, 控制器数: {ControllerCount}, 动作数: {ActionCount}","@tr":"bfbe6c7d34d329d1cd20803144def221","@sp":"5077711fbbc85db4","Name":"Auth","ControllerCount":2,"ActionCount":12,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiRefreshService","RequestId":"0HNER38TK0K1G:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER38TK0K1G","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:36.4116648Z","@mt":"热加载OpenAPI文档刷新完成","@tr":"bfbe6c7d34d329d1cd20803144def221","@sp":"5077711fbbc85db4","SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiRefreshService","RequestId":"0HNER38TK0K1G:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER38TK0K1G","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:36.4129133Z","@mt":"已使用热加载服务刷新OpenAPI组件，发现 {Count} 个模块","@tr":"bfbe6c7d34d329d1cd20803144def221","@sp":"5077711fbbc85db4","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HNER38TK0K1G:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER38TK0K1G","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:43.5065406Z","@mt":"用户登录请求: {Username}","@tr":"2e77fb9ae34e879fbfee9d5c3cdec4b1","@sp":"85b1f91d70414121","Username":"admin","SourceContext":"SSIC.Modules.Auth.Controllers.AuthController","ActionId":"b0a7642c-6368-4b07-9a95-c44c2a814899","ActionName":"SSIC.Modules.Auth.Controllers.AuthController.Login (SSIC.Modules.Auth)","RequestId":"0HNER38TK0K1H:********","RequestPath":"/api/auth/login","ConnectionId":"0HNER38TK0K1H","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:51.9728974Z","@mt":"获取所有角色","@tr":"4c1cf4ba54b42de03ed0c86a4323972c","@sp":"7128ec334651b456","SourceContext":"SSIC.Modules.Auth.Controllers.RoleController","ActionId":"b03015ff-8a84-4e9c-92a9-d0c28bf03913","ActionName":"SSIC.Modules.Auth.Controllers.RoleController.GetRoles (SSIC.Modules.Auth)","RequestId":"0HNER38TK0K1I:********","RequestPath":"/api/auth/roles/list","ConnectionId":"0HNER38TK0K1I","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:59.4712644Z","@mt":"开始转换OpenAPI文档以包含热加载的模块...","@tr":"e6d65bbb362db1bdbbb1f2227a15584a","@sp":"50751644719286df","SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER38TK0K1J:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER38TK0K1J","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:59.4747234Z","@mt":"发现 {Count} 个控制器动作","@tr":"e6d65bbb362db1bdbbb1f2227a15584a","@sp":"50751644719286df","Count":13,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER38TK0K1J:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER38TK0K1J","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:59.4759966Z","@mt":"发现 {Count} 个模块","@tr":"e6d65bbb362db1bdbbb1f2227a15584a","@sp":"50751644719286df","Count":2,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER38TK0K1J:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER38TK0K1J","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:59.4784604Z","@mt":"OpenAPI文档转换完成，包含 {ModuleCount} 个模块，{ActionCount} 个动作","@tr":"e6d65bbb362db1bdbbb1f2227a15584a","@sp":"50751644719286df","ModuleCount":2,"ActionCount":13,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER38TK0K1J:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER38TK0K1J","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:59.4803794Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"e6d65bbb362db1bdbbb1f2227a15584a","@sp":"50751644719286df","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER38TK0K1J:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER38TK0K1J","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:59.4824504Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"e6d65bbb362db1bdbbb1f2227a15584a","@sp":"50751644719286df","Count":1,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER38TK0K1J:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER38TK0K1J","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:06:59.4893573Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"e6d65bbb362db1bdbbb1f2227a15584a","@sp":"50751644719286df","PathCount":17,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER38TK0K1J:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER38TK0K1J","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:03.1884018Z","@mt":"发现 {Count} 个模块文件","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:03.2258113Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:03.2308390Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:03.2312151Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:03.2433799Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:03.3102881Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth","AssemblyName":"SSIC.Modules.Auth, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:03.3133860Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:03.3146070Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:03.3199394Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:03.3338291Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:03.3541372Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:03.3554220Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:03.4119236Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:03.5578904Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:03.5600059Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:03.6089182Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:03.6104581Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:03.6117912Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:04.2241874Z","@mt":"首次请求触发端点刷新","@tr":"0cd9c583ca2a502725f1d6429ace677a","@sp":"9a7ede128815c2b4","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER3ACEHKI6:********","RequestPath":"/scalar/v1","ConnectionId":"0HNER3ACEHKI6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:04.2284914Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"0cd9c583ca2a502725f1d6429ace677a","@sp":"9a7ede128815c2b4","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER3ACEHKI6:********","RequestPath":"/scalar/v1","ConnectionId":"0HNER3ACEHKI6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:04.6183299Z","@mt":"开始转换OpenAPI文档以包含热加载的模块...","@tr":"6e9d581abf4f5fd291cf640c19fa0068","@sp":"340d70d04eefbded","SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3ACEHKI6:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3ACEHKI6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:04.6204375Z","@mt":"发现 {Count} 个控制器动作","@tr":"6e9d581abf4f5fd291cf640c19fa0068","@sp":"340d70d04eefbded","Count":13,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3ACEHKI6:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3ACEHKI6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:04.6221838Z","@mt":"发现 {Count} 个模块","@tr":"6e9d581abf4f5fd291cf640c19fa0068","@sp":"340d70d04eefbded","Count":2,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3ACEHKI6:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3ACEHKI6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:04.6268946Z","@mt":"OpenAPI文档转换完成，包含 {ModuleCount} 个模块，{ActionCount} 个动作","@tr":"6e9d581abf4f5fd291cf640c19fa0068","@sp":"340d70d04eefbded","ModuleCount":2,"ActionCount":13,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3ACEHKI6:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3ACEHKI6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:04.6307440Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"6e9d581abf4f5fd291cf640c19fa0068","@sp":"340d70d04eefbded","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3ACEHKI6:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3ACEHKI6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:04.6333836Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"6e9d581abf4f5fd291cf640c19fa0068","@sp":"340d70d04eefbded","Count":1,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3ACEHKI6:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3ACEHKI6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:04.6411408Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"6e9d581abf4f5fd291cf640c19fa0068","@sp":"340d70d04eefbded","PathCount":17,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3ACEHKI6:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3ACEHKI6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:04.9585007Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:04.9618176Z","@mt":"开始强制刷新ActionDescriptorCollectionProvider...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:04.9640894Z","@mt":"ActionDescriptorCollectionProvider刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:04.9651944Z","@mt":"已使用ActionDescriptorRefreshService刷新ActionDescriptorCollectionProvider","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:04.9672204Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:04.9683653Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:04.9697380Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:05.4748556Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:05.4779646Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:05.4799008Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:05.4808673Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:45.9813199Z","@mt":"收到OpenAPI文档刷新请求","@tr":"39223e2b652a075023d0010bc3cec948","@sp":"6444003056ed0842","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HNER3ACEHKI9:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER3ACEHKI9","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:45.9928719Z","@mt":"开始刷新热加载OpenAPI文档...","@tr":"39223e2b652a075023d0010bc3cec948","@sp":"6444003056ed0842","SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiRefreshService","RequestId":"0HNER3ACEHKI9:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER3ACEHKI9","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:45.9967004Z","@mt":"发现 {Count} 个已加载的模块","@tr":"39223e2b652a075023d0010bc3cec948","@sp":"6444003056ed0842","Count":1,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiRefreshService","RequestId":"0HNER3ACEHKI9:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER3ACEHKI9","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:45.9985274Z","@mt":"模块: {Name}, 控制器数: {ControllerCount}, 动作数: {ActionCount}","@tr":"39223e2b652a075023d0010bc3cec948","@sp":"6444003056ed0842","Name":"Auth","ControllerCount":2,"ActionCount":12,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiRefreshService","RequestId":"0HNER3ACEHKI9:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER3ACEHKI9","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:46.0184706Z","@mt":"热加载OpenAPI文档刷新完成","@tr":"39223e2b652a075023d0010bc3cec948","@sp":"6444003056ed0842","SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiRefreshService","RequestId":"0HNER3ACEHKI9:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER3ACEHKI9","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:09:46.0210699Z","@mt":"已使用热加载服务刷新OpenAPI组件，发现 {Count} 个模块","@tr":"39223e2b652a075023d0010bc3cec948","@sp":"6444003056ed0842","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HNER3ACEHKI9:********","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNER3ACEHKI9","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:13:27.5902900Z","@mt":"发现 {Count} 个模块文件","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:13:27.6015713Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:13:27.6150370Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:13:27.6153371Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:13:27.6166861Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:13:27.6232395Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth","AssemblyName":"SSIC.Modules.Auth, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:13:27.6253168Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:13:27.6263296Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:13:27.6293671Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:13:27.6308085Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:13:27.6402558Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:13:27.6415803Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:13:27.6659557Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:13:27.7365712Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:13:27.7385075Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:13:27.7400016Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:13:27.7415562Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:13:27.7431764Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:13:29.1892929Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:13:29.1915206Z","@mt":"开始强制刷新ActionDescriptorCollectionProvider...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:13:29.1929556Z","@mt":"ActionDescriptorCollectionProvider刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:13:29.1938262Z","@mt":"已使用ActionDescriptorRefreshService刷新ActionDescriptorCollectionProvider","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:13:29.1947722Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:13:29.1953014Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:13:29.1958262Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:13:29.7064533Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:13:29.7085523Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:13:29.7097051Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:13:29.7102153Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:13:50.5735820Z","@mt":"首次请求触发端点刷新","@tr":"28433d22ed21fe974e1565aaba89d280","@sp":"3ad8100b747caa7a","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER3D1T99VB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNER3D1T99VB","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:13:50.5827128Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"28433d22ed21fe974e1565aaba89d280","@sp":"3ad8100b747caa7a","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER3D1T99VB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNER3D1T99VB","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:13:50.6408117Z","@mt":"用户登录请求: {Username}","@tr":"28433d22ed21fe974e1565aaba89d280","@sp":"3ad8100b747caa7a","Username":"admin","SourceContext":"SSIC.Modules.Auth.Controllers.AuthController","ActionId":"d2a5a434-60d2-4552-ab38-a1a69235e9ce","ActionName":"SSIC.Modules.Auth.Controllers.AuthController.Login (SSIC.Modules.Auth)","RequestId":"0HNER3D1T99VB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNER3D1T99VB","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:14:00.4985435Z","@mt":"获取所有角色","@tr":"b7debcc38b7d8172696b66d7963a2f08","@sp":"c0a3c6ab0930cad0","SourceContext":"SSIC.Modules.Auth.Controllers.RoleController","ActionId":"82f7e6b1-a8d0-4937-b77e-81e4d163c9e3","ActionName":"SSIC.Modules.Auth.Controllers.RoleController.GetRoles (SSIC.Modules.Auth)","RequestId":"0HNER3D1T99VC:********","RequestPath":"/api/auth/roles/list","ConnectionId":"0HNER3D1T99VC","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:14:09.2129693Z","@mt":"开始转换OpenAPI文档以包含热加载的模块...","@tr":"b2094351817c256d0dd680ede82481fc","@sp":"64b9b51b9cf83f5b","SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3D1T99VD:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3D1T99VD","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:14:09.2140887Z","@mt":"发现 {Count} 个控制器动作","@tr":"b2094351817c256d0dd680ede82481fc","@sp":"64b9b51b9cf83f5b","Count":13,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3D1T99VD:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3D1T99VD","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:14:09.2151773Z","@mt":"发现 {Count} 个模块","@tr":"b2094351817c256d0dd680ede82481fc","@sp":"64b9b51b9cf83f5b","Count":2,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3D1T99VD:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3D1T99VD","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:14:09.2169528Z","@mt":"OpenAPI文档转换完成，包含 {ModuleCount} 个模块，{ActionCount} 个动作","@tr":"b2094351817c256d0dd680ede82481fc","@sp":"64b9b51b9cf83f5b","ModuleCount":2,"ActionCount":13,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3D1T99VD:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3D1T99VD","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:14:09.2183890Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"b2094351817c256d0dd680ede82481fc","@sp":"64b9b51b9cf83f5b","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3D1T99VD:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3D1T99VD","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:14:09.2205008Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"b2094351817c256d0dd680ede82481fc","@sp":"64b9b51b9cf83f5b","Count":1,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3D1T99VD:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3D1T99VD","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:14:09.2252748Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"b2094351817c256d0dd680ede82481fc","@sp":"64b9b51b9cf83f5b","PathCount":17,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3D1T99VD:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3D1T99VD","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:15:54.4367749Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:15:54.4321301Z","@mt":"发现 {Count} 个模块文件","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:15:54.4553651Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:15:54.4562924Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:15:54.4586509Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:15:54.4631485Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth","AssemblyName":"SSIC.Modules.Auth, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:15:54.4646167Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:15:54.4651899Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:15:54.4678610Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:15:54.4694309Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:15:54.4786795Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:15:54.4796722Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:15:54.5073651Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:15:54.5833596Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:15:54.5847797Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:15:54.5853560Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:15:54.5858637Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:15:54.5863694Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:15:56.0312965Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:15:56.0337488Z","@mt":"开始强制刷新ActionDescriptorCollectionProvider...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:15:56.0349893Z","@mt":"ActionDescriptorCollectionProvider刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:15:56.0355526Z","@mt":"已使用ActionDescriptorRefreshService刷新ActionDescriptorCollectionProvider","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:15:56.0366470Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:15:56.0375963Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:15:56.0383938Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:15:56.5462053Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:15:56.5492157Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:15:56.5514766Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:15:56.5523256Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:16:16.8050240Z","@mt":"首次请求触发端点刷新","@tr":"7ea44dca0efa9c695216cf05f5955cd8","@sp":"0de19d961880e1b9","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER3EDFU8LJ:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3EDFU8LJ","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:16:16.8123054Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"7ea44dca0efa9c695216cf05f5955cd8","@sp":"0de19d961880e1b9","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER3EDFU8LJ:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3EDFU8LJ","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:16:16.8829409Z","@mt":"开始转换OpenAPI文档以包含热加载的模块...","@tr":"7ea44dca0efa9c695216cf05f5955cd8","@sp":"0de19d961880e1b9","SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3EDFU8LJ:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3EDFU8LJ","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:16:16.8839725Z","@mt":"发现 {Count} 个控制器动作","@tr":"7ea44dca0efa9c695216cf05f5955cd8","@sp":"0de19d961880e1b9","Count":13,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3EDFU8LJ:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3EDFU8LJ","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:16:16.8849317Z","@mt":"发现 {Count} 个模块","@tr":"7ea44dca0efa9c695216cf05f5955cd8","@sp":"0de19d961880e1b9","Count":2,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3EDFU8LJ:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3EDFU8LJ","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:16:16.8864149Z","@mt":"OpenAPI文档转换完成，包含 {ModuleCount} 个模块，{ActionCount} 个动作","@tr":"7ea44dca0efa9c695216cf05f5955cd8","@sp":"0de19d961880e1b9","ModuleCount":2,"ActionCount":13,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3EDFU8LJ:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3EDFU8LJ","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:16:16.8879013Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"7ea44dca0efa9c695216cf05f5955cd8","@sp":"0de19d961880e1b9","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3EDFU8LJ:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3EDFU8LJ","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:16:16.8897864Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"7ea44dca0efa9c695216cf05f5955cd8","@sp":"0de19d961880e1b9","Count":1,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3EDFU8LJ:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3EDFU8LJ","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:16:16.8958732Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"7ea44dca0efa9c695216cf05f5955cd8","@sp":"0de19d961880e1b9","PathCount":17,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3EDFU8LJ:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3EDFU8LJ","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:16:27.3991026Z","@mt":"已手动添加/api/Hello路由","@tr":"55d7e5253f85bb2ba9b86562a54ddcc3","@sp":"2c24718832c9e49a","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HNER3EDFU8LK:********","RequestPath":"/api/endpoints/map-module-route","ConnectionId":"0HNER3EDFU8LK","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:16:46.2079448Z","@mt":"收到模块信息查询请求","@tr":"5b01948479489c8d068822773983f6f5","@sp":"8d624d430e7a0932","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HNER3EDFU8LL:********","RequestPath":"/api/openapi/modules","ConnectionId":"0HNER3EDFU8LL","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:16:55.9978586Z","@mt":"用户登录请求: {Username}","@tr":"6dfb24f820e8191b92b888a1207e537a","@sp":"dc3dd912ab56eea9","Username":"admin","SourceContext":"SSIC.Modules.Auth.Controllers.AuthController","ActionId":"ca7798a0-7d73-4c38-8817-6097e229d93e","ActionName":"SSIC.Modules.Auth.Controllers.AuthController.Login (SSIC.Modules.Auth)","RequestId":"0HNER3EDFU8LM:********","RequestPath":"/api/auth/login","ConnectionId":"0HNER3EDFU8LM","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:17:59.5285813Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:17:59.5177783Z","@mt":"发现 {Count} 个模块文件","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:17:59.5445933Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:17:59.5460165Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:17:59.5474041Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:17:59.5532764Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth","AssemblyName":"SSIC.Modules.Auth, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:17:59.5555729Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:17:59.5564086Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:17:59.5599253Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:17:59.5620493Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:17:59.5702483Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:17:59.5713537Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:17:59.5970932Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:17:59.6961789Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:17:59.6974727Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:17:59.6981765Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:17:59.6986696Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:17:59.6992596Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:18:01.1199382Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:18:01.1220198Z","@mt":"开始强制刷新ActionDescriptorCollectionProvider...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:18:01.1234249Z","@mt":"ActionDescriptorCollectionProvider刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:18:01.1239762Z","@mt":"已使用ActionDescriptorRefreshService刷新ActionDescriptorCollectionProvider","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:18:01.1250547Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:18:01.1256188Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:18:01.1262164Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:18:01.6364767Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:18:01.6388503Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:18:01.6403152Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:18:01.6410236Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:18:22.7262753Z","@mt":"首次请求触发端点刷新","@tr":"245e19d3bdc6979d42f2587bd8d87c98","@sp":"933408530fe85036","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER3FJ0R3VN:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3FJ0R3VN","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:18:22.7326182Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"245e19d3bdc6979d42f2587bd8d87c98","@sp":"933408530fe85036","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER3FJ0R3VN:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3FJ0R3VN","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:18:22.8011692Z","@mt":"开始转换OpenAPI文档以包含热加载的模块...","@tr":"245e19d3bdc6979d42f2587bd8d87c98","@sp":"933408530fe85036","SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3FJ0R3VN:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3FJ0R3VN","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:18:22.8020124Z","@mt":"发现 {Count} 个控制器动作","@tr":"245e19d3bdc6979d42f2587bd8d87c98","@sp":"933408530fe85036","Count":13,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3FJ0R3VN:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3FJ0R3VN","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:18:22.8029248Z","@mt":"发现 {Count} 个模块","@tr":"245e19d3bdc6979d42f2587bd8d87c98","@sp":"933408530fe85036","Count":2,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3FJ0R3VN:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3FJ0R3VN","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:18:22.8043904Z","@mt":"OpenAPI文档转换完成，包含 {ModuleCount} 个模块，{ActionCount} 个动作","@tr":"245e19d3bdc6979d42f2587bd8d87c98","@sp":"933408530fe85036","ModuleCount":2,"ActionCount":13,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3FJ0R3VN:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3FJ0R3VN","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:18:22.8056258Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"245e19d3bdc6979d42f2587bd8d87c98","@sp":"933408530fe85036","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3FJ0R3VN:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3FJ0R3VN","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:18:22.8070266Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"245e19d3bdc6979d42f2587bd8d87c98","@sp":"933408530fe85036","Count":1,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3FJ0R3VN:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3FJ0R3VN","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:18:22.8123658Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"245e19d3bdc6979d42f2587bd8d87c98","@sp":"933408530fe85036","PathCount":17,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3FJ0R3VN:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3FJ0R3VN","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:18:35.8752305Z","@mt":"用户登录请求: {Username}","@tr":"6cad02a9fb9b04e568b54720a7a93816","@sp":"226718ec1d569994","Username":"admin","SourceContext":"SSIC.Modules.Auth.Controllers.AuthController","ActionId":"8b7eb1f7-8da0-4873-94bf-21035c4251b3","ActionName":"SSIC.Modules.Auth.Controllers.AuthController.Login (SSIC.Modules.Auth)","RequestId":"0HNER3FJ0R3VO:********","RequestPath":"/api/auth/login","ConnectionId":"0HNER3FJ0R3VO","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:18:46.0013817Z","@mt":"开始转换OpenAPI文档以包含热加载的模块...","@tr":"4d92131be7a6480030e3ed110a4171ca","@sp":"2c824684c217de58","SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3FJ0R3VP:00000007","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3FJ0R3VP","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:18:46.0020957Z","@mt":"发现 {Count} 个控制器动作","@tr":"4d92131be7a6480030e3ed110a4171ca","@sp":"2c824684c217de58","Count":13,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3FJ0R3VP:00000007","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3FJ0R3VP","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:18:46.0028970Z","@mt":"发现 {Count} 个模块","@tr":"4d92131be7a6480030e3ed110a4171ca","@sp":"2c824684c217de58","Count":2,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3FJ0R3VP:00000007","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3FJ0R3VP","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:18:46.0038238Z","@mt":"OpenAPI文档转换完成，包含 {ModuleCount} 个模块，{ActionCount} 个动作","@tr":"4d92131be7a6480030e3ed110a4171ca","@sp":"2c824684c217de58","ModuleCount":2,"ActionCount":13,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3FJ0R3VP:00000007","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3FJ0R3VP","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:18:46.0047424Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"4d92131be7a6480030e3ed110a4171ca","@sp":"2c824684c217de58","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3FJ0R3VP:00000007","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3FJ0R3VP","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:18:46.0062885Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"4d92131be7a6480030e3ed110a4171ca","@sp":"2c824684c217de58","Count":1,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3FJ0R3VP:00000007","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3FJ0R3VP","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:18:46.0085015Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"4d92131be7a6480030e3ed110a4171ca","@sp":"2c824684c217de58","PathCount":17,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3FJ0R3VP:00000007","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3FJ0R3VP","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:48.2317561Z","@mt":"发现 {Count} 个模块文件","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:48.2724978Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:48.2864483Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:48.2865871Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:48.3467244Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:48.3676726Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth","AssemblyName":"SSIC.Modules.Auth, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:48.3713161Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:48.3729568Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:48.3988749Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:48.4011593Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:48.4137548Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:48.4147375Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:48.4717464Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:48.6116222Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:48.6140885Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:48.6450420Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:48.6461781Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:48.6471496Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:49.5847373Z","@mt":"首次请求触发端点刷新","@tr":"e9813aff0d9a0ed7a3a45f4848a81ab2","@sp":"73d16c0367b08e0b","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER3GUL7CA9:********","RequestPath":"/scalar/v1","ConnectionId":"0HNER3GUL7CA9","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:49.5883731Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"e9813aff0d9a0ed7a3a45f4848a81ab2","@sp":"73d16c0367b08e0b","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER3GUL7CA9:********","RequestPath":"/scalar/v1","ConnectionId":"0HNER3GUL7CA9","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:49.9480344Z","@mt":"开始转换OpenAPI文档以包含热加载的模块...","@tr":"f4b223391ec3384f7a57674cc9a8c325","@sp":"46a79d3b2e230a27","SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3GUL7CA9:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3GUL7CA9","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:49.9497417Z","@mt":"发现 {Count} 个控制器动作","@tr":"f4b223391ec3384f7a57674cc9a8c325","@sp":"46a79d3b2e230a27","Count":13,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3GUL7CA9:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3GUL7CA9","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:49.9518117Z","@mt":"发现 {Count} 个模块","@tr":"f4b223391ec3384f7a57674cc9a8c325","@sp":"46a79d3b2e230a27","Count":2,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3GUL7CA9:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3GUL7CA9","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:49.9556066Z","@mt":"OpenAPI文档转换完成，包含 {ModuleCount} 个模块，{ActionCount} 个动作","@tr":"f4b223391ec3384f7a57674cc9a8c325","@sp":"46a79d3b2e230a27","ModuleCount":2,"ActionCount":13,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3GUL7CA9:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3GUL7CA9","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:49.9597263Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"f4b223391ec3384f7a57674cc9a8c325","@sp":"46a79d3b2e230a27","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3GUL7CA9:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3GUL7CA9","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:49.9632959Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"f4b223391ec3384f7a57674cc9a8c325","@sp":"46a79d3b2e230a27","Count":1,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3GUL7CA9:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3GUL7CA9","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:49.9709261Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"f4b223391ec3384f7a57674cc9a8c325","@sp":"46a79d3b2e230a27","PathCount":17,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3GUL7CA9:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3GUL7CA9","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:50.0034119Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:50.0059857Z","@mt":"开始强制刷新ActionDescriptorCollectionProvider...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:50.0075434Z","@mt":"ActionDescriptorCollectionProvider刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:50.0082594Z","@mt":"已使用ActionDescriptorRefreshService刷新ActionDescriptorCollectionProvider","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:50.0100363Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:50.0111083Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:50.0124892Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:50.5185499Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:50.5216785Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:50.5239535Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:20:50.5251270Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:21:44.4610965Z","@mt":"收到模块信息查询请求","@tr":"53e49bc44bfb3e130b87d7abc4278327","@sp":"f101c31f7ad80504","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HNER3GUL7CAA:********","RequestPath":"/api/openapi/modules","ConnectionId":"0HNER3GUL7CAA","MachineName":"LAPTOP-F6SGUTN5","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:19.1795537Z","@mt":"发现 {Count} 个模块文件","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:19.1924211Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:19.2062481Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:19.2074401Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:19.2087041Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:19.2158071Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth","AssemblyName":"SSIC.Modules.Auth, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:19.2175642Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:19.2184658Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:19.2222228Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:19.2237892Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:19.2323956Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:19.2334604Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:19.2581428Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:19.3359001Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:19.3371730Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:19.3378800Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:19.3383375Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:19.3389219Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:20.7762266Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:20.7786468Z","@mt":"开始强制刷新ActionDescriptorCollectionProvider...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:20.7803357Z","@mt":"ActionDescriptorCollectionProvider刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:20.7813315Z","@mt":"已使用ActionDescriptorRefreshService刷新ActionDescriptorCollectionProvider","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:20.7826855Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:20.7839234Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:20.7850839Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:21.2896525Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:21.2926026Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:21.2939592Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:21.2946879Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:39.6059791Z","@mt":"首次请求触发端点刷新","@tr":"4636161a3d4593110bc01be6423c4fe0","@sp":"2eae281a3b274a00","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER3IHEQP3F:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3IHEQP3F","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:39.6139614Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"4636161a3d4593110bc01be6423c4fe0","@sp":"2eae281a3b274a00","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER3IHEQP3F:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3IHEQP3F","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:39.6765628Z","@mt":"开始转换OpenAPI文档以包含热加载的模块...","@tr":"4636161a3d4593110bc01be6423c4fe0","@sp":"2eae281a3b274a00","SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3IHEQP3F:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3IHEQP3F","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:39.6781702Z","@mt":"发现 {Count} 个控制器动作","@tr":"4636161a3d4593110bc01be6423c4fe0","@sp":"2eae281a3b274a00","Count":13,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3IHEQP3F:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3IHEQP3F","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:39.6800309Z","@mt":"发现 {Count} 个模块","@tr":"4636161a3d4593110bc01be6423c4fe0","@sp":"2eae281a3b274a00","Count":2,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3IHEQP3F:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3IHEQP3F","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:39.6822561Z","@mt":"OpenAPI文档转换完成，包含 {ModuleCount} 个模块，{ActionCount} 个动作","@tr":"4636161a3d4593110bc01be6423c4fe0","@sp":"2eae281a3b274a00","ModuleCount":2,"ActionCount":13,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3IHEQP3F:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3IHEQP3F","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:39.6838111Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"4636161a3d4593110bc01be6423c4fe0","@sp":"2eae281a3b274a00","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3IHEQP3F:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3IHEQP3F","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:39.6852469Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"4636161a3d4593110bc01be6423c4fe0","@sp":"2eae281a3b274a00","Count":1,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3IHEQP3F:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3IHEQP3F","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:39.6889318Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"4636161a3d4593110bc01be6423c4fe0","@sp":"2eae281a3b274a00","PathCount":15,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3IHEQP3F:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3IHEQP3F","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:23:55.5879080Z","@mt":"用户登录请求: {Username}","@tr":"bacd7cb0a57086d5cead9c9184663a7b","@sp":"70fa07d59107d2c1","Username":"admin","SourceContext":"SSIC.Modules.Auth.Controllers.AuthController","ActionId":"4c154fcc-ca87-48c3-b97c-9f67e85d16ca","ActionName":"SSIC.Modules.Auth.Controllers.AuthController.Login (SSIC.Modules.Auth)","RequestId":"0HNER3IHEQP3G:********","RequestPath":"/api/auth/login","ConnectionId":"0HNER3IHEQP3G","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:24:02.9219421Z","@mt":"获取所有角色","@tr":"071fd4370cd252aef57853bae2b1f6a3","@sp":"e6db9f05fe1d7012","SourceContext":"SSIC.Modules.Auth.Controllers.RoleController","ActionId":"b88b3eb8-e40c-47f3-a84f-38b8b9c12574","ActionName":"SSIC.Modules.Auth.Controllers.RoleController.GetRoles (SSIC.Modules.Auth)","RequestId":"0HNER3IHEQP3H:********","RequestPath":"/api/auth/roles/list","ConnectionId":"0HNER3IHEQP3H","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:24:09.5682838Z","@mt":"开始转换OpenAPI文档以包含热加载的模块...","@tr":"7f357c11338d26b1edbdc07400d4ec24","@sp":"b75a83ea423856ed","SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3IHEQP3I:00000007","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3IHEQP3I","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:24:09.5693410Z","@mt":"发现 {Count} 个控制器动作","@tr":"7f357c11338d26b1edbdc07400d4ec24","@sp":"b75a83ea423856ed","Count":13,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3IHEQP3I:00000007","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3IHEQP3I","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:24:09.5709827Z","@mt":"发现 {Count} 个模块","@tr":"7f357c11338d26b1edbdc07400d4ec24","@sp":"b75a83ea423856ed","Count":2,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3IHEQP3I:00000007","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3IHEQP3I","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:24:09.5720278Z","@mt":"OpenAPI文档转换完成，包含 {ModuleCount} 个模块，{ActionCount} 个动作","@tr":"7f357c11338d26b1edbdc07400d4ec24","@sp":"b75a83ea423856ed","ModuleCount":2,"ActionCount":13,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3IHEQP3I:00000007","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3IHEQP3I","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:24:09.5730902Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"7f357c11338d26b1edbdc07400d4ec24","@sp":"b75a83ea423856ed","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3IHEQP3I:00000007","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3IHEQP3I","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:24:09.5746059Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"7f357c11338d26b1edbdc07400d4ec24","@sp":"b75a83ea423856ed","Count":1,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3IHEQP3I:00000007","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3IHEQP3I","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:24:09.5760300Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"7f357c11338d26b1edbdc07400d4ec24","@sp":"b75a83ea423856ed","PathCount":15,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3IHEQP3I:00000007","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3IHEQP3I","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:24:43.5298188Z","@mt":"开始转换OpenAPI文档以包含热加载的模块...","@tr":"26a32ae32e3b2a7485c4f985a0a2c343","@sp":"81b257ef0ea0518a","SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3IHEQP3K:00000007","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3IHEQP3K","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:24:43.5306761Z","@mt":"发现 {Count} 个控制器动作","@tr":"26a32ae32e3b2a7485c4f985a0a2c343","@sp":"81b257ef0ea0518a","Count":13,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3IHEQP3K:00000007","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3IHEQP3K","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:24:43.5315101Z","@mt":"发现 {Count} 个模块","@tr":"26a32ae32e3b2a7485c4f985a0a2c343","@sp":"81b257ef0ea0518a","Count":2,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3IHEQP3K:00000007","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3IHEQP3K","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:24:43.5322192Z","@mt":"OpenAPI文档转换完成，包含 {ModuleCount} 个模块，{ActionCount} 个动作","@tr":"26a32ae32e3b2a7485c4f985a0a2c343","@sp":"81b257ef0ea0518a","ModuleCount":2,"ActionCount":13,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3IHEQP3K:00000007","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3IHEQP3K","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:24:43.5331736Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"26a32ae32e3b2a7485c4f985a0a2c343","@sp":"81b257ef0ea0518a","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3IHEQP3K:00000007","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3IHEQP3K","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:24:43.5344789Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"26a32ae32e3b2a7485c4f985a0a2c343","@sp":"81b257ef0ea0518a","Count":1,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3IHEQP3K:00000007","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3IHEQP3K","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:24:43.5357728Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"26a32ae32e3b2a7485c4f985a0a2c343","@sp":"81b257ef0ea0518a","PathCount":15,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3IHEQP3K:00000007","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3IHEQP3K","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:54.8853595Z","@mt":"发现 {Count} 个模块文件","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:54.9237663Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:54.9370194Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:54.9376219Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:54.9435177Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:54.9958597Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth","AssemblyName":"SSIC.Modules.Auth, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:54.9984266Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:54.9994458Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:55.0076941Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:55.0115113Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:55.0599617Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:55.0610250Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:55.1300857Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:55.2912468Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:55.2938310Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:55.3261524Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:55.3270711Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:55.3280176Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:56.1772252Z","@mt":"首次请求触发端点刷新","@tr":"d1fbf65c0337ffbfd676c30ee91de989","@sp":"4eb1c592f3496b8e","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER3JQ1K5FA:********","RequestPath":"/scalar/v1","ConnectionId":"0HNER3JQ1K5FA","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:56.1814103Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"d1fbf65c0337ffbfd676c30ee91de989","@sp":"4eb1c592f3496b8e","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER3JQ1K5FA:********","RequestPath":"/scalar/v1","ConnectionId":"0HNER3JQ1K5FA","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:56.5742955Z","@mt":"开始转换OpenAPI文档以包含热加载的模块...","@tr":"7ac0f104eaccdffe31f195e270a14abe","@sp":"ca8985d2b6f48136","SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3JQ1K5FA:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3JQ1K5FA","MachineName":"LAPTOP-F6SGUTN5","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:56.5760433Z","@mt":"发现 {Count} 个控制器动作","@tr":"7ac0f104eaccdffe31f195e270a14abe","@sp":"ca8985d2b6f48136","Count":13,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3JQ1K5FA:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3JQ1K5FA","MachineName":"LAPTOP-F6SGUTN5","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:56.5780716Z","@mt":"发现 {Count} 个模块","@tr":"7ac0f104eaccdffe31f195e270a14abe","@sp":"ca8985d2b6f48136","Count":2,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3JQ1K5FA:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3JQ1K5FA","MachineName":"LAPTOP-F6SGUTN5","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:56.5831761Z","@mt":"OpenAPI文档转换完成，包含 {ModuleCount} 个模块，{ActionCount} 个动作","@tr":"7ac0f104eaccdffe31f195e270a14abe","@sp":"ca8985d2b6f48136","ModuleCount":2,"ActionCount":13,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3JQ1K5FA:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3JQ1K5FA","MachineName":"LAPTOP-F6SGUTN5","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:56.5863801Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"7ac0f104eaccdffe31f195e270a14abe","@sp":"ca8985d2b6f48136","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3JQ1K5FA:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3JQ1K5FA","MachineName":"LAPTOP-F6SGUTN5","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:56.5898459Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"7ac0f104eaccdffe31f195e270a14abe","@sp":"ca8985d2b6f48136","Count":1,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3JQ1K5FA:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3JQ1K5FA","MachineName":"LAPTOP-F6SGUTN5","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:56.6042073Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"7ac0f104eaccdffe31f195e270a14abe","@sp":"ca8985d2b6f48136","PathCount":15,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3JQ1K5FA:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3JQ1K5FA","MachineName":"LAPTOP-F6SGUTN5","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:56.6534080Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:56.6572738Z","@mt":"开始强制刷新ActionDescriptorCollectionProvider...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:56.6591862Z","@mt":"ActionDescriptorCollectionProvider刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:56.6603883Z","@mt":"已使用ActionDescriptorRefreshService刷新ActionDescriptorCollectionProvider","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:56.6622236Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:56.6634218Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:56.6647213Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:57.1788752Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:57.1829473Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:57.1848038Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:25:57.1856130Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:03.0094248Z","@mt":"发现 {Count} 个模块文件","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:03.0202502Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:03.0334877Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:03.0340034Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:03.0354723Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:03.0426084Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth","AssemblyName":"SSIC.Modules.Auth, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:03.0453394Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:03.0462497Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:03.0488077Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:03.0505528Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:03.0590044Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:03.0599478Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:03.0837503Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:03.1492895Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:03.1506684Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:03.1515797Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:03.1525515Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:03.1532985Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:04.6041956Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:04.6068302Z","@mt":"开始强制刷新ActionDescriptorCollectionProvider...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:04.6087747Z","@mt":"ActionDescriptorCollectionProvider刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:04.6097221Z","@mt":"已使用ActionDescriptorRefreshService刷新ActionDescriptorCollectionProvider","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:04.6110620Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:04.6121462Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:04.6127442Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:05.1163074Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:05.1193152Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:05.1208672Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:05.1214487Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:25.0219092Z","@mt":"首次请求触发端点刷新","@tr":"c206edb5020e54fb00c35d197be7a2e6","@sp":"c3530422d88af8b7","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER3MA92ABU:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3MA92ABU","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:25.0295220Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"c206edb5020e54fb00c35d197be7a2e6","@sp":"c3530422d88af8b7","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER3MA92ABU:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3MA92ABU","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:25.1139421Z","@mt":"开始转换OpenAPI文档以包含热加载的模块...","@tr":"c206edb5020e54fb00c35d197be7a2e6","@sp":"c3530422d88af8b7","SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3MA92ABU:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3MA92ABU","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:25.1152284Z","@mt":"发现 {Count} 个控制器动作","@tr":"c206edb5020e54fb00c35d197be7a2e6","@sp":"c3530422d88af8b7","Count":13,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3MA92ABU:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3MA92ABU","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:25.1171250Z","@mt":"发现 {Count} 个模块","@tr":"c206edb5020e54fb00c35d197be7a2e6","@sp":"c3530422d88af8b7","Count":2,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3MA92ABU:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3MA92ABU","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:25.1215913Z","@mt":"OpenAPI文档转换完成，包含 {ModuleCount} 个模块，{ActionCount} 个动作","@tr":"c206edb5020e54fb00c35d197be7a2e6","@sp":"c3530422d88af8b7","ModuleCount":2,"ActionCount":13,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3MA92ABU:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3MA92ABU","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:25.1246734Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"c206edb5020e54fb00c35d197be7a2e6","@sp":"c3530422d88af8b7","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3MA92ABU:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3MA92ABU","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:25.1272718Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"c206edb5020e54fb00c35d197be7a2e6","@sp":"c3530422d88af8b7","Count":1,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3MA92ABU:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3MA92ABU","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:25.1320305Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"c206edb5020e54fb00c35d197be7a2e6","@sp":"c3530422d88af8b7","PathCount":15,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3MA92ABU:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3MA92ABU","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:38.0143537Z","@mt":"用户登录请求: {Username}","@tr":"f303aa3e573b4c6d14c7154e4251c467","@sp":"ccef7b55ab9f5fe7","Username":"admin","SourceContext":"SSIC.Modules.Auth.Controllers.AuthController","ActionId":"d806661b-c1e5-432c-89b4-92d89d93440c","ActionName":"SSIC.Modules.Auth.Controllers.AuthController.Login (SSIC.Modules.Auth)","RequestId":"0HNER3MA92ABV:********","RequestPath":"/api/auth/login","ConnectionId":"0HNER3MA92ABV","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:45.6020281Z","@mt":"获取所有角色","@tr":"e18b6882cff7027096e5b72ff5be7488","@sp":"6d110c327930ec5b","SourceContext":"SSIC.Modules.Auth.Controllers.RoleController","ActionId":"a9080d67-3248-420e-bfa1-d15c75ac442d","ActionName":"SSIC.Modules.Auth.Controllers.RoleController.GetRoles (SSIC.Modules.Auth)","RequestId":"0HNER3MA92AC0:********","RequestPath":"/api/auth/roles/list","ConnectionId":"0HNER3MA92AC0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:56.4344613Z","@mt":"开始转换OpenAPI文档以包含热加载的模块...","@tr":"1c467a3cf6a89cb99d87f0eb64b6624f","@sp":"6d0b1a1eca582f35","SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3MA92AC1:00000007","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3MA92AC1","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:56.4356176Z","@mt":"发现 {Count} 个控制器动作","@tr":"1c467a3cf6a89cb99d87f0eb64b6624f","@sp":"6d0b1a1eca582f35","Count":13,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3MA92AC1:00000007","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3MA92AC1","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:56.4367514Z","@mt":"发现 {Count} 个模块","@tr":"1c467a3cf6a89cb99d87f0eb64b6624f","@sp":"6d0b1a1eca582f35","Count":2,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3MA92AC1:00000007","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3MA92AC1","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:56.4377240Z","@mt":"OpenAPI文档转换完成，包含 {ModuleCount} 个模块，{ActionCount} 个动作","@tr":"1c467a3cf6a89cb99d87f0eb64b6624f","@sp":"6d0b1a1eca582f35","ModuleCount":2,"ActionCount":13,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3MA92AC1:00000007","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3MA92AC1","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:56.4388595Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"1c467a3cf6a89cb99d87f0eb64b6624f","@sp":"6d0b1a1eca582f35","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3MA92AC1:00000007","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3MA92AC1","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:56.4402135Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"1c467a3cf6a89cb99d87f0eb64b6624f","@sp":"6d0b1a1eca582f35","Count":1,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3MA92AC1:00000007","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3MA92AC1","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:30:56.4417539Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"1c467a3cf6a89cb99d87f0eb64b6624f","@sp":"6d0b1a1eca582f35","PathCount":15,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3MA92AC1:00000007","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3MA92AC1","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:31:05.0186368Z","@mt":"获取所有角色","@tr":"1acb5e1a4939d535688aa69f5679eb67","@sp":"f325cd2237a2874f","SourceContext":"SSIC.Modules.Auth.Controllers.RoleController","ActionId":"a9080d67-3248-420e-bfa1-d15c75ac442d","ActionName":"SSIC.Modules.Auth.Controllers.RoleController.GetRoles (SSIC.Modules.Auth)","RequestId":"0HNER3MA92AC1:00000009","RequestPath":"/api/auth/roles/list","ConnectionId":"0HNER3MA92AC1","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:32:58.9488423Z","@mt":"发现 {Count} 个模块文件","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:32:58.9934438Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:32:59.0280057Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:32:59.0281878Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:32:59.0296043Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:32:59.1116297Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth","AssemblyName":"SSIC.Modules.Auth, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:32:59.1455626Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:32:59.1466159Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:32:59.1515282Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:32:59.1536009Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:32:59.1640777Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:32:59.1658620Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:32:59.2260527Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:32:59.3624775Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:32:59.3652253Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:32:59.3926021Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:32:59.3936881Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:32:59.3944425Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:32:59.8830878Z","@mt":"首次请求触发端点刷新","@tr":"979d5b809524b6aad0ad9c2db2c914c0","@sp":"7313c9ea6096cb18","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER3NOA34AS:********","RequestPath":"/scalar/v1","ConnectionId":"0HNER3NOA34AS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:32:59.8876503Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"979d5b809524b6aad0ad9c2db2c914c0","@sp":"7313c9ea6096cb18","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER3NOA34AS:********","RequestPath":"/scalar/v1","ConnectionId":"0HNER3NOA34AS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:33:00.3217109Z","@mt":"开始转换OpenAPI文档以包含热加载的模块...","@tr":"470803154ace6e26ddf9a5289f055294","@sp":"5a6fae70f6d75dbe","SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3NOA34AS:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3NOA34AS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:33:00.3234517Z","@mt":"发现 {Count} 个控制器动作","@tr":"470803154ace6e26ddf9a5289f055294","@sp":"5a6fae70f6d75dbe","Count":13,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3NOA34AS:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3NOA34AS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:33:00.3260493Z","@mt":"发现 {Count} 个模块","@tr":"470803154ace6e26ddf9a5289f055294","@sp":"5a6fae70f6d75dbe","Count":2,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3NOA34AS:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3NOA34AS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:33:00.3296705Z","@mt":"OpenAPI文档转换完成，包含 {ModuleCount} 个模块，{ActionCount} 个动作","@tr":"470803154ace6e26ddf9a5289f055294","@sp":"5a6fae70f6d75dbe","ModuleCount":2,"ActionCount":13,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3NOA34AS:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3NOA34AS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:33:00.3341992Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"470803154ace6e26ddf9a5289f055294","@sp":"5a6fae70f6d75dbe","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3NOA34AS:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3NOA34AS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:33:00.3396711Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"470803154ace6e26ddf9a5289f055294","@sp":"5a6fae70f6d75dbe","Count":1,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3NOA34AS:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3NOA34AS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:33:00.3454606Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"470803154ace6e26ddf9a5289f055294","@sp":"5a6fae70f6d75dbe","PathCount":15,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3NOA34AS:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3NOA34AS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:33:00.7385519Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:33:00.7422539Z","@mt":"开始强制刷新ActionDescriptorCollectionProvider...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:33:00.7450350Z","@mt":"ActionDescriptorCollectionProvider刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:33:00.7461136Z","@mt":"已使用ActionDescriptorRefreshService刷新ActionDescriptorCollectionProvider","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:33:00.7478731Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:33:00.7491912Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:33:00.7507853Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:33:01.2668879Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:33:01.2717575Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:33:01.2736665Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:33:01.2744032Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:20.2135392Z","@mt":"发现 {Count} 个模块文件","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:20.2274224Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:20.2414746Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:20.2417393Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:20.2433841Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:20.7467854Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth","AssemblyName":"SSIC.Modules.Auth, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:20.7505167Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:20.7521151Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:20.7557513Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:20.7579488Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:20.7681483Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:20.7692558Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:20.7961440Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:20.8761165Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:20.8774537Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:20.8781362Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:20.8786539Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:20.8793584Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:22.3200622Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:22.3229865Z","@mt":"开始强制刷新ActionDescriptorCollectionProvider...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:22.3251533Z","@mt":"ActionDescriptorCollectionProvider刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:22.3266003Z","@mt":"已使用ActionDescriptorRefreshService刷新ActionDescriptorCollectionProvider","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:22.3281785Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:22.3293143Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:22.3321985Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:22.8332977Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:22.8357921Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:22.8372270Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:22.8377640Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:42.2850976Z","@mt":"首次请求触发端点刷新","@tr":"dc6b2f23dfc317aebd861e6a06cf4624","@sp":"9633367db792987d","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER3OMUJN5P:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3OMUJN5P","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:42.2923511Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"dc6b2f23dfc317aebd861e6a06cf4624","@sp":"9633367db792987d","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER3OMUJN5P:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3OMUJN5P","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:42.3442254Z","@mt":"开始转换OpenAPI文档以包含热加载的模块...","@tr":"dc6b2f23dfc317aebd861e6a06cf4624","@sp":"9633367db792987d","SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3OMUJN5P:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3OMUJN5P","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:42.3461465Z","@mt":"发现 {Count} 个控制器动作","@tr":"dc6b2f23dfc317aebd861e6a06cf4624","@sp":"9633367db792987d","Count":13,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3OMUJN5P:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3OMUJN5P","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:42.3485809Z","@mt":"发现 {Count} 个模块","@tr":"dc6b2f23dfc317aebd861e6a06cf4624","@sp":"9633367db792987d","Count":2,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3OMUJN5P:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3OMUJN5P","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:42.3504450Z","@mt":"OpenAPI文档转换完成，包含 {ModuleCount} 个模块，{ActionCount} 个动作","@tr":"dc6b2f23dfc317aebd861e6a06cf4624","@sp":"9633367db792987d","ModuleCount":2,"ActionCount":13,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3OMUJN5P:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3OMUJN5P","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:42.3528848Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"dc6b2f23dfc317aebd861e6a06cf4624","@sp":"9633367db792987d","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3OMUJN5P:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3OMUJN5P","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:42.3553707Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"dc6b2f23dfc317aebd861e6a06cf4624","@sp":"9633367db792987d","Count":1,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3OMUJN5P:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3OMUJN5P","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:34:42.3596553Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"dc6b2f23dfc317aebd861e6a06cf4624","@sp":"9633367db792987d","PathCount":15,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3OMUJN5P:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3OMUJN5P","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:35:08.7531808Z","@mt":"手动API触发端点刷新完成，发现 {Count} 个模块","@tr":"e65e3e901c5758a5fee386b5b0ba5fe2","@sp":"dc2121becf6cb280","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HNER3OMUJN5S:********","RequestPath":"/api/endpoints/refresh","ConnectionId":"0HNER3OMUJN5S","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:35:25.5525262Z","@mt":"已手动添加/api/Hello路由","@tr":"e9ea9b7c180ef4bdcd78b4daa5e35f7d","@sp":"6e90d888e1319826","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HNER3OMUJN5U:********","RequestPath":"/api/endpoints/map-module-route","ConnectionId":"0HNER3OMUJN5U","MachineName":"LAPTOP-F6SGUTN5","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:36:20.9581624Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Dapr.Client.InvocationException: An exception occurred while invoking method: 'WeatherForecast/Get123/123' on app-id: 'ssic-devtools'\r\n ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:3500)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.<SendAsync>g__CreateHandlerAndSendAsync|115_0(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)\r\n   at Dapr.Client.DaprClientGrpc.InvokeMethodWithResponseAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at Dapr.Client.DaprClientGrpc.InvokeMethodWithResponseAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Dapr.Client.DaprClientGrpc.InvokeMethodAsync[TResponse](HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at SSIC.Infrastructure.Dapr.DaprClientProvider.InvokeServiceWithLoadBalancingAsync[TRequest,TResponse](HttpMethod httpMethod, String appId, String methodName, TRequest data) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Dapr\\DaprClientProvider.cs:line 34\r\n   at SSIC.HostServer.Controllers.WeatherForecast2Controller.Get() in F:\\SSIC\\Host\\SSIC.HostServer\\Controllers\\WeatherForecastController.cs:line 46\r\n   at lambda_method47(Closure, Object)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 94\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"b2ad7e4d63fffb5649473a47ac0ec8cf","@sp":"ad68ec8889e0db67","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNER3OMUJN60:********","RequestPath":"/WeatherForecast2","ConnectionId":"0HNER3OMUJN60","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:36:41.5833793Z","@mt":"收到模块信息查询请求","@tr":"e3e453457604cca8b7535ae696b45fba","@sp":"d28ac1aece2b28fb","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HNER3OMUJN61:********","RequestPath":"/api/openapi/modules","ConnectionId":"0HNER3OMUJN61","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:03.8843627Z","@mt":"手动API触发端点刷新完成，发现 {Count} 个模块","@tr":"09134814d2464916e41e0b23752360ba","@sp":"d8610641d1c960ab","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HNER3OMUJN64:********","RequestPath":"/api/endpoints/refresh","ConnectionId":"0HNER3OMUJN64","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:21.2956527Z","@mt":"开始转换OpenAPI文档以包含热加载的模块...","@tr":"21d74882309914bd2cef26c7197d8573","@sp":"135b689bcbcc30b1","SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3OMUJN66:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3OMUJN66","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:21.2968864Z","@mt":"发现 {Count} 个控制器动作","@tr":"21d74882309914bd2cef26c7197d8573","@sp":"135b689bcbcc30b1","Count":13,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3OMUJN66:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3OMUJN66","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:21.2980649Z","@mt":"发现 {Count} 个模块","@tr":"21d74882309914bd2cef26c7197d8573","@sp":"135b689bcbcc30b1","Count":2,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3OMUJN66:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3OMUJN66","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:21.2986162Z","@mt":"OpenAPI文档转换完成，包含 {ModuleCount} 个模块，{ActionCount} 个动作","@tr":"21d74882309914bd2cef26c7197d8573","@sp":"135b689bcbcc30b1","ModuleCount":2,"ActionCount":13,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNER3OMUJN66:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3OMUJN66","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:21.2994686Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"21d74882309914bd2cef26c7197d8573","@sp":"135b689bcbcc30b1","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3OMUJN66:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3OMUJN66","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:21.3004644Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"21d74882309914bd2cef26c7197d8573","@sp":"135b689bcbcc30b1","Count":1,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3OMUJN66:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3OMUJN66","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:21.3013376Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"21d74882309914bd2cef26c7197d8573","@sp":"135b689bcbcc30b1","PathCount":15,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNER3OMUJN66:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3OMUJN66","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:43.4086977Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:43.4014085Z","@mt":"发现 {Count} 个模块文件","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:43.4244077Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:43.4253902Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:43.4287880Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:44.0022109Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth","AssemblyName":"SSIC.Modules.Auth, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:44.0048379Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:44.0068588Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:44.0142678Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:44.0162608Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:44.0262556Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:44.0275499Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:44.0392084Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:44.1227737Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:44.1238247Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:44.1244170Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:44.1249864Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:44.1255144Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:45.5531000Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:45.5555818Z","@mt":"开始强制刷新ActionDescriptorCollectionProvider...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:45.5569496Z","@mt":"ActionDescriptorCollectionProvider刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:45.5580647Z","@mt":"已使用ActionDescriptorRefreshService刷新ActionDescriptorCollectionProvider","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:45.5597911Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:45.5610729Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:45.5621226Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:46.0672403Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:46.0699634Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:46.0715853Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:38:46.0723840Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:39:06.3599581Z","@mt":"首次请求触发端点刷新","@tr":"ee72b7dcb58f23ac71f6a26ce281f9a4","@sp":"5adc49ca3be82a59","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER3R5L3FMF:********","RequestPath":"/api/auth/role/test","ConnectionId":"0HNER3R5L3FMF","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:39:06.3651193Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"ee72b7dcb58f23ac71f6a26ce281f9a4","@sp":"5adc49ca3be82a59","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER3R5L3FMF:********","RequestPath":"/api/auth/role/test","ConnectionId":"0HNER3R5L3FMF","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:39:06.3694465Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.InvalidOperationException: Action 'SSIC.Modules.Auth.Controllers.RoleController.CreateRole (SSIC.Modules.Auth)' does not have an attribute route. Action methods on controllers annotated with ApiControllerAttribute must be attribute routed.\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.EnsureActionIsAttributeRouted(ActionModel actionModel)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.OnProvidersExecuting(ApplicationModelProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApplicationModelFactory.CreateApplicationModel(IEnumerable`1 controllerTypes)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.GetDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.OnProvidersExecuting(ActionDescriptorProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.UpdateCollection()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.get_ActionDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.UpdateEndpoints()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.GetChangeToken()\r\n   at Microsoft.Extensions.Primitives.ChangeToken.ChangeTokenRegistration`1..ctor(Func`1 changeTokenProducer, Action`1 changeTokenConsumer, TState state)\r\n   at Microsoft.Extensions.Primitives.ChangeToken.OnChange(Func`1 changeTokenProducer, Action changeTokenConsumer)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.CreateChangeTokenUnsynchronized(Boolean collectionChanged)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.EnsureChangeTokenInitialized()\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.GetChangeToken()\r\n   at Microsoft.AspNetCore.Routing.DataSourceDependentCache`1.Initialize()\r\n   at System.Threading.LazyInitializer.EnsureInitializedCore[T](T& target, Boolean& initialized, Object& syncLock, Func`1 valueFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DataSourceDependentMatcher..ctor(EndpointDataSource dataSource, Lifetime lifetime, Func`1 matcherBuilderFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DfaMatcherFactory.CreateMatcher(EndpointDataSource dataSource)\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.InitializeCoreAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatcher|10_0(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task`1 matcherTask)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"ee72b7dcb58f23ac71f6a26ce281f9a4","@sp":"5adc49ca3be82a59","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNER3R5L3FMF:********","RequestPath":"/api/auth/role/test","ConnectionId":"0HNER3R5L3FMF","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:39:15.0618184Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.InvalidOperationException: Action 'SSIC.Modules.Auth.Controllers.RoleController.CreateRole (SSIC.Modules.Auth)' does not have an attribute route. Action methods on controllers annotated with ApiControllerAttribute must be attribute routed.\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.EnsureActionIsAttributeRouted(ActionModel actionModel)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.OnProvidersExecuting(ApplicationModelProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApplicationModelFactory.CreateApplicationModel(IEnumerable`1 controllerTypes)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.GetDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.OnProvidersExecuting(ActionDescriptorProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.UpdateCollection()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.get_ActionDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.UpdateEndpoints()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.GetChangeToken()\r\n   at Microsoft.Extensions.Primitives.ChangeToken.ChangeTokenRegistration`1..ctor(Func`1 changeTokenProducer, Action`1 changeTokenConsumer, TState state)\r\n   at Microsoft.Extensions.Primitives.ChangeToken.OnChange(Func`1 changeTokenProducer, Action changeTokenConsumer)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.CreateChangeTokenUnsynchronized(Boolean collectionChanged)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.EnsureChangeTokenInitialized()\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.GetChangeToken()\r\n   at Microsoft.AspNetCore.Routing.DataSourceDependentCache`1.Initialize()\r\n   at System.Threading.LazyInitializer.EnsureInitializedCore[T](T& target, Boolean& initialized, Object& syncLock, Func`1 valueFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DataSourceDependentMatcher..ctor(EndpointDataSource dataSource, Lifetime lifetime, Func`1 matcherBuilderFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DfaMatcherFactory.CreateMatcher(EndpointDataSource dataSource)\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.InitializeCoreAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatcher|10_0(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task`1 matcherTask)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"d6926cfd4195c51b0089787feb855668","@sp":"ecfdfeca2eeb5190","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNER3R5L3FMG:********","RequestPath":"/api/auth/auth/login","ConnectionId":"0HNER3R5L3FMG","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:41:06.8680463Z","@mt":"发现 {Count} 个模块文件","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:41:06.8783547Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:41:06.8914841Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:41:06.8917649Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:41:06.8941383Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:41:06.8999541Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth","AssemblyName":"SSIC.Modules.Auth, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:41:06.9023948Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:41:06.9041214Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:41:06.9070832Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:41:06.9083836Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:41:06.9169029Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:41:06.9182538Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:41:06.9297155Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:41:07.0130488Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:41:07.0146074Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:41:07.0156597Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:41:07.0164680Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:41:07.0173592Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:41:08.4566672Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:41:08.4594198Z","@mt":"开始强制刷新ActionDescriptorCollectionProvider...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:41:08.4612205Z","@mt":"ActionDescriptorCollectionProvider刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:41:08.4622884Z","@mt":"已使用ActionDescriptorRefreshService刷新ActionDescriptorCollectionProvider","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:41:08.4635236Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:41:08.4643240Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:41:08.4655994Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:41:08.9737050Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:41:08.9762029Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:41:08.9773428Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:41:08.9777974Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:41:28.6464240Z","@mt":"首次请求触发端点刷新","@tr":"764534d7d74104da02dd5389f229a3d8","@sp":"d150e5f7447e9e21","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER3SG20PHB:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3SG20PHB","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:41:28.6530277Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"764534d7d74104da02dd5389f229a3d8","@sp":"d150e5f7447e9e21","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER3SG20PHB:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3SG20PHB","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:41:28.6572944Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.InvalidOperationException: Action 'SSIC.Modules.Auth.Controllers.RoleController.CreateRole (SSIC.Modules.Auth)' does not have an attribute route. Action methods on controllers annotated with ApiControllerAttribute must be attribute routed.\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.EnsureActionIsAttributeRouted(ActionModel actionModel)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.OnProvidersExecuting(ApplicationModelProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApplicationModelFactory.CreateApplicationModel(IEnumerable`1 controllerTypes)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.GetDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.OnProvidersExecuting(ActionDescriptorProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.UpdateCollection()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.get_ActionDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.UpdateEndpoints()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.GetChangeToken()\r\n   at Microsoft.Extensions.Primitives.ChangeToken.ChangeTokenRegistration`1..ctor(Func`1 changeTokenProducer, Action`1 changeTokenConsumer, TState state)\r\n   at Microsoft.Extensions.Primitives.ChangeToken.OnChange(Func`1 changeTokenProducer, Action changeTokenConsumer)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.CreateChangeTokenUnsynchronized(Boolean collectionChanged)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.EnsureChangeTokenInitialized()\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.GetChangeToken()\r\n   at Microsoft.AspNetCore.Routing.DataSourceDependentCache`1.Initialize()\r\n   at System.Threading.LazyInitializer.EnsureInitializedCore[T](T& target, Boolean& initialized, Object& syncLock, Func`1 valueFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DataSourceDependentMatcher..ctor(EndpointDataSource dataSource, Lifetime lifetime, Func`1 matcherBuilderFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DfaMatcherFactory.CreateMatcher(EndpointDataSource dataSource)\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.InitializeCoreAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatcher|10_0(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task`1 matcherTask)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"764534d7d74104da02dd5389f229a3d8","@sp":"d150e5f7447e9e21","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNER3SG20PHB:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNER3SG20PHB","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:41:37.5503704Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.InvalidOperationException: Action 'SSIC.Modules.Auth.Controllers.RoleController.CreateRole (SSIC.Modules.Auth)' does not have an attribute route. Action methods on controllers annotated with ApiControllerAttribute must be attribute routed.\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.EnsureActionIsAttributeRouted(ActionModel actionModel)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.OnProvidersExecuting(ApplicationModelProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApplicationModelFactory.CreateApplicationModel(IEnumerable`1 controllerTypes)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.GetDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.OnProvidersExecuting(ActionDescriptorProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.UpdateCollection()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.get_ActionDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.UpdateEndpoints()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.GetChangeToken()\r\n   at Microsoft.Extensions.Primitives.ChangeToken.ChangeTokenRegistration`1..ctor(Func`1 changeTokenProducer, Action`1 changeTokenConsumer, TState state)\r\n   at Microsoft.Extensions.Primitives.ChangeToken.OnChange(Func`1 changeTokenProducer, Action changeTokenConsumer)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.CreateChangeTokenUnsynchronized(Boolean collectionChanged)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.EnsureChangeTokenInitialized()\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.GetChangeToken()\r\n   at Microsoft.AspNetCore.Routing.DataSourceDependentCache`1.Initialize()\r\n   at System.Threading.LazyInitializer.EnsureInitializedCore[T](T& target, Boolean& initialized, Object& syncLock, Func`1 valueFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DataSourceDependentMatcher..ctor(EndpointDataSource dataSource, Lifetime lifetime, Func`1 matcherBuilderFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DfaMatcherFactory.CreateMatcher(EndpointDataSource dataSource)\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.InitializeCoreAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatcher|10_0(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task`1 matcherTask)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"a3898bbd041803e3bbeb027ce798801a","@sp":"223733722bf19c7a","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNER3SG20PHC:********","RequestPath":"/api/auth/role/test","ConnectionId":"0HNER3SG20PHC","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:42:39.9103723Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:42:39.9009908Z","@mt":"发现 {Count} 个模块文件","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:42:39.9226848Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:42:39.9239572Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:42:39.9262854Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:42:39.9322213Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth","AssemblyName":"SSIC.Modules.Auth, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:42:39.9337263Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:42:39.9346152Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:42:39.9372217Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:42:39.9382351Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:42:39.9467575Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:42:39.9481059Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:42:39.9613453Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:42:40.0468082Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:42:40.0478894Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:42:40.0484622Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:42:40.0488572Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:42:40.0493374Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:42:41.4870151Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:42:41.4893949Z","@mt":"开始强制刷新ActionDescriptorCollectionProvider...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:42:41.4904422Z","@mt":"ActionDescriptorCollectionProvider刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:42:41.4910517Z","@mt":"已使用ActionDescriptorRefreshService刷新ActionDescriptorCollectionProvider","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:42:41.4929857Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:42:41.4936997Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:42:41.4942298Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:42:41.9985003Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:42:42.0008278Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:42:42.0021170Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:42:42.0028418Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:44:44.6679981Z","@mt":"首次请求触发端点刷新","@tr":"2e1db0bf573798d70e655178bd3632f6","@sp":"cd9405e02fc80f7e","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER3UA6HR5N:********","RequestPath":"/api/auth/role/test","ConnectionId":"0HNER3UA6HR5N","MachineName":"LAPTOP-F6SGUTN5","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:44:44.6756814Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"2e1db0bf573798d70e655178bd3632f6","@sp":"cd9405e02fc80f7e","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER3UA6HR5N:********","RequestPath":"/api/auth/role/test","ConnectionId":"0HNER3UA6HR5N","MachineName":"LAPTOP-F6SGUTN5","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:44:44.6826832Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.InvalidOperationException: Action 'SSIC.Modules.Auth.Controllers.RoleController.CreateRole (SSIC.Modules.Auth)' does not have an attribute route. Action methods on controllers annotated with ApiControllerAttribute must be attribute routed.\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.EnsureActionIsAttributeRouted(ActionModel actionModel)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.OnProvidersExecuting(ApplicationModelProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApplicationModelFactory.CreateApplicationModel(IEnumerable`1 controllerTypes)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.GetDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.OnProvidersExecuting(ActionDescriptorProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.UpdateCollection()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.get_ActionDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.UpdateEndpoints()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.GetChangeToken()\r\n   at Microsoft.Extensions.Primitives.ChangeToken.ChangeTokenRegistration`1..ctor(Func`1 changeTokenProducer, Action`1 changeTokenConsumer, TState state)\r\n   at Microsoft.Extensions.Primitives.ChangeToken.OnChange(Func`1 changeTokenProducer, Action changeTokenConsumer)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.CreateChangeTokenUnsynchronized(Boolean collectionChanged)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.EnsureChangeTokenInitialized()\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.GetChangeToken()\r\n   at Microsoft.AspNetCore.Routing.DataSourceDependentCache`1.Initialize()\r\n   at System.Threading.LazyInitializer.EnsureInitializedCore[T](T& target, Boolean& initialized, Object& syncLock, Func`1 valueFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DataSourceDependentMatcher..ctor(EndpointDataSource dataSource, Lifetime lifetime, Func`1 matcherBuilderFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DfaMatcherFactory.CreateMatcher(EndpointDataSource dataSource)\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.InitializeCoreAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatcher|10_0(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task`1 matcherTask)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"2e1db0bf573798d70e655178bd3632f6","@sp":"cd9405e02fc80f7e","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNER3UA6HR5N:********","RequestPath":"/api/auth/role/test","ConnectionId":"0HNER3UA6HR5N","MachineName":"LAPTOP-F6SGUTN5","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:46:03.3881117Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.InvalidOperationException: Action 'SSIC.Modules.Auth.Controllers.RoleController.CreateRole (SSIC.Modules.Auth)' does not have an attribute route. Action methods on controllers annotated with ApiControllerAttribute must be attribute routed.\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.EnsureActionIsAttributeRouted(ActionModel actionModel)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.OnProvidersExecuting(ApplicationModelProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApplicationModelFactory.CreateApplicationModel(IEnumerable`1 controllerTypes)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.GetDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.OnProvidersExecuting(ActionDescriptorProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.UpdateCollection()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.get_ActionDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.UpdateEndpoints()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.GetChangeToken()\r\n   at Microsoft.Extensions.Primitives.ChangeToken.ChangeTokenRegistration`1..ctor(Func`1 changeTokenProducer, Action`1 changeTokenConsumer, TState state)\r\n   at Microsoft.Extensions.Primitives.ChangeToken.OnChange(Func`1 changeTokenProducer, Action changeTokenConsumer)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.CreateChangeTokenUnsynchronized(Boolean collectionChanged)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.EnsureChangeTokenInitialized()\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.GetChangeToken()\r\n   at Microsoft.AspNetCore.Routing.DataSourceDependentCache`1.Initialize()\r\n   at System.Threading.LazyInitializer.EnsureInitializedCore[T](T& target, Boolean& initialized, Object& syncLock, Func`1 valueFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DataSourceDependentMatcher..ctor(EndpointDataSource dataSource, Lifetime lifetime, Func`1 matcherBuilderFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DfaMatcherFactory.CreateMatcher(EndpointDataSource dataSource)\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.InitializeCoreAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatcher|10_0(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task`1 matcherTask)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"688cb181776980e8adc7901f313459fa","@sp":"f5d5baaf57fc92f6","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNER3UA6HR5O:********","RequestPath":"/WeatherForecast2","ConnectionId":"0HNER3UA6HR5O","MachineName":"LAPTOP-F6SGUTN5","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:46:08.4259635Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.InvalidOperationException: Action 'SSIC.Modules.Auth.Controllers.RoleController.CreateRole (SSIC.Modules.Auth)' does not have an attribute route. Action methods on controllers annotated with ApiControllerAttribute must be attribute routed.\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.EnsureActionIsAttributeRouted(ActionModel actionModel)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.OnProvidersExecuting(ApplicationModelProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApplicationModelFactory.CreateApplicationModel(IEnumerable`1 controllerTypes)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.GetDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.OnProvidersExecuting(ActionDescriptorProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.UpdateCollection()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.get_ActionDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.UpdateEndpoints()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.GetChangeToken()\r\n   at Microsoft.Extensions.Primitives.ChangeToken.ChangeTokenRegistration`1..ctor(Func`1 changeTokenProducer, Action`1 changeTokenConsumer, TState state)\r\n   at Microsoft.Extensions.Primitives.ChangeToken.OnChange(Func`1 changeTokenProducer, Action changeTokenConsumer)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.CreateChangeTokenUnsynchronized(Boolean collectionChanged)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.EnsureChangeTokenInitialized()\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.GetChangeToken()\r\n   at Microsoft.AspNetCore.Routing.DataSourceDependentCache`1.Initialize()\r\n   at System.Threading.LazyInitializer.EnsureInitializedCore[T](T& target, Boolean& initialized, Object& syncLock, Func`1 valueFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DataSourceDependentMatcher..ctor(EndpointDataSource dataSource, Lifetime lifetime, Func`1 matcherBuilderFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DfaMatcherFactory.CreateMatcher(EndpointDataSource dataSource)\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.InitializeCoreAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatcher|10_0(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task`1 matcherTask)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"7e64c6f3b2b31864a8e1a3bd1a65e3f0","@sp":"b1e35c19652268fe","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNER3UA6HR5P:********","RequestPath":"/api/auth/role/createrole","ConnectionId":"0HNER3UA6HR5P","MachineName":"LAPTOP-F6SGUTN5","ThreadId":3,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:46:12.8267576Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.InvalidOperationException: Action 'SSIC.Modules.Auth.Controllers.RoleController.CreateRole (SSIC.Modules.Auth)' does not have an attribute route. Action methods on controllers annotated with ApiControllerAttribute must be attribute routed.\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.EnsureActionIsAttributeRouted(ActionModel actionModel)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.OnProvidersExecuting(ApplicationModelProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApplicationModelFactory.CreateApplicationModel(IEnumerable`1 controllerTypes)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.GetDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.OnProvidersExecuting(ActionDescriptorProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.UpdateCollection()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.get_ActionDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.UpdateEndpoints()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.GetChangeToken()\r\n   at Microsoft.Extensions.Primitives.ChangeToken.ChangeTokenRegistration`1..ctor(Func`1 changeTokenProducer, Action`1 changeTokenConsumer, TState state)\r\n   at Microsoft.Extensions.Primitives.ChangeToken.OnChange(Func`1 changeTokenProducer, Action changeTokenConsumer)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.CreateChangeTokenUnsynchronized(Boolean collectionChanged)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.EnsureChangeTokenInitialized()\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.GetChangeToken()\r\n   at Microsoft.AspNetCore.Routing.DataSourceDependentCache`1.Initialize()\r\n   at System.Threading.LazyInitializer.EnsureInitializedCore[T](T& target, Boolean& initialized, Object& syncLock, Func`1 valueFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DataSourceDependentMatcher..ctor(EndpointDataSource dataSource, Lifetime lifetime, Func`1 matcherBuilderFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DfaMatcherFactory.CreateMatcher(EndpointDataSource dataSource)\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.InitializeCoreAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatcher|10_0(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task`1 matcherTask)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"bf5c0be7e1f48c5736ae5b5f27000c93","@sp":"a477b719b9165fe9","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNER3UA6HR5Q:********","RequestPath":"/","ConnectionId":"0HNER3UA6HR5Q","MachineName":"LAPTOP-F6SGUTN5","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:20.9862868Z","@mt":"发现 {Count} 个模块文件","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:20.9990521Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:21.0110368Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:21.0113059Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:21.0141545Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:21.0188250Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth","AssemblyName":"SSIC.Modules.Auth, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:21.0211988Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:21.0218890Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:21.0254815Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:21.0268417Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:21.0361636Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:21.0375739Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:21.0487637Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:21.1347615Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:21.1362571Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:21.1376451Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:21.1384365Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:21.1393285Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:22.5667648Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:22.5702062Z","@mt":"开始强制刷新ActionDescriptorCollectionProvider...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:22.5717481Z","@mt":"ActionDescriptorCollectionProvider刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:22.5724705Z","@mt":"已使用ActionDescriptorRefreshService刷新ActionDescriptorCollectionProvider","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:22.5732314Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:22.5738047Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:22.5743675Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:23.0851071Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:23.0896930Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:23.0924008Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:23.0933050Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:43.1160031Z","@mt":"首次请求触发端点刷新","@tr":"4d334f792847846b336c202f2e0dfcda","@sp":"f618e70d041f1ae6","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER40HH9SPD:********","RequestPath":"/api/auth/role/test","ConnectionId":"0HNER40HH9SPD","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:43.1222843Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"4d334f792847846b336c202f2e0dfcda","@sp":"f618e70d041f1ae6","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER40HH9SPD:********","RequestPath":"/api/auth/role/test","ConnectionId":"0HNER40HH9SPD","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:43.1277862Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.InvalidOperationException: Action 'SSIC.Modules.Auth.Controllers.RoleController.CreateRole (SSIC.Modules.Auth)' does not have an attribute route. Action methods on controllers annotated with ApiControllerAttribute must be attribute routed.\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.EnsureActionIsAttributeRouted(ActionModel actionModel)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.OnProvidersExecuting(ApplicationModelProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApplicationModelFactory.CreateApplicationModel(IEnumerable`1 controllerTypes)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.GetDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.OnProvidersExecuting(ActionDescriptorProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.UpdateCollection()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.get_ActionDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.UpdateEndpoints()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.GetChangeToken()\r\n   at Microsoft.Extensions.Primitives.ChangeToken.ChangeTokenRegistration`1..ctor(Func`1 changeTokenProducer, Action`1 changeTokenConsumer, TState state)\r\n   at Microsoft.Extensions.Primitives.ChangeToken.OnChange(Func`1 changeTokenProducer, Action changeTokenConsumer)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.CreateChangeTokenUnsynchronized(Boolean collectionChanged)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.EnsureChangeTokenInitialized()\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.GetChangeToken()\r\n   at Microsoft.AspNetCore.Routing.DataSourceDependentCache`1.Initialize()\r\n   at System.Threading.LazyInitializer.EnsureInitializedCore[T](T& target, Boolean& initialized, Object& syncLock, Func`1 valueFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DataSourceDependentMatcher..ctor(EndpointDataSource dataSource, Lifetime lifetime, Func`1 matcherBuilderFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DfaMatcherFactory.CreateMatcher(EndpointDataSource dataSource)\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.InitializeCoreAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatcher|10_0(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task`1 matcherTask)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"4d334f792847846b336c202f2e0dfcda","@sp":"f618e70d041f1ae6","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNER40HH9SPD:********","RequestPath":"/api/auth/role/test","ConnectionId":"0HNER40HH9SPD","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:46.8511008Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.InvalidOperationException: Action 'SSIC.Modules.Auth.Controllers.RoleController.CreateRole (SSIC.Modules.Auth)' does not have an attribute route. Action methods on controllers annotated with ApiControllerAttribute must be attribute routed.\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.EnsureActionIsAttributeRouted(ActionModel actionModel)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.OnProvidersExecuting(ApplicationModelProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApplicationModelFactory.CreateApplicationModel(IEnumerable`1 controllerTypes)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.GetDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.OnProvidersExecuting(ActionDescriptorProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.UpdateCollection()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.get_ActionDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.UpdateEndpoints()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.GetChangeToken()\r\n   at Microsoft.Extensions.Primitives.ChangeToken.ChangeTokenRegistration`1..ctor(Func`1 changeTokenProducer, Action`1 changeTokenConsumer, TState state)\r\n   at Microsoft.Extensions.Primitives.ChangeToken.OnChange(Func`1 changeTokenProducer, Action changeTokenConsumer)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.CreateChangeTokenUnsynchronized(Boolean collectionChanged)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.EnsureChangeTokenInitialized()\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.GetChangeToken()\r\n   at Microsoft.AspNetCore.Routing.DataSourceDependentCache`1.Initialize()\r\n   at System.Threading.LazyInitializer.EnsureInitializedCore[T](T& target, Boolean& initialized, Object& syncLock, Func`1 valueFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DataSourceDependentMatcher..ctor(EndpointDataSource dataSource, Lifetime lifetime, Func`1 matcherBuilderFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DfaMatcherFactory.CreateMatcher(EndpointDataSource dataSource)\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.InitializeCoreAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatcher|10_0(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task`1 matcherTask)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"da817a14683f033b1bc8f0ee1aeb4d7f","@sp":"cf6c8dfda30e5971","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNER40HH9SPE:********","RequestPath":"/scalar/v1","ConnectionId":"0HNER40HH9SPE","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:51.7465210Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.InvalidOperationException: Action 'SSIC.Modules.Auth.Controllers.RoleController.CreateRole (SSIC.Modules.Auth)' does not have an attribute route. Action methods on controllers annotated with ApiControllerAttribute must be attribute routed.\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.EnsureActionIsAttributeRouted(ActionModel actionModel)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.OnProvidersExecuting(ApplicationModelProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApplicationModelFactory.CreateApplicationModel(IEnumerable`1 controllerTypes)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.GetDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.OnProvidersExecuting(ActionDescriptorProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.UpdateCollection()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.get_ActionDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.UpdateEndpoints()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.GetChangeToken()\r\n   at Microsoft.Extensions.Primitives.ChangeToken.ChangeTokenRegistration`1..ctor(Func`1 changeTokenProducer, Action`1 changeTokenConsumer, TState state)\r\n   at Microsoft.Extensions.Primitives.ChangeToken.OnChange(Func`1 changeTokenProducer, Action changeTokenConsumer)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.CreateChangeTokenUnsynchronized(Boolean collectionChanged)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.EnsureChangeTokenInitialized()\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.GetChangeToken()\r\n   at Microsoft.AspNetCore.Routing.DataSourceDependentCache`1.Initialize()\r\n   at System.Threading.LazyInitializer.EnsureInitializedCore[T](T& target, Boolean& initialized, Object& syncLock, Func`1 valueFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DataSourceDependentMatcher..ctor(EndpointDataSource dataSource, Lifetime lifetime, Func`1 matcherBuilderFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DfaMatcherFactory.CreateMatcher(EndpointDataSource dataSource)\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.InitializeCoreAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatcher|10_0(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task`1 matcherTask)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"91d8ca19a70256c168c8e4daf03cd67c","@sp":"6305614ec5de9f83","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNER40HH9SPF:********","RequestPath":"/WeatherForecast2","ConnectionId":"0HNER40HH9SPF","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:48:57.7323352Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.InvalidOperationException: Action 'SSIC.Modules.Auth.Controllers.RoleController.CreateRole (SSIC.Modules.Auth)' does not have an attribute route. Action methods on controllers annotated with ApiControllerAttribute must be attribute routed.\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.EnsureActionIsAttributeRouted(ActionModel actionModel)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.OnProvidersExecuting(ApplicationModelProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApplicationModelFactory.CreateApplicationModel(IEnumerable`1 controllerTypes)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.GetDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.OnProvidersExecuting(ActionDescriptorProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.UpdateCollection()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.get_ActionDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.UpdateEndpoints()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.GetChangeToken()\r\n   at Microsoft.Extensions.Primitives.ChangeToken.ChangeTokenRegistration`1..ctor(Func`1 changeTokenProducer, Action`1 changeTokenConsumer, TState state)\r\n   at Microsoft.Extensions.Primitives.ChangeToken.OnChange(Func`1 changeTokenProducer, Action changeTokenConsumer)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.CreateChangeTokenUnsynchronized(Boolean collectionChanged)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.EnsureChangeTokenInitialized()\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.GetChangeToken()\r\n   at Microsoft.AspNetCore.Routing.DataSourceDependentCache`1.Initialize()\r\n   at System.Threading.LazyInitializer.EnsureInitializedCore[T](T& target, Boolean& initialized, Object& syncLock, Func`1 valueFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DataSourceDependentMatcher..ctor(EndpointDataSource dataSource, Lifetime lifetime, Func`1 matcherBuilderFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DfaMatcherFactory.CreateMatcher(EndpointDataSource dataSource)\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.InitializeCoreAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatcher|10_0(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task`1 matcherTask)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"8ca5d42d35eea1e339f2c96cb6e1ec51","@sp":"0af219abfcd51dea","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNER40HH9SPE:********","RequestPath":"/scalar/v1","ConnectionId":"0HNER40HH9SPE","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:49:05.4356817Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.InvalidOperationException: Action 'SSIC.Modules.Auth.Controllers.RoleController.CreateRole (SSIC.Modules.Auth)' does not have an attribute route. Action methods on controllers annotated with ApiControllerAttribute must be attribute routed.\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.EnsureActionIsAttributeRouted(ActionModel actionModel)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.OnProvidersExecuting(ApplicationModelProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApplicationModelFactory.CreateApplicationModel(IEnumerable`1 controllerTypes)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.GetDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.OnProvidersExecuting(ActionDescriptorProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.UpdateCollection()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.get_ActionDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.UpdateEndpoints()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.GetChangeToken()\r\n   at Microsoft.Extensions.Primitives.ChangeToken.ChangeTokenRegistration`1..ctor(Func`1 changeTokenProducer, Action`1 changeTokenConsumer, TState state)\r\n   at Microsoft.Extensions.Primitives.ChangeToken.OnChange(Func`1 changeTokenProducer, Action changeTokenConsumer)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.CreateChangeTokenUnsynchronized(Boolean collectionChanged)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.EnsureChangeTokenInitialized()\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.GetChangeToken()\r\n   at Microsoft.AspNetCore.Routing.DataSourceDependentCache`1.Initialize()\r\n   at System.Threading.LazyInitializer.EnsureInitializedCore[T](T& target, Boolean& initialized, Object& syncLock, Func`1 valueFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DataSourceDependentMatcher..ctor(EndpointDataSource dataSource, Lifetime lifetime, Func`1 matcherBuilderFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DfaMatcherFactory.CreateMatcher(EndpointDataSource dataSource)\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.InitializeCoreAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatcher|10_0(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task`1 matcherTask)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"d6d097a4cc3020be5928c2210a30bfbb","@sp":"f41b5f68ea86d14a","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNER40HH9SPG:********","RequestPath":"/","ConnectionId":"0HNER40HH9SPG","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:50:50.1202929Z","@mt":"发现 {Count} 个模块文件","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:50:50.1281871Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:50:50.1436382Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:50:50.1436558Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:50:50.1451301Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:50:50.1495041Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth","AssemblyName":"SSIC.Modules.Auth, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:50:50.1523726Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:50:50.1532926Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:50:50.1561577Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:50:50.1572060Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:50:50.1645165Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:50:50.1660822Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:50:50.1766634Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:50:50.2552014Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:50:50.2566913Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:50:50.2573917Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:50:50.2578754Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:50:50.2593188Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:50:51.6995063Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:50:51.7021601Z","@mt":"开始强制刷新ActionDescriptorCollectionProvider...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:50:51.7037619Z","@mt":"ActionDescriptorCollectionProvider刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:50:51.7044884Z","@mt":"已使用ActionDescriptorRefreshService刷新ActionDescriptorCollectionProvider","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:50:51.7059012Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:50:51.7067265Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:50:51.7072615Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:50:52.2130454Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:50:52.2156593Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:50:52.2175862Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:50:52.2187063Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:51:11.6456302Z","@mt":"首次请求触发端点刷新","@tr":"38446465b68cb6dfc3cfef4eddc83a22","@sp":"45a71d0312c5df74","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER41TPT514:********","RequestPath":"/api/auth/role/test","ConnectionId":"0HNER41TPT514","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:51:11.6525565Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"38446465b68cb6dfc3cfef4eddc83a22","@sp":"45a71d0312c5df74","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER41TPT514:********","RequestPath":"/api/auth/role/test","ConnectionId":"0HNER41TPT514","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:51:11.6576536Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.InvalidOperationException: Action 'SSIC.Modules.Auth.Controllers.RoleController.CreateRole (SSIC.Modules.Auth)' does not have an attribute route. Action methods on controllers annotated with ApiControllerAttribute must be attribute routed.\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.EnsureActionIsAttributeRouted(ActionModel actionModel)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.OnProvidersExecuting(ApplicationModelProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApplicationModelFactory.CreateApplicationModel(IEnumerable`1 controllerTypes)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.GetDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.OnProvidersExecuting(ActionDescriptorProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.UpdateCollection()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.get_ActionDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.UpdateEndpoints()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.GetChangeToken()\r\n   at Microsoft.Extensions.Primitives.ChangeToken.ChangeTokenRegistration`1..ctor(Func`1 changeTokenProducer, Action`1 changeTokenConsumer, TState state)\r\n   at Microsoft.Extensions.Primitives.ChangeToken.OnChange(Func`1 changeTokenProducer, Action changeTokenConsumer)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.CreateChangeTokenUnsynchronized(Boolean collectionChanged)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.EnsureChangeTokenInitialized()\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.GetChangeToken()\r\n   at Microsoft.AspNetCore.Routing.DataSourceDependentCache`1.Initialize()\r\n   at System.Threading.LazyInitializer.EnsureInitializedCore[T](T& target, Boolean& initialized, Object& syncLock, Func`1 valueFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DataSourceDependentMatcher..ctor(EndpointDataSource dataSource, Lifetime lifetime, Func`1 matcherBuilderFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DfaMatcherFactory.CreateMatcher(EndpointDataSource dataSource)\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.InitializeCoreAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatcher|10_0(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task`1 matcherTask)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"38446465b68cb6dfc3cfef4eddc83a22","@sp":"45a71d0312c5df74","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNER41TPT514:********","RequestPath":"/api/auth/role/test","ConnectionId":"0HNER41TPT514","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:51:19.9218334Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.InvalidOperationException: Action 'SSIC.Modules.Auth.Controllers.RoleController.CreateRole (SSIC.Modules.Auth)' does not have an attribute route. Action methods on controllers annotated with ApiControllerAttribute must be attribute routed.\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.EnsureActionIsAttributeRouted(ActionModel actionModel)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.OnProvidersExecuting(ApplicationModelProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApplicationModelFactory.CreateApplicationModel(IEnumerable`1 controllerTypes)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.GetDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.OnProvidersExecuting(ActionDescriptorProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.UpdateCollection()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.get_ActionDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.UpdateEndpoints()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.GetChangeToken()\r\n   at Microsoft.Extensions.Primitives.ChangeToken.ChangeTokenRegistration`1..ctor(Func`1 changeTokenProducer, Action`1 changeTokenConsumer, TState state)\r\n   at Microsoft.Extensions.Primitives.ChangeToken.OnChange(Func`1 changeTokenProducer, Action changeTokenConsumer)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.CreateChangeTokenUnsynchronized(Boolean collectionChanged)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.EnsureChangeTokenInitialized()\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.GetChangeToken()\r\n   at Microsoft.AspNetCore.Routing.DataSourceDependentCache`1.Initialize()\r\n   at System.Threading.LazyInitializer.EnsureInitializedCore[T](T& target, Boolean& initialized, Object& syncLock, Func`1 valueFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DataSourceDependentMatcher..ctor(EndpointDataSource dataSource, Lifetime lifetime, Func`1 matcherBuilderFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DfaMatcherFactory.CreateMatcher(EndpointDataSource dataSource)\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.InitializeCoreAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatcher|10_0(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task`1 matcherTask)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"9ce839cbab330ceb3f36709e1df93cab","@sp":"aa3008e7aee1c315","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNER41TPT515:********","RequestPath":"/","ConnectionId":"0HNER41TPT515","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:52:44.0333252Z","@mt":"发现 {Count} 个模块文件","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:52:44.0438267Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:52:44.0554437Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:52:44.0558776Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:52:44.0574502Z","@mt":"开始加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:52:44.0644159Z","@mt":"成功加载插件: {PluginName}, 程序集: {AssemblyName}","PluginName":"SSIC.Modules.Auth","AssemblyName":"SSIC.Modules.Auth, Version=*******, Culture=neutral, PublicKeyToken=null","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:52:44.0663092Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:52:44.0671275Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:52:44.0698388Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:52:44.0709506Z","@mt":"开始强制刷新MVC系统...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:52:44.0817270Z","@mt":"MVC系统刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:52:44.0826607Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:52:44.0932525Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:52:44.1733909Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:52:44.1752925Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:52:44.1767624Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:52:44.1774731Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:52:44.1780849Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:52:45.6112361Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:52:45.6141378Z","@mt":"开始强制刷新ActionDescriptorCollectionProvider...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:52:45.6161792Z","@mt":"ActionDescriptorCollectionProvider刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:52:45.6172160Z","@mt":"已使用ActionDescriptorRefreshService刷新ActionDescriptorCollectionProvider","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:52:45.6188594Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:52:45.6198857Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:52:45.6210830Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:52:46.1282295Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:52:46.1305271Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:52:46.1317193Z","@mt":"热插拔功能已禁用，不启动文件监控","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:52:46.1322940Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:53:05.2516108Z","@mt":"首次请求触发端点刷新","@tr":"008de04ff613ae9b89705d434cafff0d","@sp":"5cdacfd4db283376","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER42VLBUKT:********","RequestPath":"/","ConnectionId":"0HNER42VLBUKT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:53:05.2575791Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"008de04ff613ae9b89705d434cafff0d","@sp":"5cdacfd4db283376","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNER42VLBUKT:********","RequestPath":"/","ConnectionId":"0HNER42VLBUKT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-14T10:53:05.2617450Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.InvalidOperationException: Action 'SSIC.Modules.Auth.Controllers.RoleController.CreateRole (SSIC.Modules.Auth)' does not have an attribute route. Action methods on controllers annotated with ApiControllerAttribute must be attribute routed.\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.EnsureActionIsAttributeRouted(ActionModel actionModel)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.OnProvidersExecuting(ApplicationModelProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApplicationModelFactory.CreateApplicationModel(IEnumerable`1 controllerTypes)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.GetDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.OnProvidersExecuting(ActionDescriptorProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.UpdateCollection()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.get_ActionDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.UpdateEndpoints()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.GetChangeToken()\r\n   at Microsoft.Extensions.Primitives.ChangeToken.ChangeTokenRegistration`1..ctor(Func`1 changeTokenProducer, Action`1 changeTokenConsumer, TState state)\r\n   at Microsoft.Extensions.Primitives.ChangeToken.OnChange(Func`1 changeTokenProducer, Action changeTokenConsumer)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.CreateChangeTokenUnsynchronized(Boolean collectionChanged)\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.EnsureChangeTokenInitialized()\r\n   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.GetChangeToken()\r\n   at Microsoft.AspNetCore.Routing.DataSourceDependentCache`1.Initialize()\r\n   at System.Threading.LazyInitializer.EnsureInitializedCore[T](T& target, Boolean& initialized, Object& syncLock, Func`1 valueFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DataSourceDependentMatcher..ctor(EndpointDataSource dataSource, Lifetime lifetime, Func`1 matcherBuilderFactory)\r\n   at Microsoft.AspNetCore.Routing.Matching.DfaMatcherFactory.CreateMatcher(EndpointDataSource dataSource)\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.InitializeCoreAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatcher|10_0(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task`1 matcherTask)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"008de04ff613ae9b89705d434cafff0d","@sp":"5cdacfd4db283376","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNER42VLBUKT:********","RequestPath":"/","ConnectionId":"0HNER42VLBUKT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
