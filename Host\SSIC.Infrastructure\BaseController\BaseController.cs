using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SSIC.Infrastructure.BaseProvider;
using SSIC.Infrastructure.Enums;
using SSIC.Infrastructure.ViewModel;
using System;

namespace SSIC.Infrastructure.BaseController
{
    /// <summary>
    /// 基础控制器
    /// </summary>
    public abstract class  BaseController : ControllerBase
    {
        protected readonly ILogger? Logger;
        protected readonly IServiceProvider ServiceProvider;

        protected BaseController(IServiceProvider serviceProvider)
        {
            ServiceProvider = serviceProvider;
        }

        protected BaseController(IServiceProvider serviceProvider, ILogger logger) : this(serviceProvider)
        {
            Logger = logger;
        }

        /// <summary>
        /// 获取指定类型的服务
        /// </summary>
        /// <typeparam name="TService">服务类型</typeparam>
        /// <returns></returns>
        protected TService GetService<TService>() where TService : class
        {
            return ServiceProvider.GetRequiredService<TService>();
        }

        /// <summary>
        /// 获取指定类型的服务（可选）
        /// </summary>
        /// <typeparam name="TService">服务类型</typeparam>
        /// <returns></returns>
        protected TService? GetOptionalService<TService>() where TService : class
        {
            return ServiceProvider.GetService<TService>();
        }

        /// <summary>
        /// 返回成功结果
        /// </summary>
        /// <param name="data">数据</param>
        /// <param name="message">消息</param>
        /// <returns></returns>
        protected ActionResult Success(object? data = null, string message = "操作成功")
        {
            return Ok(new OutputView { c = OutCode.Success, d = data, m = message });
        }

        /// <summary>
        /// 返回失败结果
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="code">错误代码</param>
        /// <returns></returns>
        protected ActionResult Error(string message = "操作失败", OutCode code = OutCode.ServerError)
        {
            return Ok(new OutputView { c = code, d = null, m = message });
        }

        /// <summary>
        /// 返回分页结果
        /// </summary>
        /// <param name="data">数据列表</param>
        /// <param name="total">总数</param>
        /// <param name="message">消息</param>
        /// <returns></returns>
        protected ActionResult PageResult(object data, long total, string message = "查询成功")
        {
            return Ok(new OutputView
            {
                c = OutCode.Success,
                d = new { items = data, total = total },
                m = message
            });
        }

        /// <summary>
        /// 返回验证失败结果
        /// </summary>
        /// <param name="message">验证失败消息</param>
        /// <returns></returns>
        protected ActionResult ValidationError(string message = "数据验证失败")
        {
            return BadRequest(new OutputView { c = OutCode.ValidationError, d = null, m = message });
        }

        /// <summary>
        /// 返回未授权结果
        /// </summary>
        /// <param name="message">未授权消息</param>
        /// <returns></returns>
        protected new ActionResult Unauthorized(string message = "未授权访问")
        {
            return base.Unauthorized(new OutputView { c = OutCode.Unauthorized, d = null, m = message });
        }

        /// <summary>
        /// 返回禁止访问结果
        /// </summary>
        /// <param name="message">禁止访问消息</param>
        /// <returns></returns>
        protected ActionResult Forbidden(string message = "禁止访问")
        {
            return StatusCode(403, new OutputView { c = OutCode.Forbidden, d = null, m = message });
        }

        /// <summary>
        /// 返回未找到结果
        /// </summary>
        /// <param name="message">未找到消息</param>
        /// <returns></returns>
        protected new ActionResult NotFound(string message = "资源未找到")
        {
            return base.NotFound(new OutputView { c = OutCode.NotFound, d = null, m = message });
        }

        /// <summary>
        /// 记录信息日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="args">参数</param>
        protected void LogInformation(string message, params object[] args)
        {
            Logger?.LogInformation(message, args);
        }

        /// <summary>
        /// 记录警告日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="args">参数</param>
        protected void LogWarning(string message, params object[] args)
        {
            Logger?.LogWarning(message, args);
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="message">日志消息</param>
        /// <param name="args">参数</param>
        protected void LogError(Exception exception, string message, params object[] args)
        {
            Logger?.LogError(exception, message, args);
        }

        /// <summary>
        /// 基础控制器健康检查接口
        /// </summary>
        /// <returns>健康状态</returns>
        [HttpGet("health")]
        public  ActionResult Health()
        {
            var controllerName = GetType().Name;
            LogInformation("健康检查被调用: {ControllerName}", controllerName);

            return Success(new
            {
                controller = controllerName,
                status = "健康",
                timestamp = DateTime.Now,
                message = $"{controllerName} 运行正常"
            }, "健康检查通过");
        }
    }
}
