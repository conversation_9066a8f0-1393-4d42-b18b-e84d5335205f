/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果要增加方法请在当前目录下Expands文件夹RoleController编写
 */
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using SSIC.Infrastructure.BaseController;
using SSIC.Modules.Auth.Services.Interfaces;
using System;

namespace SSIC.Modules.Auth.Controllers
{
    /// <summary>
    /// Role控制器（角色管理）
    /// 提供Role实体的Web API接口
    /// </summary>
    public partial class RoleController : BaseController
    {
        /// <summary>
        /// 初始化Role控制器
        /// </summary>
        /// <param name="serviceProvider">服务提供者</param>
        /// <param name="logger">日志记录器</param>
        public RoleController(IServiceProvider serviceProvider, ILogger<RoleController> logger)
            : base(serviceProvider, logger)
        {
        }

        /// <summary>
        /// 获取Role服务
        /// </summary>
        protected IRoleService RoleService => GetService<IRoleService>();
    }
}