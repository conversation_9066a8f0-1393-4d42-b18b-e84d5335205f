using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using SSIC.Infrastructure.Authentication;
using SSIC.Infrastructure.Startups.Common;
using System.Reflection;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.IO;
using System.Linq;
using Swashbuckle.AspNetCore.SwaggerGen;
using Microsoft.AspNetCore.OpenApi;
using Microsoft.OpenApi;

namespace SSIC.Infrastructure.Startups.OpenAPI
{
    /// <summary>
    /// OpenApi 统一配置扩展
    /// </summary>
    public static class OpenApiConfigurationExtensions
    {
        /// <summary>
        /// 添加 SSIC OpenApi 配置
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="environment">环境信息</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddSSICOpenApi(this IServiceCollection services, IWebHostEnvironment environment)
        {
            // 注册统一的文档转换器
            services.AddScoped<UnifiedDocumentTransformer>();
            
            // 注册统一的刷新服务（修复生命周期问题）
            services.AddSingleton<IUnifiedOpenApiRefreshService, UnifiedOpenApiRefreshService>();
            services.AddSingleton<ISimpleOpenApiRefreshService, SimpleOpenApiRefreshService>();

            // 添加Swashbuckle服务来支持XML注释
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo
                {
                    Title = "SSIC 模块化API",
                    Version = "v1",
                    Description = "SSIC 模块化架构API文档，支持动态模块加载"
                });

                // 包含XML注释
                var xmlFiles = Directory.GetFiles(AppContext.BaseDirectory, "*.xml");
                foreach (var xmlFile in xmlFiles)
                {
                    if (Path.GetFileNameWithoutExtension(xmlFile).StartsWith("SSIC"))
                    {
                        c.IncludeXmlComments(xmlFile);
                        Console.WriteLine($"✓ 已加载XML文档: {Path.GetFileName(xmlFile)}");
                    }
                }
            });

            // 配置 OpenApi
            services.AddOpenApi(options =>
            {
                // 配置XML注释文档
                ConfigureXmlComments(options, environment);

                // 基础文档信息
                options.AddDocumentTransformer((document, context, cancellationToken) =>
                {
                    document.Info = new Microsoft.OpenApi.Models.OpenApiInfo
                    {
                        Title = "SSIC 模块化API",
                        Version = "V1",
                        Description = "SSIC 模块化架构API文档，支持动态模块加载",
                        Contact = new Microsoft.OpenApi.Models.OpenApiContact
                        {
                            Name = "SSIC Team",
                            Email = "<EMAIL>"
                        }
                    };

                    return Task.CompletedTask;
                });

                // 添加统一的文档转换器
                options.AddDocumentTransformer<UnifiedDocumentTransformer>();

                // 添加模块标签转换器和操作ID设置
                options.AddOperationTransformer((operation, context, cancellationToken) =>
                {
                    // 为动态路由添加标签和设置操作ID
                    if (context.Description.ActionDescriptor is ControllerActionDescriptor controllerAction)
                    {
                        var assemblyName = controllerAction.ControllerTypeInfo.Assembly.GetName().Name;
                        if (assemblyName?.StartsWith("SSIC.Modules.") == true)
                        {
                            var moduleName = ModuleUtilities.ExtractModuleName(assemblyName);
                            if (!string.IsNullOrEmpty(moduleName))
                            {
                                operation.Tags = new List<Microsoft.OpenApi.Models.OpenApiTag> { new Microsoft.OpenApi.Models.OpenApiTag { Name = moduleName } };

                                // 设置操作ID为方法名称+路由模板格式
                                var operationId = GenerateCustomOperationId(controllerAction);
                                operation.OperationId = operationId;
                            }
                        }
                    }

                    return Task.CompletedTask;
                });

                // 生产环境添加安全方案
                if (!environment.IsDevelopment())
                {
                    options.AddDocumentTransformer<BearerSecuritySchemeTransformer>();
                }

                // 添加安全方案配置
                options.AddDocumentTransformer((document, context, cancellationToken) =>
                {
                    if (!environment.IsDevelopment())
                    {
                        ConfigureSecurityScheme(document);
                    }
                    return Task.CompletedTask;
                });
            });

            return services;
        }

        /// <summary>
        /// 生成自定义操作ID
        /// </summary>
        /// <param name="controllerAction">控制器动作描述符</param>
        /// <returns>操作ID</returns>
        private static string GenerateCustomOperationId(ControllerActionDescriptor controllerAction)
        {
            var controllerName = controllerAction.ControllerName;
            var actionName = controllerAction.ActionName;
            var httpMethods = controllerAction.ActionConstraints?
                .OfType<Microsoft.AspNetCore.Mvc.ActionConstraints.HttpMethodActionConstraint>()
                .FirstOrDefault()?.HttpMethods?.FirstOrDefault() ?? "GET";

            return $"{controllerName}_{actionName}_{httpMethods}";
        }

        /// <summary>
        /// 配置安全方案
        /// </summary>
        /// <param name="document">OpenApi 文档</param>
        private static void ConfigureSecurityScheme(Microsoft.OpenApi.Models.OpenApiDocument document)
        {
            document.Components.SecuritySchemes.Add("Bearer", new Microsoft.OpenApi.Models.OpenApiSecurityScheme
            {
                Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
                Name = "Authorization",
                In = Microsoft.OpenApi.Models.ParameterLocation.Header,
                Type = Microsoft.OpenApi.Models.SecuritySchemeType.ApiKey,
                Scheme = "Bearer"
            });

            document.SecurityRequirements.Add(new Microsoft.OpenApi.Models.OpenApiSecurityRequirement
            {
                {
                    new Microsoft.OpenApi.Models.OpenApiSecurityScheme
                    {
                        Reference = new Microsoft.OpenApi.Models.OpenApiReference
                        {
                            Type = Microsoft.OpenApi.Models.ReferenceType.SecurityScheme,
                            Id = "Bearer"
                        }
                    },
                    new string[] {}
                }
            });
        }

        /// <summary>
        /// 配置XML注释文档
        /// </summary>
        /// <param name="options">OpenAPI选项</param>
        /// <param name="environment">环境信息</param>
        private static void ConfigureXmlComments(OpenApiOptions options, IWebHostEnvironment environment)
        {
            try
            {
                var xmlFiles = Directory.GetFiles(AppContext.BaseDirectory, "*.xml");
                foreach (var xmlFile in xmlFiles)
                {
                    if (Path.GetFileNameWithoutExtension(xmlFile).StartsWith("SSIC"))
                    {
                        // 这里可以添加XML注释的配置逻辑
                        Console.WriteLine($"✓ 发现XML文档: {Path.GetFileName(xmlFile)}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ 配置XML注释时出错: {ex.Message}");
            }
        }
    }
}
