# 代码生成路径说明

## 📁 **生成路径概览**

代码生成器会将生成的文件保存到以下位置：

```
F:\SSIC\Host\SSIC.Modules\
```

## 🎯 **路径计算逻辑**

### 1. 路径计算步骤

1. **获取当前工作目录**：
   ```csharp
   var ServerHost = Directory.GetCurrentDirectory();
   // 结果：F:\SSIC\Host\SSIC.DevTools
   ```

2. **计算项目根目录**：
   ```csharp
   var HostPath = ServerHost.Substring(0, ServerHost.LastIndexOf("\\"));
   // 结果：F:\SSIC\Host
   ```

3. **设置模块生成路径**：
   ```csharp
   var ModulesPath = $"{HostPath}\\SSIC.Modules\\";
   // 结果：F:\SSIC\Host\SSIC.Modules\
   ```

### 2. 相关路径

- **实体路径**：`F:\SSIC\Host\SSIC.Entity\`
- **模板路径**：`F:\SSIC\Host\SSIC.Infrastructure\Template\`
- **生成路径**：`F:\SSIC\Host\SSIC.Modules\`

## 🏗️ **生成的完整目录结构**

### 模块化项目结构

```
F:\SSIC\Host\SSIC.Modules\
├── SSIC.Modules.Auth\                          # Auth模块
│   ├── SSIC.Modules.Auth.Services\             # 服务层项目
│   │   ├── Interfaces\                         # 服务接口
│   │   │   ├── IUserService.cs
│   │   │   ├── IRoleService.cs
│   │   │   ├── IPermissionService.cs
│   │   │   └── Expands\                        # 扩展接口
│   │   │       ├── IUserService.cs
│   │   │       ├── IRoleService.cs
│   │   │       └── IPermissionService.cs
│   │   ├── Implementations\                    # 服务实现
│   │   │   ├── UserService.cs
│   │   │   ├── RoleService.cs
│   │   │   ├── PermissionService.cs
│   │   │   └── Expands\                        # 扩展实现
│   │   │       ├── UserService.cs
│   │   │       ├── RoleService.cs
│   │   │       └── PermissionService.cs
│   │   ├── ActionModels\                       # API模型
│   │   ├── Configuration\                      # 配置类
│   │   └── SSIC.Modules.Auth.Services.csproj   # 项目文件
│   ├── SSIC.Modules.Auth.Controller\           # 控制器项目
│   │   ├── Controllers\                        # 控制器
│   │   │   ├── UserController.cs
│   │   │   ├── RoleController.cs
│   │   │   ├── PermissionController.cs
│   │   │   └── Expands\                        # 扩展控制器
│   │   │       ├── UserController.cs
│   │   │       ├── RoleController.cs
│   │   │       └── PermissionController.cs
│   │   ├── Program.cs                          # 启动文件
│   │   └── SSIC.Modules.Auth.Controller.csproj # 项目文件
│   └── SSIC.Modules.Auth.sln                   # 解决方案文件
├── SSIC.Modules.Sys\                           # 系统模块
│   ├── SSIC.Modules.Sys.Services\
│   ├── SSIC.Modules.Sys.Controllers\
│   └── SSIC.Modules.Sys.sln
└── SSIC.Modules.其他模块\
    └── ... (类似结构)
```

## 📋 **实际生成示例**

### 当前已存在的结构

基于你的项目，当前已经存在：

```
F:\SSIC\Host\SSIC.Modules\
└── SSIC.Modules.Auth\
    ├── SSIC.Modules.Auth.Controller\           # 注意：这里是Controller而不是Controllers
    │   ├── Controllers\
    │   ├── Program.cs
    │   └── SSIC.Modules.Auth.Controller.csproj
    ├── SSIC.Modules.Auth.Services\
    │   ├── ActionModels\
    │   ├── Configuration\
    │   ├── Implementations\
    │   ├── Interfaces\
    │   └── SSIC.Modules.Auth.Services.csproj
    └── SSIC.Modules.Auth.sln
```

### 新代码生成器会创建

新的代码生成器会创建：

```
F:\SSIC\Host\SSIC.Modules\
└── SSIC.Modules.Auth\
    ├── SSIC.Modules.Auth.Controller\           # 注意：这里是Controller（单数）
    │   ├── Controllers\
    │   ├── Program.cs
    │   └── SSIC.Modules.Auth.Controller.csproj
    └── ... (其他文件)
```

## ⚠️ **注意事项**

### 1. 命名差异

- **现有项目**：`SSIC.Modules.Auth.Controller` (单数)
- **新生成器**：`SSIC.Modules.Auth.Controller` (单数) - 已修正

### 2. 文件覆盖

- **基础文件**：会被覆盖（如 `UserService.cs`、`UserController.cs`）
- **扩展文件**：不会覆盖（如 `Expands/UserService.cs`）

### 3. 生成条件

代码生成器会根据以下条件生成文件：

1. **实体文件存在**：`F:\SSIC\Host\SSIC.Entity\{ModuleName}\{EntityName}.cs`
2. **模板文件存在**：`F:\SSIC\Host\SSIC.Infrastructure\Template\`
3. **指定的服务/控制器名称**：如果指定了数组参数

## 🔍 **如何查看生成结果**

### 1. 通过文件资源管理器

直接打开文件夹：`F:\SSIC\Host\SSIC.Modules\`

### 2. 通过VS Code

在VS Code中打开：`F:\SSIC\Host\SSIC.Modules\`

### 3. 通过命令行

```bash
cd F:\SSIC\Host\SSIC.Modules
dir /s
```

### 4. 通过代码生成器日志

查看生成器的日志输出，会显示每个生成的文件路径。

## 🚀 **生成后的下一步**

1. **打开解决方案**：双击 `.sln` 文件
2. **构建项目**：`dotnet build`
3. **运行项目**：`dotnet run`
4. **查看生成的代码**：检查Services和Controllers文件

## 📞 **路径问题排查**

如果生成的文件找不到，请检查：

1. **工作目录**：确保从正确的目录运行代码生成器
2. **权限问题**：确保有写入权限
3. **路径分隔符**：Windows使用 `\`，Linux/Mac使用 `/`
4. **磁盘空间**：确保有足够的磁盘空间

## 🎯 **总结**

- **生成根路径**：`F:\SSIC\Host\SSIC.Modules\`
- **模块路径**：`F:\SSIC\Host\SSIC.Modules\SSIC.Modules.{ModuleName}\`
- **服务项目**：`F:\SSIC\Host\SSIC.Modules\SSIC.Modules.{ModuleName}\SSIC.Modules.{ModuleName}.Services\`
- **控制器项目**：`F:\SSIC\Host\SSIC.Modules\SSIC.Modules.{ModuleName}\SSIC.Modules.{ModuleName}.Controller\`

所有生成的文件都会保存在这些路径下，并且会自动创建必要的子目录结构。
