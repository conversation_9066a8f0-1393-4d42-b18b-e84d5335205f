{"@t":"2025-08-19T16:00:47.6678785Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:00:47.7805932Z","@mt":"动态端点管理器初始化完成","SourceContext":"ApplicationBuilderExtensions","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:00:47.8273032Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:00:47.8334487Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:00:47.8345244Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:00:47.8352862Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:00:47.8363446Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:00:47.8371921Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:00:47.9218223Z","@mt":"Now listening on: {address}","address":"http://localhost:5000","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:00:47.9235721Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:00:47.9247702Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:00:47.9257904Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.Controller","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:01:42.5440321Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:01:42.6537436Z","@mt":"动态端点管理器初始化完成","SourceContext":"ApplicationBuilderExtensions","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:01:42.7336786Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:01:42.7430708Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:01:42.7455976Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:01:42.7479328Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:01:42.7500270Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:01:42.7522669Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:01:42.9891455Z","@mt":"Now listening on: {address}","address":"https://localhost:7000","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:01:42.9911316Z","@mt":"Now listening on: {address}","address":"http://localhost:5000","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:01:43.0698524Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:01:43.0712382Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:01:43.0724918Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.Controller","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:01:45.3257854Z","@mt":"初始化模块热加载监控...","@tr":"3e8bf87be17fe70cf9cf43483e3ba4aa","@sp":"e688ff4169c9d5d2","SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNEV76ODSCLH:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV76ODSCLH","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:01:45.3296344Z","@mt":"模块热加载监控初始化完成，监控 {DirectoryCount} 个目录","@tr":"3e8bf87be17fe70cf9cf43483e3ba4aa","@sp":"e688ff4169c9d5d2","DirectoryCount":0,"SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNEV76ODSCLH:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV76ODSCLH","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:01:45.3325361Z","@mt":"初始化动态路由系统...","@tr":"3e8bf87be17fe70cf9cf43483e3ba4aa","@sp":"e688ff4169c9d5d2","SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNEV76ODSCLH:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV76ODSCLH","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:01:45.3347546Z","@mt":"发现 {Count} 个模块程序集","@tr":"3e8bf87be17fe70cf9cf43483e3ba4aa","@sp":"e688ff4169c9d5d2","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV76ODSCLH:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV76ODSCLH","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:01:45.3379170Z","@mt":"动态路由系统初始化完成，发现 {ModuleCount} 个模块","@tr":"3e8bf87be17fe70cf9cf43483e3ba4aa","@sp":"e688ff4169c9d5d2","ModuleCount":2,"SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNEV76ODSCLH:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV76ODSCLH","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:01:45.3409376Z","@mt":"发现 {Count} 个模块程序集","@tr":"3e8bf87be17fe70cf9cf43483e3ba4aa","@sp":"e688ff4169c9d5d2","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV76ODSCLH:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV76ODSCLH","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:01:45.3452593Z","@mt":"首次请求触发端点刷新","@tr":"3e8bf87be17fe70cf9cf43483e3ba4aa","@sp":"e688ff4169c9d5d2","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEV76ODSCLH:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV76ODSCLH","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:01:45.3471721Z","@mt":"发现 {Count} 个模块程序集","@tr":"3e8bf87be17fe70cf9cf43483e3ba4aa","@sp":"e688ff4169c9d5d2","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV76ODSCLH:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV76ODSCLH","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:01:45.3491740Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"3e8bf87be17fe70cf9cf43483e3ba4aa","@sp":"e688ff4169c9d5d2","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEV76ODSCLH:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV76ODSCLH","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:01:45.3503686Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"3e8bf87be17fe70cf9cf43483e3ba4aa","@sp":"e688ff4169c9d5d2","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEV76ODSCLH:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV76ODSCLH","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:01:45.3512960Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"3e8bf87be17fe70cf9cf43483e3ba4aa","@sp":"e688ff4169c9d5d2","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEV76ODSCLH:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV76ODSCLH","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:01:45.3537010Z","@mt":"发现 {Count} 个模块程序集","@tr":"3e8bf87be17fe70cf9cf43483e3ba4aa","@sp":"e688ff4169c9d5d2","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV76ODSCLH:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV76ODSCLH","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:01:45.3551441Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"3e8bf87be17fe70cf9cf43483e3ba4aa","@sp":"e688ff4169c9d5d2","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEV76ODSCLH:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV76ODSCLH","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:01:45.3560261Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"3e8bf87be17fe70cf9cf43483e3ba4aa","@sp":"e688ff4169c9d5d2","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEV76ODSCLH:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV76ODSCLH","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:01:45.3568393Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"3e8bf87be17fe70cf9cf43483e3ba4aa","@sp":"e688ff4169c9d5d2","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEV76ODSCLH:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV76ODSCLH","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:01:45.3575925Z","@mt":"请求触发的端点刷新完成，发现 {ModuleCount} 个模块，{EndpointCount} 个端点","@tr":"3e8bf87be17fe70cf9cf43483e3ba4aa","@sp":"e688ff4169c9d5d2","ModuleCount":2,"EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEV76ODSCLH:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV76ODSCLH","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:01:45.4513360Z","@mt":"发现 {Count} 个模块程序集","@tr":"f5b621579e3db37c3a4753974d12d79d","@sp":"e8bdadbffd04d371","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV76ODSCLH:00000005","RequestPath":"/scalar/scalar.aspnetcore.js","ConnectionId":"0HNEV76ODSCLH","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:01:45.4514373Z","@mt":"发现 {Count} 个模块程序集","@tr":"cd6942701b533210f7f878026fca3e2a","@sp":"bdfd0f9733079d2b","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV76ODSCLH:00000007","RequestPath":"/scalar/scalar.js","ConnectionId":"0HNEV76ODSCLH","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:01:46.1505671Z","@mt":"发现 {Count} 个模块程序集","@tr":"6835c78687a201710a36c3fe466287b2","@sp":"da93bcf163b473ba","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV76ODSCLH:0000000D","RequestPath":"/favicon.ico","ConnectionId":"0HNEV76ODSCLH","MachineName":"LAPTOP-F6SGUTN5","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:01:52.2008096Z","@mt":"发现 {Count} 个模块程序集","@tr":"d779f18b954d129d8a5fcb0583bdc14b","@sp":"f77ee72d05e954dc","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV76ODSCLI:********","RequestPath":"/api/auth/permission/health","ConnectionId":"0HNEV76ODSCLI","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:01:52.2780236Z","@mt":"健康检查被调用: {ControllerName}","@tr":"d779f18b954d129d8a5fcb0583bdc14b","@sp":"f77ee72d05e954dc","ControllerName":"PermissionController","SourceContext":"SSIC.Modules.Auth.Controllers.PermissionController","ActionId":"0944addb-cbb7-4633-9fb7-fb6cd9f19a5b","ActionName":"SSIC.Modules.Auth.Controllers.PermissionController.Health (SSIC.Modules.Auth.Controller)","RequestId":"0HNEV76ODSCLI:********","RequestPath":"/api/auth/permission/health","ConnectionId":"0HNEV76ODSCLI","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:02:11.1542923Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:02:11.2734814Z","@mt":"动态端点管理器初始化完成","SourceContext":"ApplicationBuilderExtensions","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:02:11.3963872Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:02:11.4059443Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:02:11.4080580Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:02:11.4100722Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:02:11.4122932Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:02:11.4138132Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:02:11.6369941Z","@mt":"Now listening on: {address}","address":"https://localhost:7000","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:02:11.6394676Z","@mt":"Now listening on: {address}","address":"http://localhost:5000","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:02:11.6823135Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:02:11.6842340Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:02:11.6862783Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.Controller","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:02:12.4330699Z","@mt":"初始化模块热加载监控...","@tr":"d03e35f36aa7883ecdc2b8c7d5c10314","@sp":"38c393edb0c0e841","SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNEV770VK9H7:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV770VK9H7","MachineName":"LAPTOP-F6SGUTN5","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:02:12.4364994Z","@mt":"模块热加载监控初始化完成，监控 {DirectoryCount} 个目录","@tr":"d03e35f36aa7883ecdc2b8c7d5c10314","@sp":"38c393edb0c0e841","DirectoryCount":0,"SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNEV770VK9H7:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV770VK9H7","MachineName":"LAPTOP-F6SGUTN5","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:02:12.4390266Z","@mt":"初始化动态路由系统...","@tr":"d03e35f36aa7883ecdc2b8c7d5c10314","@sp":"38c393edb0c0e841","SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNEV770VK9H7:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV770VK9H7","MachineName":"LAPTOP-F6SGUTN5","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:02:12.4411323Z","@mt":"发现 {Count} 个模块程序集","@tr":"d03e35f36aa7883ecdc2b8c7d5c10314","@sp":"38c393edb0c0e841","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV770VK9H7:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV770VK9H7","MachineName":"LAPTOP-F6SGUTN5","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:02:12.4434138Z","@mt":"动态路由系统初始化完成，发现 {ModuleCount} 个模块","@tr":"d03e35f36aa7883ecdc2b8c7d5c10314","@sp":"38c393edb0c0e841","ModuleCount":2,"SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNEV770VK9H7:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV770VK9H7","MachineName":"LAPTOP-F6SGUTN5","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:02:12.4460928Z","@mt":"发现 {Count} 个模块程序集","@tr":"d03e35f36aa7883ecdc2b8c7d5c10314","@sp":"38c393edb0c0e841","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV770VK9H7:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV770VK9H7","MachineName":"LAPTOP-F6SGUTN5","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:02:12.4495507Z","@mt":"首次请求触发端点刷新","@tr":"d03e35f36aa7883ecdc2b8c7d5c10314","@sp":"38c393edb0c0e841","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEV770VK9H7:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV770VK9H7","MachineName":"LAPTOP-F6SGUTN5","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:02:12.4506519Z","@mt":"发现 {Count} 个模块程序集","@tr":"d03e35f36aa7883ecdc2b8c7d5c10314","@sp":"38c393edb0c0e841","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV770VK9H7:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV770VK9H7","MachineName":"LAPTOP-F6SGUTN5","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:02:12.4523405Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"d03e35f36aa7883ecdc2b8c7d5c10314","@sp":"38c393edb0c0e841","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEV770VK9H7:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV770VK9H7","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:02:12.4544634Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"d03e35f36aa7883ecdc2b8c7d5c10314","@sp":"38c393edb0c0e841","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEV770VK9H7:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV770VK9H7","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:02:12.4564078Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"d03e35f36aa7883ecdc2b8c7d5c10314","@sp":"38c393edb0c0e841","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEV770VK9H7:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV770VK9H7","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:02:12.4586501Z","@mt":"发现 {Count} 个模块程序集","@tr":"d03e35f36aa7883ecdc2b8c7d5c10314","@sp":"38c393edb0c0e841","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV770VK9H7:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV770VK9H7","MachineName":"LAPTOP-F6SGUTN5","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:02:12.4602942Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"d03e35f36aa7883ecdc2b8c7d5c10314","@sp":"38c393edb0c0e841","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEV770VK9H7:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV770VK9H7","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:02:12.4619354Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"d03e35f36aa7883ecdc2b8c7d5c10314","@sp":"38c393edb0c0e841","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEV770VK9H7:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV770VK9H7","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:02:12.4631011Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"d03e35f36aa7883ecdc2b8c7d5c10314","@sp":"38c393edb0c0e841","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEV770VK9H7:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV770VK9H7","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:02:12.4643501Z","@mt":"请求触发的端点刷新完成，发现 {ModuleCount} 个模块，{EndpointCount} 个端点","@tr":"d03e35f36aa7883ecdc2b8c7d5c10314","@sp":"38c393edb0c0e841","ModuleCount":2,"EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEV770VK9H7:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV770VK9H7","MachineName":"LAPTOP-F6SGUTN5","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:02:12.5530359Z","@mt":"发现 {Count} 个模块程序集","@tr":"6f8e353ad11701be68ea813fd3968eba","@sp":"429611b3dabe2b48","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV770VK9H7:00000005","RequestPath":"/scalar/scalar.aspnetcore.js","ConnectionId":"0HNEV770VK9H7","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:02:12.5571518Z","@mt":"发现 {Count} 个模块程序集","@tr":"aa8cffdb89684aad8aaacc3e5991e63c","@sp":"f5bde7ab0e7700e2","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV770VK9H7:00000007","RequestPath":"/scalar/scalar.js","ConnectionId":"0HNEV770VK9H7","MachineName":"LAPTOP-F6SGUTN5","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T16:02:13.0204365Z","@mt":"发现 {Count} 个模块程序集","@tr":"334de5d0a2ce6306b66bf61cd6a03aff","@sp":"c3972697ac6292f6","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV770VK9H7:0000000D","RequestPath":"/favicon.ico","ConnectionId":"0HNEV770VK9H7","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:18.3291676Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:18.4246719Z","@mt":"动态端点管理器初始化完成","SourceContext":"ApplicationBuilderExtensions","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:18.4628087Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:18.4685502Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:18.4686620Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:18.4688308Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:18.4693645Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:18.4696629Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:18.6128681Z","@mt":"Now listening on: {address}","address":"https://localhost:55826","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:18.6134506Z","@mt":"Now listening on: {address}","address":"http://localhost:55835","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:18.6368277Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:18.6369601Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:18.6369881Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.Controller","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:33.8044289Z","@mt":"初始化模块热加载监控...","@tr":"0289a8e7efe26fad2860ae4b384223dc","@sp":"9399248c7935150b","SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNEV91B0M643:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV91B0M643","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:33.8057515Z","@mt":"模块热加载监控初始化完成，监控 {DirectoryCount} 个目录","@tr":"0289a8e7efe26fad2860ae4b384223dc","@sp":"9399248c7935150b","DirectoryCount":0,"SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNEV91B0M643:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV91B0M643","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:33.8069529Z","@mt":"初始化动态路由系统...","@tr":"0289a8e7efe26fad2860ae4b384223dc","@sp":"9399248c7935150b","SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNEV91B0M643:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV91B0M643","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:33.8077493Z","@mt":"发现 {Count} 个模块程序集","@tr":"0289a8e7efe26fad2860ae4b384223dc","@sp":"9399248c7935150b","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV91B0M643:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV91B0M643","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:33.8087957Z","@mt":"动态路由系统初始化完成，发现 {ModuleCount} 个模块","@tr":"0289a8e7efe26fad2860ae4b384223dc","@sp":"9399248c7935150b","ModuleCount":2,"SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNEV91B0M643:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV91B0M643","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:33.8099891Z","@mt":"发现 {Count} 个模块程序集","@tr":"0289a8e7efe26fad2860ae4b384223dc","@sp":"9399248c7935150b","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV91B0M643:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV91B0M643","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:33.8121734Z","@mt":"首次请求触发端点刷新","@tr":"0289a8e7efe26fad2860ae4b384223dc","@sp":"9399248c7935150b","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEV91B0M643:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV91B0M643","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:33.8126922Z","@mt":"发现 {Count} 个模块程序集","@tr":"0289a8e7efe26fad2860ae4b384223dc","@sp":"9399248c7935150b","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV91B0M643:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV91B0M643","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:33.8131172Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"0289a8e7efe26fad2860ae4b384223dc","@sp":"9399248c7935150b","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEV91B0M643:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV91B0M643","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:33.8132244Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"0289a8e7efe26fad2860ae4b384223dc","@sp":"9399248c7935150b","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEV91B0M643:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV91B0M643","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:33.8132911Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"0289a8e7efe26fad2860ae4b384223dc","@sp":"9399248c7935150b","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEV91B0M643:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV91B0M643","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:33.8138819Z","@mt":"发现 {Count} 个模块程序集","@tr":"0289a8e7efe26fad2860ae4b384223dc","@sp":"9399248c7935150b","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV91B0M643:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV91B0M643","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:33.8141870Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"0289a8e7efe26fad2860ae4b384223dc","@sp":"9399248c7935150b","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEV91B0M643:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV91B0M643","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:33.8142919Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"0289a8e7efe26fad2860ae4b384223dc","@sp":"9399248c7935150b","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEV91B0M643:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV91B0M643","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:33.8143344Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"0289a8e7efe26fad2860ae4b384223dc","@sp":"9399248c7935150b","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEV91B0M643:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV91B0M643","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:33.8145348Z","@mt":"请求触发的端点刷新完成，发现 {ModuleCount} 个模块，{EndpointCount} 个端点","@tr":"0289a8e7efe26fad2860ae4b384223dc","@sp":"9399248c7935150b","ModuleCount":2,"EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEV91B0M643:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV91B0M643","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:33.8421421Z","@mt":"发现 {Count} 个模块程序集","@tr":"c41313249ad33e7bf41f8f7e1f839ddd","@sp":"24a14bb119013636","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV91B0M645:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEV91B0M645","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:33.9087934Z","@mt":"发现 {Count} 个模块程序集","@tr":"d5ef4208bb6d6aa6ede8f18cfb1913f8","@sp":"f139ab8c0c5bce64","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV91B0M645:00000003","RequestPath":"/scalar/scalar.aspnetcore.js","ConnectionId":"0HNEV91B0M645","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:33.9087934Z","@mt":"发现 {Count} 个模块程序集","@tr":"5037615cde22a2301fa042070048994e","@sp":"a83f504d011116aa","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV91B0M645:00000007","RequestPath":"/scalar/scalar.js","ConnectionId":"0HNEV91B0M645","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:37.8143017Z","@mt":"发现 {Count} 个模块程序集","@tr":"8a14a8e3fe2ab0c495647e7b417db876","@sp":"61fc7c4aa8ba5c0b","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV91B0M645:0000000D","RequestPath":"/api/auth/permission/health","ConnectionId":"0HNEV91B0M645","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:37.8285782Z","@mt":"健康检查被调用: {ControllerName}","@tr":"8a14a8e3fe2ab0c495647e7b417db876","@sp":"61fc7c4aa8ba5c0b","ControllerName":"PermissionController","SourceContext":"SSIC.Modules.Auth.Controllers.PermissionController","ActionId":"bbd40f11-864e-41d9-b677-8116197572e8","ActionName":"SSIC.Modules.Auth.Controllers.PermissionController.Health (SSIC.Modules.Auth.Controller)","RequestId":"0HNEV91B0M645:0000000D","RequestPath":"/api/auth/permission/health","ConnectionId":"0HNEV91B0M645","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:46:45.1903057Z","@mt":"发现 {Count} 个模块程序集","@tr":"3fad4ced7d699b0652a51eb70c8e7fee","@sp":"f63aac2cf15c9d94","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEV91B0M645:0000000F","RequestPath":"/favicon.ico","ConnectionId":"0HNEV91B0M645","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:54.1669176Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:54.2811987Z","@mt":"动态端点管理器初始化完成","SourceContext":"ApplicationBuilderExtensions","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:54.3748213Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:54.3815108Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:54.3816700Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:54.3818711Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:54.3824437Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:54.3827913Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:54.6859149Z","@mt":"Now listening on: {address}","address":"https://localhost:56893","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:54.6865560Z","@mt":"Now listening on: {address}","address":"http://localhost:56894","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:54.7294818Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:54.7296497Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T17:49:54.7296948Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.Controller","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T09:12:31.6207445Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T09:12:31.7132174Z","@mt":"动态端点管理器初始化完成","SourceContext":"ApplicationBuilderExtensions","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T09:12:31.7909567Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T09:12:31.7981105Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T09:12:31.7982485Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T09:12:31.7984262Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T09:12:31.7989785Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T09:12:31.7993793Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T09:12:32.0089713Z","@mt":"Now listening on: {address}","address":"https://localhost:57334","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T09:12:32.0095510Z","@mt":"Now listening on: {address}","address":"http://localhost:57335","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T09:12:32.0469460Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T09:12:32.0471329Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T09:12:32.0471684Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.Controller","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T09:20:31.1954109Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T09:20:31.2885030Z","@mt":"动态端点管理器初始化完成","SourceContext":"ApplicationBuilderExtensions","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T09:20:31.3840458Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T09:20:31.3900362Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T09:20:31.3901703Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T09:20:31.3903196Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T09:20:31.3908251Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T09:20:31.3911687Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T09:20:31.7171541Z","@mt":"Now listening on: {address}","address":"https://localhost:59654","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T09:20:31.7178192Z","@mt":"Now listening on: {address}","address":"http://localhost:59661","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T09:20:31.7699690Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T09:20:31.7702096Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T09:20:31.7702567Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.Controller","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:27:47.6280992Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:27:47.6873500Z","@mt":"动态端点管理器初始化完成","SourceContext":"ApplicationBuilderExtensions","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:27:47.8014951Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:27:47.8102280Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:27:47.8104249Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:27:47.8107290Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:27:47.8114349Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:27:47.8118249Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:27:48.0965709Z","@mt":"Now listening on: {address}","address":"https://localhost:52104","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:27:48.0980444Z","@mt":"Now listening on: {address}","address":"http://localhost:52106","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:27:48.1483657Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:27:48.1485306Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:27:48.1485679Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.Controller","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:27:49.1851951Z","@mt":"初始化模块热加载监控...","@tr":"8d5544f57b460fe96d94e5c75465c9a4","@sp":"b19a959e8c17c5c1","SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNEVQGQR6RH0:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVQGQR6RH0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:27:49.1866052Z","@mt":"模块热加载监控初始化完成，监控 {DirectoryCount} 个目录","@tr":"8d5544f57b460fe96d94e5c75465c9a4","@sp":"b19a959e8c17c5c1","DirectoryCount":0,"SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNEVQGQR6RH0:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVQGQR6RH0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:27:49.1875461Z","@mt":"初始化动态路由系统...","@tr":"8d5544f57b460fe96d94e5c75465c9a4","@sp":"b19a959e8c17c5c1","SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNEVQGQR6RH0:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVQGQR6RH0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:27:49.1887158Z","@mt":"发现 {Count} 个模块程序集","@tr":"8d5544f57b460fe96d94e5c75465c9a4","@sp":"b19a959e8c17c5c1","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVQGQR6RH0:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVQGQR6RH0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:27:49.1906901Z","@mt":"动态路由系统初始化完成，发现 {ModuleCount} 个模块","@tr":"8d5544f57b460fe96d94e5c75465c9a4","@sp":"b19a959e8c17c5c1","ModuleCount":2,"SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNEVQGQR6RH0:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVQGQR6RH0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:27:49.1922106Z","@mt":"发现 {Count} 个模块程序集","@tr":"8d5544f57b460fe96d94e5c75465c9a4","@sp":"b19a959e8c17c5c1","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVQGQR6RH0:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVQGQR6RH0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:27:49.1956481Z","@mt":"首次请求触发端点刷新","@tr":"8d5544f57b460fe96d94e5c75465c9a4","@sp":"b19a959e8c17c5c1","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEVQGQR6RH0:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVQGQR6RH0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:27:49.1966559Z","@mt":"发现 {Count} 个模块程序集","@tr":"8d5544f57b460fe96d94e5c75465c9a4","@sp":"b19a959e8c17c5c1","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVQGQR6RH0:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVQGQR6RH0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:27:49.1972872Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"8d5544f57b460fe96d94e5c75465c9a4","@sp":"b19a959e8c17c5c1","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVQGQR6RH0:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVQGQR6RH0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":11,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:27:49.1973840Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"8d5544f57b460fe96d94e5c75465c9a4","@sp":"b19a959e8c17c5c1","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVQGQR6RH0:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVQGQR6RH0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":11,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:27:49.1974322Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"8d5544f57b460fe96d94e5c75465c9a4","@sp":"b19a959e8c17c5c1","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVQGQR6RH0:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVQGQR6RH0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":11,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:27:49.1983037Z","@mt":"发现 {Count} 个模块程序集","@tr":"8d5544f57b460fe96d94e5c75465c9a4","@sp":"b19a959e8c17c5c1","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVQGQR6RH0:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVQGQR6RH0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:27:49.1986486Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"8d5544f57b460fe96d94e5c75465c9a4","@sp":"b19a959e8c17c5c1","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVQGQR6RH0:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVQGQR6RH0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":11,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:27:49.1987211Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"8d5544f57b460fe96d94e5c75465c9a4","@sp":"b19a959e8c17c5c1","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVQGQR6RH0:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVQGQR6RH0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":11,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:27:49.1987575Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"8d5544f57b460fe96d94e5c75465c9a4","@sp":"b19a959e8c17c5c1","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVQGQR6RH0:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVQGQR6RH0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":11,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:27:49.1989099Z","@mt":"请求触发的端点刷新完成，发现 {ModuleCount} 个模块，{EndpointCount} 个端点","@tr":"8d5544f57b460fe96d94e5c75465c9a4","@sp":"b19a959e8c17c5c1","ModuleCount":2,"EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEVQGQR6RH0:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVQGQR6RH0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:27:49.2061737Z","@mt":"发现 {Count} 个模块程序集","@tr":"b798f7f9d145b4956554273c875b75ee","@sp":"edf49e2d201a1940","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVQGQR6RH0:00000002","RequestPath":"/dapr/subscribe","ConnectionId":"0HNEVQGQR6RH0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:30:30.2660811Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:30:30.3761238Z","@mt":"动态端点管理器初始化完成","SourceContext":"ApplicationBuilderExtensions","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:30:30.4647101Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:30:30.4716222Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:30:30.4717934Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:30:30.4719748Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:30:30.4734620Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:30:30.4738405Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:30:30.7210885Z","@mt":"Now listening on: {address}","address":"https://localhost:52805","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:30:30.7216681Z","@mt":"Now listening on: {address}","address":"http://localhost:52810","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:30:30.7589483Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:30:30.7591154Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:30:30.7591463Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.Controller","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:30:35.5946105Z","@mt":"初始化模块热加载监控...","@tr":"21916718247ad14f4f2a949da3912d8c","@sp":"41320dc08e2f0273","SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNEVQICL8UV6:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVQICL8UV6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:30:35.5961080Z","@mt":"模块热加载监控初始化完成，监控 {DirectoryCount} 个目录","@tr":"21916718247ad14f4f2a949da3912d8c","@sp":"41320dc08e2f0273","DirectoryCount":0,"SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNEVQICL8UV6:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVQICL8UV6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:30:35.5970350Z","@mt":"初始化动态路由系统...","@tr":"21916718247ad14f4f2a949da3912d8c","@sp":"41320dc08e2f0273","SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNEVQICL8UV6:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVQICL8UV6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:30:35.5978991Z","@mt":"发现 {Count} 个模块程序集","@tr":"21916718247ad14f4f2a949da3912d8c","@sp":"41320dc08e2f0273","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVQICL8UV6:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVQICL8UV6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:30:35.5993743Z","@mt":"动态路由系统初始化完成，发现 {ModuleCount} 个模块","@tr":"21916718247ad14f4f2a949da3912d8c","@sp":"41320dc08e2f0273","ModuleCount":2,"SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNEVQICL8UV6:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVQICL8UV6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:30:35.6007735Z","@mt":"发现 {Count} 个模块程序集","@tr":"21916718247ad14f4f2a949da3912d8c","@sp":"41320dc08e2f0273","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVQICL8UV6:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVQICL8UV6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:30:35.6032955Z","@mt":"首次请求触发端点刷新","@tr":"21916718247ad14f4f2a949da3912d8c","@sp":"41320dc08e2f0273","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEVQICL8UV6:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVQICL8UV6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:30:35.6042503Z","@mt":"发现 {Count} 个模块程序集","@tr":"21916718247ad14f4f2a949da3912d8c","@sp":"41320dc08e2f0273","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVQICL8UV6:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVQICL8UV6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:30:35.6050814Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"21916718247ad14f4f2a949da3912d8c","@sp":"41320dc08e2f0273","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVQICL8UV6:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVQICL8UV6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:30:35.6051856Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"21916718247ad14f4f2a949da3912d8c","@sp":"41320dc08e2f0273","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVQICL8UV6:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVQICL8UV6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:30:35.6052421Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"21916718247ad14f4f2a949da3912d8c","@sp":"41320dc08e2f0273","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVQICL8UV6:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVQICL8UV6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:30:35.6067028Z","@mt":"发现 {Count} 个模块程序集","@tr":"21916718247ad14f4f2a949da3912d8c","@sp":"41320dc08e2f0273","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVQICL8UV6:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVQICL8UV6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:30:35.6071602Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"21916718247ad14f4f2a949da3912d8c","@sp":"41320dc08e2f0273","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVQICL8UV6:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVQICL8UV6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:30:35.6072966Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"21916718247ad14f4f2a949da3912d8c","@sp":"41320dc08e2f0273","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVQICL8UV6:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVQICL8UV6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:30:35.6073703Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"21916718247ad14f4f2a949da3912d8c","@sp":"41320dc08e2f0273","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVQICL8UV6:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVQICL8UV6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:30:35.6075680Z","@mt":"请求触发的端点刷新完成，发现 {ModuleCount} 个模块，{EndpointCount} 个端点","@tr":"21916718247ad14f4f2a949da3912d8c","@sp":"41320dc08e2f0273","ModuleCount":2,"EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEVQICL8UV6:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVQICL8UV6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T10:30:35.6161292Z","@mt":"发现 {Count} 个模块程序集","@tr":"123ba938868fabcc2bfbc50056aa2339","@sp":"9d2c819b83dd0f2b","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVQICL8UV6:00000002","RequestPath":"/dapr/subscribe","ConnectionId":"0HNEVQICL8UV6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:25:25.2649109Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:25:25.3507134Z","@mt":"动态端点管理器初始化完成","SourceContext":"ApplicationBuilderExtensions","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:25:25.4115848Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:25:25.4173122Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:25:25.4174304Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:25:25.4175627Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:25:25.4189989Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:25:25.4193514Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:25:25.6537940Z","@mt":"Now listening on: {address}","address":"https://localhost:52979","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:25:25.6544001Z","@mt":"Now listening on: {address}","address":"http://localhost:52982","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:25:25.7178563Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:25:25.7181068Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:25:25.7181920Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.Controller","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:25:31.0946103Z","@mt":"初始化模块热加载监控...","@tr":"18e838b984540abf6cebdf596032ea1f","@sp":"18a5e37eba6a438f","SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNEVVN5QDIIA:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVN5QDIIA","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:25:31.0962258Z","@mt":"模块热加载监控初始化完成，监控 {DirectoryCount} 个目录","@tr":"18e838b984540abf6cebdf596032ea1f","@sp":"18a5e37eba6a438f","DirectoryCount":0,"SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNEVVN5QDIIA:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVN5QDIIA","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:25:31.0972205Z","@mt":"初始化动态路由系统...","@tr":"18e838b984540abf6cebdf596032ea1f","@sp":"18a5e37eba6a438f","SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNEVVN5QDIIA:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVN5QDIIA","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:25:31.0979920Z","@mt":"发现 {Count} 个模块程序集","@tr":"18e838b984540abf6cebdf596032ea1f","@sp":"18a5e37eba6a438f","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVVN5QDIIA:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVN5QDIIA","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:25:31.0990426Z","@mt":"动态路由系统初始化完成，发现 {ModuleCount} 个模块","@tr":"18e838b984540abf6cebdf596032ea1f","@sp":"18a5e37eba6a438f","ModuleCount":2,"SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNEVVN5QDIIA:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVN5QDIIA","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:25:31.1001538Z","@mt":"发现 {Count} 个模块程序集","@tr":"18e838b984540abf6cebdf596032ea1f","@sp":"18a5e37eba6a438f","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVVN5QDIIA:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVN5QDIIA","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:25:31.1021750Z","@mt":"首次请求触发端点刷新","@tr":"18e838b984540abf6cebdf596032ea1f","@sp":"18a5e37eba6a438f","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEVVN5QDIIA:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVN5QDIIA","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:25:31.1026772Z","@mt":"发现 {Count} 个模块程序集","@tr":"18e838b984540abf6cebdf596032ea1f","@sp":"18a5e37eba6a438f","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVVN5QDIIA:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVN5QDIIA","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:25:31.1029861Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"18e838b984540abf6cebdf596032ea1f","@sp":"18a5e37eba6a438f","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVVN5QDIIA:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVN5QDIIA","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:25:31.1030650Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"18e838b984540abf6cebdf596032ea1f","@sp":"18a5e37eba6a438f","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVVN5QDIIA:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVN5QDIIA","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:25:31.1031143Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"18e838b984540abf6cebdf596032ea1f","@sp":"18a5e37eba6a438f","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVVN5QDIIA:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVN5QDIIA","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:25:31.1039695Z","@mt":"发现 {Count} 个模块程序集","@tr":"18e838b984540abf6cebdf596032ea1f","@sp":"18a5e37eba6a438f","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVVN5QDIIA:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVN5QDIIA","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:25:31.1043418Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"18e838b984540abf6cebdf596032ea1f","@sp":"18a5e37eba6a438f","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVVN5QDIIA:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVN5QDIIA","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:25:31.1044236Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"18e838b984540abf6cebdf596032ea1f","@sp":"18a5e37eba6a438f","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVVN5QDIIA:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVN5QDIIA","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:25:31.1044710Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"18e838b984540abf6cebdf596032ea1f","@sp":"18a5e37eba6a438f","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVVN5QDIIA:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVN5QDIIA","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:25:31.1046161Z","@mt":"请求触发的端点刷新完成，发现 {ModuleCount} 个模块，{EndpointCount} 个端点","@tr":"18e838b984540abf6cebdf596032ea1f","@sp":"18a5e37eba6a438f","ModuleCount":2,"EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEVVN5QDIIA:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVN5QDIIA","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:25:31.1144822Z","@mt":"发现 {Count} 个模块程序集","@tr":"5067a507316ba1a6069fa83e1546fc90","@sp":"1c6bc053e12c9508","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVVN5QDIIA:00000002","RequestPath":"/dapr/subscribe","ConnectionId":"0HNEVVN5QDIIA","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:53.4320584Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:53.4982676Z","@mt":"动态端点管理器初始化完成","SourceContext":"ApplicationBuilderExtensions","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:53.5951128Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:53.6026113Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:53.6027656Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:53.6029173Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:53.6043393Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:53.6046648Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:53.8767959Z","@mt":"Now listening on: {address}","address":"https://localhost:63165","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:53.8774744Z","@mt":"Now listening on: {address}","address":"http://localhost:63166","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:53.9219203Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:53.9220777Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:53.9221132Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.Controller","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:54.2926059Z","@mt":"初始化模块热加载监控...","@tr":"712b23cb772108075f7f7bfcf2863a01","@sp":"20ceb1ecbee12568","SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNEVVPKO2P0O:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVPKO2P0O","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:54.2943352Z","@mt":"模块热加载监控初始化完成，监控 {DirectoryCount} 个目录","@tr":"712b23cb772108075f7f7bfcf2863a01","@sp":"20ceb1ecbee12568","DirectoryCount":0,"SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNEVVPKO2P0O:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVPKO2P0O","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:54.2953331Z","@mt":"初始化动态路由系统...","@tr":"712b23cb772108075f7f7bfcf2863a01","@sp":"20ceb1ecbee12568","SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNEVVPKO2P0O:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVPKO2P0O","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:54.2961300Z","@mt":"发现 {Count} 个模块程序集","@tr":"712b23cb772108075f7f7bfcf2863a01","@sp":"20ceb1ecbee12568","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVVPKO2P0O:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVPKO2P0O","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:54.2973515Z","@mt":"动态路由系统初始化完成，发现 {ModuleCount} 个模块","@tr":"712b23cb772108075f7f7bfcf2863a01","@sp":"20ceb1ecbee12568","ModuleCount":2,"SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNEVVPKO2P0O:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVPKO2P0O","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:54.2986919Z","@mt":"发现 {Count} 个模块程序集","@tr":"712b23cb772108075f7f7bfcf2863a01","@sp":"20ceb1ecbee12568","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVVPKO2P0O:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVPKO2P0O","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:54.3011523Z","@mt":"首次请求触发端点刷新","@tr":"712b23cb772108075f7f7bfcf2863a01","@sp":"20ceb1ecbee12568","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEVVPKO2P0O:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVPKO2P0O","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:54.3021115Z","@mt":"发现 {Count} 个模块程序集","@tr":"712b23cb772108075f7f7bfcf2863a01","@sp":"20ceb1ecbee12568","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVVPKO2P0O:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVPKO2P0O","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:54.3027983Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"712b23cb772108075f7f7bfcf2863a01","@sp":"20ceb1ecbee12568","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVVPKO2P0O:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVPKO2P0O","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:54.3028924Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"712b23cb772108075f7f7bfcf2863a01","@sp":"20ceb1ecbee12568","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVVPKO2P0O:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVPKO2P0O","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:54.3029453Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"712b23cb772108075f7f7bfcf2863a01","@sp":"20ceb1ecbee12568","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVVPKO2P0O:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVPKO2P0O","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:54.3037119Z","@mt":"发现 {Count} 个模块程序集","@tr":"712b23cb772108075f7f7bfcf2863a01","@sp":"20ceb1ecbee12568","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVVPKO2P0O:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVPKO2P0O","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:54.3040819Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"712b23cb772108075f7f7bfcf2863a01","@sp":"20ceb1ecbee12568","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVVPKO2P0O:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVPKO2P0O","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:54.3041702Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"712b23cb772108075f7f7bfcf2863a01","@sp":"20ceb1ecbee12568","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVVPKO2P0O:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVPKO2P0O","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:54.3042141Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"712b23cb772108075f7f7bfcf2863a01","@sp":"20ceb1ecbee12568","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVVPKO2P0O:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVPKO2P0O","MachineName":"LAPTOP-F6SGUTN5","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:54.3043688Z","@mt":"请求触发的端点刷新完成，发现 {ModuleCount} 个模块，{EndpointCount} 个端点","@tr":"712b23cb772108075f7f7bfcf2863a01","@sp":"20ceb1ecbee12568","ModuleCount":2,"EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEVVPKO2P0O:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVPKO2P0O","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:29:54.3155395Z","@mt":"发现 {Count} 个模块程序集","@tr":"842c3620e010d0c04ffbd36d586837bd","@sp":"00b138f004da59ea","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVVPKO2P0O:00000002","RequestPath":"/dapr/subscribe","ConnectionId":"0HNEVVPKO2P0O","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:38.7778019Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:38.8646865Z","@mt":"动态端点管理器初始化完成","SourceContext":"ApplicationBuilderExtensions","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:38.9307044Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:38.9370065Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:38.9371348Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:38.9372973Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:38.9388822Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:38.9393034Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:39.1947388Z","@mt":"Now listening on: {address}","address":"https://localhost:55348","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:39.1953794Z","@mt":"Now listening on: {address}","address":"http://localhost:55349","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:39.2385305Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:39.2386988Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:39.2387384Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.Controller","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.1655387Z","@mt":"初始化模块热加载监控...","@tr":"6f6dcf00cd287258157db9e4b78edb0a","@sp":"c70b1201cccc7086","SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNEVVS9V2OAE:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVS9V2OAE","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.1673368Z","@mt":"模块热加载监控初始化完成，监控 {DirectoryCount} 个目录","@tr":"6f6dcf00cd287258157db9e4b78edb0a","@sp":"c70b1201cccc7086","DirectoryCount":0,"SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNEVVS9V2OAE:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVS9V2OAE","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.1683914Z","@mt":"初始化动态路由系统...","@tr":"6f6dcf00cd287258157db9e4b78edb0a","@sp":"c70b1201cccc7086","SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNEVVS9V2OAE:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVS9V2OAE","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.1693053Z","@mt":"发现 {Count} 个模块程序集","@tr":"6f6dcf00cd287258157db9e4b78edb0a","@sp":"c70b1201cccc7086","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVVS9V2OAE:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVS9V2OAE","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.1707354Z","@mt":"动态路由系统初始化完成，发现 {ModuleCount} 个模块","@tr":"6f6dcf00cd287258157db9e4b78edb0a","@sp":"c70b1201cccc7086","ModuleCount":2,"SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNEVVS9V2OAE:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVS9V2OAE","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.1723132Z","@mt":"发现 {Count} 个模块程序集","@tr":"6f6dcf00cd287258157db9e4b78edb0a","@sp":"c70b1201cccc7086","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVVS9V2OAE:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVS9V2OAE","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.1751147Z","@mt":"首次请求触发端点刷新","@tr":"6f6dcf00cd287258157db9e4b78edb0a","@sp":"c70b1201cccc7086","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEVVS9V2OAE:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVS9V2OAE","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.1760295Z","@mt":"发现 {Count} 个模块程序集","@tr":"6f6dcf00cd287258157db9e4b78edb0a","@sp":"c70b1201cccc7086","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVVS9V2OAE:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVS9V2OAE","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.1765372Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"6f6dcf00cd287258157db9e4b78edb0a","@sp":"c70b1201cccc7086","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVVS9V2OAE:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVS9V2OAE","MachineName":"LAPTOP-F6SGUTN5","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.1766412Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"6f6dcf00cd287258157db9e4b78edb0a","@sp":"c70b1201cccc7086","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVVS9V2OAE:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVS9V2OAE","MachineName":"LAPTOP-F6SGUTN5","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.1766878Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"6f6dcf00cd287258157db9e4b78edb0a","@sp":"c70b1201cccc7086","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVVS9V2OAE:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVS9V2OAE","MachineName":"LAPTOP-F6SGUTN5","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.1775767Z","@mt":"发现 {Count} 个模块程序集","@tr":"6f6dcf00cd287258157db9e4b78edb0a","@sp":"c70b1201cccc7086","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVVS9V2OAE:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVS9V2OAE","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.1779798Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"6f6dcf00cd287258157db9e4b78edb0a","@sp":"c70b1201cccc7086","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVVS9V2OAE:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVS9V2OAE","MachineName":"LAPTOP-F6SGUTN5","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.1780738Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"6f6dcf00cd287258157db9e4b78edb0a","@sp":"c70b1201cccc7086","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVVS9V2OAE:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVS9V2OAE","MachineName":"LAPTOP-F6SGUTN5","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.1781274Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"6f6dcf00cd287258157db9e4b78edb0a","@sp":"c70b1201cccc7086","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEVVS9V2OAE:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVS9V2OAE","MachineName":"LAPTOP-F6SGUTN5","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.1785206Z","@mt":"请求触发的端点刷新完成，发现 {ModuleCount} 个模块，{EndpointCount} 个端点","@tr":"6f6dcf00cd287258157db9e4b78edb0a","@sp":"c70b1201cccc7086","ModuleCount":2,"EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEVVS9V2OAE:********","RequestPath":"/dapr/config","ConnectionId":"0HNEVVS9V2OAE","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:34:40.1903490Z","@mt":"发现 {Count} 个模块程序集","@tr":"3935b0223f46002110f8ef8c7e71a423","@sp":"2b4574591126f759","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEVVS9V2OAE:00000002","RequestPath":"/dapr/subscribe","ConnectionId":"0HNEVVS9V2OAE","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:45:57.1333843Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:45:57.2077394Z","@mt":"动态端点管理器初始化完成","SourceContext":"ApplicationBuilderExtensions","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:45:57.3036900Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:45:57.3118691Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:45:57.3120392Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:45:57.3122433Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:45:57.3139689Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:45:57.3144016Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:45:57.5243647Z","@mt":"Now listening on: {address}","address":"https://localhost:63662","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:45:57.5249366Z","@mt":"Now listening on: {address}","address":"http://localhost:63671","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:45:57.5618256Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:45:57.5620063Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:45:57.5620427Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.Controller","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:45:57.8089356Z","@mt":"初始化模块热加载监控...","@tr":"32abf07aae5b663936d20a1b919cacad","@sp":"0006442f9de6d907","SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNF002JTDO91:********","RequestPath":"/dapr/config","ConnectionId":"0HNF002JTDO91","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:45:57.8105038Z","@mt":"模块热加载监控初始化完成，监控 {DirectoryCount} 个目录","@tr":"32abf07aae5b663936d20a1b919cacad","@sp":"0006442f9de6d907","DirectoryCount":0,"SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNF002JTDO91:********","RequestPath":"/dapr/config","ConnectionId":"0HNF002JTDO91","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:45:57.8116036Z","@mt":"初始化动态路由系统...","@tr":"32abf07aae5b663936d20a1b919cacad","@sp":"0006442f9de6d907","SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNF002JTDO91:********","RequestPath":"/dapr/config","ConnectionId":"0HNF002JTDO91","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:45:57.8123547Z","@mt":"发现 {Count} 个模块程序集","@tr":"32abf07aae5b663936d20a1b919cacad","@sp":"0006442f9de6d907","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNF002JTDO91:********","RequestPath":"/dapr/config","ConnectionId":"0HNF002JTDO91","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:45:57.8135342Z","@mt":"动态路由系统初始化完成，发现 {ModuleCount} 个模块","@tr":"32abf07aae5b663936d20a1b919cacad","@sp":"0006442f9de6d907","ModuleCount":2,"SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNF002JTDO91:********","RequestPath":"/dapr/config","ConnectionId":"0HNF002JTDO91","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:45:57.8148337Z","@mt":"发现 {Count} 个模块程序集","@tr":"32abf07aae5b663936d20a1b919cacad","@sp":"0006442f9de6d907","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNF002JTDO91:********","RequestPath":"/dapr/config","ConnectionId":"0HNF002JTDO91","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:45:57.8170739Z","@mt":"首次请求触发端点刷新","@tr":"32abf07aae5b663936d20a1b919cacad","@sp":"0006442f9de6d907","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNF002JTDO91:********","RequestPath":"/dapr/config","ConnectionId":"0HNF002JTDO91","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:45:57.8177642Z","@mt":"发现 {Count} 个模块程序集","@tr":"32abf07aae5b663936d20a1b919cacad","@sp":"0006442f9de6d907","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNF002JTDO91:********","RequestPath":"/dapr/config","ConnectionId":"0HNF002JTDO91","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:45:57.8182206Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"32abf07aae5b663936d20a1b919cacad","@sp":"0006442f9de6d907","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF002JTDO91:********","RequestPath":"/dapr/config","ConnectionId":"0HNF002JTDO91","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:45:57.8183149Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"32abf07aae5b663936d20a1b919cacad","@sp":"0006442f9de6d907","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF002JTDO91:********","RequestPath":"/dapr/config","ConnectionId":"0HNF002JTDO91","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:45:57.8183636Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"32abf07aae5b663936d20a1b919cacad","@sp":"0006442f9de6d907","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF002JTDO91:********","RequestPath":"/dapr/config","ConnectionId":"0HNF002JTDO91","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:45:57.8191869Z","@mt":"发现 {Count} 个模块程序集","@tr":"32abf07aae5b663936d20a1b919cacad","@sp":"0006442f9de6d907","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNF002JTDO91:********","RequestPath":"/dapr/config","ConnectionId":"0HNF002JTDO91","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:45:57.8195366Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"32abf07aae5b663936d20a1b919cacad","@sp":"0006442f9de6d907","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF002JTDO91:********","RequestPath":"/dapr/config","ConnectionId":"0HNF002JTDO91","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:45:57.8196020Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"32abf07aae5b663936d20a1b919cacad","@sp":"0006442f9de6d907","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF002JTDO91:********","RequestPath":"/dapr/config","ConnectionId":"0HNF002JTDO91","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:45:57.8196432Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"32abf07aae5b663936d20a1b919cacad","@sp":"0006442f9de6d907","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF002JTDO91:********","RequestPath":"/dapr/config","ConnectionId":"0HNF002JTDO91","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:45:57.8197537Z","@mt":"请求触发的端点刷新完成，发现 {ModuleCount} 个模块，{EndpointCount} 个端点","@tr":"32abf07aae5b663936d20a1b919cacad","@sp":"0006442f9de6d907","ModuleCount":2,"EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNF002JTDO91:********","RequestPath":"/dapr/config","ConnectionId":"0HNF002JTDO91","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:45:57.8310267Z","@mt":"发现 {Count} 个模块程序集","@tr":"83ae9484cce50d97f903095b69f13eeb","@sp":"d63f42ac746061d4","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNF002JTDO91:00000002","RequestPath":"/dapr/subscribe","ConnectionId":"0HNF002JTDO91","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.7416968Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.8684745Z","@mt":"动态端点管理器初始化完成","SourceContext":"ApplicationBuilderExtensions","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.9880648Z","@mt":"发现 {Count} 个模块程序集","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.9942197Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.9943443Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.9944890Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.9960536Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:05.9964496Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:06.2238871Z","@mt":"Now listening on: {address}","address":"https://localhost:50891","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:06.2245004Z","@mt":"Now listening on: {address}","address":"http://localhost:50892","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:06.2680410Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:06.2681886Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-20T15:55:06.2682203Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.Controller","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
