﻿using Google.Api;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Microsoft.AspNetCore.Mvc.ApplicationParts;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.Infrastructure;
using Microsoft.AspNetCore.Mvc.ActionConstraints;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Reflection;
using Scalar.AspNetCore;
using SSIC.Infrastructure.Authentication;
using SSIC.Infrastructure.Extensions;

using SSIC.Infrastructure.ConfigurableOptions.Realization;
using SSIC.Infrastructure.Dapr;
using SSIC.Infrastructure.DependencyInjection.Extensions;
using SSIC.Infrastructure.Logging;
using SSIC.Infrastructure.OptionsEntity;
using SSIC.Infrastructure.Orm;
using SSIC.Infrastructure.Startups.Common;
using SSIC.Infrastructure.Startups.Endpoints;
using SSIC.Infrastructure.Startups.HotReload;
using SSIC.Infrastructure.Startups.OpenAPI;
using StackExchange.Profiling;
using StackExchange.Profiling.Storage;
using System.IO;
namespace SSIC.Infrastructure.Startups
{
    /// <summary>
    /// 配置项目启动项
    /// </summary>
    [Startup(1000)]
    public sealed class HostBuilder : IStartups
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<HostBuilder> _logger;
        public HostBuilder(IConfiguration configuration, ILogger<HostBuilder> logger)
        {
            _configuration = configuration;
            _logger = logger;
        }
     

        public void ConfigureServices(IServiceCollection services)
        {
            // 获取当前环境
            var env = services.BuildServiceProvider().GetRequiredService<IWebHostEnvironment>();

            // 配置 Dapr 客户端
            services.AddDaprClientProvider();

            // 添加SSIC基础设施服务（包含模块管理和热加载服务）
            services.AddSSICInfrastructure();

            //services.AddConfigurableOptions<CorsOptions>();

            // 添加控制器并配置全局路由约定
            services.AddControllers(options =>
            {
                options.Conventions.Add(new RoutePrefixConvention("api", env.ApplicationName));
            })
            .ConfigureApiBehaviorOptions(options =>
            {
                // 配置模型绑定行为，允许从JSON请求体自动绑定复杂对象
                options.SuppressConsumesConstraintForFormFileParameters = true;
                options.SuppressInferBindingSourcesForParameters = false;
                options.SuppressModelStateInvalidFilter = false;
            })
            .AddJsonOptions(options =>
            {
                // 配置JSON序列化选项
                options.JsonSerializerOptions.PropertyNameCaseInsensitive = true;
                options.JsonSerializerOptions.PropertyNamingPolicy = null; // 保持原始属性名
            });

            //扫描项目中继承作用域接口的类，并注册
            services.AddDependencyInjection();

            // 注册统一的OpenAPI转换器和服务（已在AddSSICInfrastructure中注册，这里保留兼容性）
            // 这些服务已在AddSSICOpenApiConfiguration中注册
            // 添加健康检查服务
            //services.AddHealthChecks();

            // 配置Swagger
            //services.AddSwaggerGen(c =>
            //{
            //    c.SwaggerDoc("v1", new OpenApiInfo { Title = "SSIC.WebApi", Version = "v1" });

            //    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
            //    {
            //        Name = "Authorization",
            //        Type = SecuritySchemeType.ApiKey,
            //        Scheme = "Bearer",
            //        BearerFormat = "JWT",
            //        In = ParameterLocation.Header,
            //        Description = "在下方填入 Bearer 空格 + token，例如: \"Bearer {token}\"",
            //    });
            //    c.AddSecurityRequirement(new OpenApiSecurityRequirement
            //    {
            //        {
            //            new OpenApiSecurityScheme
            //            {
            //                Reference = new OpenApiReference
            //                {
            //                    Type = ReferenceType.SecurityScheme,
            //                    Id = "Bearer"
            //                }
            //            },
            //            new string[] {}
            //        }
            //    });

            //    // 包含XML注释
            //    var xmlFile = AppContext.BaseDirectory;
            //    var xmlDocumentationFiles = Directory.GetFiles(xmlFile, "*.xml");
            //    foreach (var xmlFilePath in xmlDocumentationFiles)
            //    {
            //        c.IncludeXmlComments(xmlFilePath);
            //    }
            //});
            // 添加统一的OpenApi配置
            services.AddSSICOpenApi(env);
        

            // 添加MiniProfiler服务
            services.AddMiniProfiler(options =>
            {
                options.RouteBasePath = "/profiler"; // 设置MiniProfiler的路径
                (options.Storage as MemoryCacheStorage).CacheDuration = TimeSpan.FromMinutes(10);
            }); // 如果你使用的是EF Core
            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();

            services.AddSingleton<IAuthorizationHandler, PermissionHandler>();
            services.AddSingleton<IAuthorizationPolicyProvider, PermissionPolicyProvider>();
            services.AddTokenAuthentication(_configuration);
            services.AddHttpContextAccessor();

            var corsOption = services.GetOptions<CorsOptions>();
            services.AddCors(c =>
            {
                c.AddPolicy(corsOption.PolicyName, policy =>
                {
                    policy.AllowAnyHeader()
                          .AllowAnyMethod();
                         
                    if (corsOption.IsUse == true)
                    {
                        policy.WithOrigins(corsOption.WithOrigins).AllowCredentials();
                    }
                    else { 
                        policy.AllowAnyOrigin();
                    }
                });
            });
            
            // 注册新的FixedEndpointDataSource
            services.AddSingleton<FixedEndpointDataSource>();
            
            // 仍然注册DynamicEndpointDataSource以保持兼容性
            services.AddSingleton<DynamicEndpointDataSource>(sp => 
            {
                return DynamicEndpointManager.Instance.GetDynamicEndpoints() as DynamicEndpointDataSource;
            });
        }

        public void Configure(WebApplication app)
        {
            // 配置顺序很重要
            app.UseDeveloperExceptionPage();

            // 使用Swagger中间件
            if (app.Environment.IsDevelopment())
            {
                app.UseSwagger();
                app.UseSwaggerUI(c =>
                {
                    c.SwaggerEndpoint("/swagger/v1/swagger.json", "SSIC.WebApi v1");
                    c.RoutePrefix = "swagger"; // 设置Swagger UI的路由前缀
                });
            }

            app.UseAuthentication();
            app.UseContextMiddleware(); // 引入自定义的 HttpContextMiddleware 中间件

            // 使用SSIC基础设施中间件（包含动态路由、热加载等）
            app.UseSSICInfrastructure();

            app.UseHttpsRedirection(); // 取消 https 验证
            app.UseRouting();
            var corsOption = app.Services.GetOptions<CorsOptions>();

            // 确保使用正确的 CORS 策略名称
            app.UseCors(corsOption.PolicyName);

            app.UseAuthorization();
            // 使用MiniProfiler中间件
            app.UseMiniProfiler();

            // 映射控制器路由
            app.MapControllers();

            // 添加调试端点来检查路由
            app.MapGet("/debug/routes", async context =>
            {
                var actionProvider = context.RequestServices.GetService<IActionDescriptorCollectionProvider>();
                var routes = actionProvider?.ActionDescriptors.Items
                    .Where(a => a is ControllerActionDescriptor)
                    .Cast<ControllerActionDescriptor>()
                    .Select(a => new
                    {
                        Controller = a.ControllerName,
                        Action = a.ActionName,
                        Route = a.AttributeRouteInfo?.Template ?? "No Route",
                        Assembly = a.ControllerTypeInfo.Assembly.GetName().Name,
                        FullName = a.ControllerTypeInfo.FullName
                    })
                    .OrderBy(r => r.Assembly)
                    .ThenBy(r => r.Controller)
                    .ToList();

                context.Response.ContentType = "application/json";
                await context.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(routes, new System.Text.Json.JsonSerializerOptions { WriteIndented = true }));
            });

            // 在开发环境中启用Scalar
            if (app.Environment.IsDevelopment())
            {
                app.MapScalarApiReference(opt =>
                {
                    opt.Title = "SSIC 模块化API文档";
                    opt.Theme = ScalarTheme.Kepler;
                    opt.Servers = [];
                    opt.DefaultHttpClient = new(ScalarTarget.Http, ScalarClient.Http11);
                    // 使用新的OpenAPI端点，而不是Swagger
                    opt.OpenApiRoutePattern = "/openapi/v1.json";
                });

                app.MapOpenApi();//映射OpenApi文档路径
            }
            
            // 手动触发一次动态端点刷新，确保正常启动时也能正确加载控制器路由
            try
            {
                var endpointManager = SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointManager.Instance;
                if (endpointManager != null)
                {
                    // 获取已加载的插件程序集
                    var loadedAssemblies = AppDomain.CurrentDomain.GetAssemblies()
                        .Where(a => a.GetName().Name?.StartsWith("SSIC.Modules.") == true)
                        .ToList();

                    // 刷新路由端点
                    endpointManager.RefreshEndpoints(app.Services, loadedAssemblies);
                    _logger.LogInformation("应用启动时已手动刷新路由端点，共 {Count} 个模块", loadedAssemblies.Count);
                    
                    // 手动触发固定端点数据源的刷新
                    var fixedDataSource = app.Services.GetService<FixedEndpointDataSource>();
                    if (fixedDataSource != null)
                    {
                        fixedDataSource.ForceRefresh();
                        _logger.LogInformation("已强制刷新FixedEndpointDataSource");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "应用启动时手动刷新路由端点失败");
            }
            




        }



    }

    /// <summary>
    /// 路由前缀约定类 - 支持模块化路由
    /// </summary>
    public class RoutePrefixConvention : IApplicationModelConvention
    {
        private readonly string _prefix;

        public RoutePrefixConvention(string prefix, string projectName = "")
        {
            _prefix = prefix; // 只使用 "api" 前缀
        }

        public void Apply(ApplicationModel application)
        {
            Console.WriteLine($"RoutePrefixConvention.Apply 被调用，处理 {application.Controllers.Count} 个控制器");

            foreach (var controller in application.Controllers)
            {
                var controllerName = controller.ControllerName;

                // 获取控制器所在的程序集名称
                var assemblyName = controller.ControllerType.Assembly.GetName().Name;
                Console.WriteLine($"处理控制器: {controller.ControllerType.FullName}, 程序集: {assemblyName}");

                // 确定模块名称
                string moduleName = "";
                if (!string.IsNullOrEmpty(assemblyName) && assemblyName.StartsWith("SSIC.Modules."))
                {
                    // 处理新的项目结构：SSIC.Modules.Auth.Controller -> auth
                    if (assemblyName.EndsWith(".Controller"))
                    {
                        moduleName = assemblyName.Replace("SSIC.Modules.", "").Replace(".Controller", "").ToLowerInvariant();
                    }
                    else
                    {
                        // 处理旧的项目结构：SSIC.Modules.Auth -> auth
                        var tempName = assemblyName.Substring("SSIC.Modules.".Length);
                        // 如果包含其他后缀（如.Services），去掉它们
                        var dotIndex = tempName.IndexOf('.');
                        if (dotIndex > 0)
                        {
                            tempName = tempName.Substring(0, dotIndex);
                        }
                        moduleName = tempName.ToLowerInvariant();
                    }
                }
                else
                {
                    // 如果不是模块程序集，使用默认路由
                    moduleName = "default";
                }

                // 确保控制器具有 ApiController 行为
                if (!controller.Attributes.Any(a => a.GetType().Name == "ApiControllerAttribute"))
                {
                    // 添加 API 控制器行为
                    controller.ApiExplorer.IsVisible = true;
                    Console.WriteLine($"为控制器 {controllerName} 启用 ApiExplorer");
                }

                // 处理控制器级别的路由
                foreach (var selector in controller.Selectors)
                {
                    if (selector.AttributeRouteModel == null)
                    {
                        // 构建模块化路由：api/{模块名}/{控制器名}
                        var routeTemplate = $"{_prefix}/{moduleName}/{controllerName.ToLowerInvariant()}";

                        selector.AttributeRouteModel = new AttributeRouteModel
                        {
                            Template = routeTemplate
                        };

                        Console.WriteLine($"✓ 应用控制器路由约定: {controller.ControllerType.FullName} -> {routeTemplate}");
                    }
                    else
                    {
                        Console.WriteLine($"跳过控制器 {controllerName}，已有路由: {selector.AttributeRouteModel.Template}");
                    }
                }

                // 处理方法级别的路由
                foreach (var action in controller.Actions)
                {
                    // 配置参数的模型绑定源
                    ConfigureParameterBindingSources(action);

                    foreach (var selector in action.Selectors)
                    {
                        if (selector.AttributeRouteModel == null)
                        {
                            // 分析HTTP特性来确定路由和HTTP方法
                            var routeInfo = GetActionRouteInfo(action);

                            selector.AttributeRouteModel = new AttributeRouteModel
                            {
                                Template = routeInfo.Template
                            };

                            // 设置HTTP方法约束
                            if (routeInfo.HttpMethods.Any())
                            {
                                selector.ActionConstraints.Add(new HttpMethodActionConstraint(routeInfo.HttpMethods));
                            }

                            Console.WriteLine($"✓ 应用方法路由约定: {action.ActionName} -> {routeInfo.Template} [{string.Join(",", routeInfo.HttpMethods)}]");
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 根据HTTP特性确定方法的路由信息
        /// </summary>
        private ModuleUtilities.ActionRouteInfo GetActionRouteInfo(ActionModel action)
        {
            var routeInfo = new ModuleUtilities.ActionRouteInfo();

            // 查找HTTP方法特性
            var httpMethodAttributes = action.Attributes
                .Where(attr => attr.GetType().Name.StartsWith("Http") && attr.GetType().Name.EndsWith("Attribute"))
                .ToList();

            if (httpMethodAttributes.Any())
            {
                // 有HTTP特性，提取HTTP方法和路由模板
                foreach (var attr in httpMethodAttributes)
                {
                    // 提取HTTP方法
                    var httpMethod = ModuleUtilities.ExtractHttpMethod(attr.GetType().Name);
                    if (!string.IsNullOrEmpty(httpMethod))
                    {
                        routeInfo.HttpMethods.Add(httpMethod);
                    }

                    // 尝试获取路由模板（如 [HttpGet("list")] 中的 "list"）
                    var templateProperty = attr.GetType().GetProperty("Template");
                    if (templateProperty != null)
                    {
                        var template = templateProperty.GetValue(attr) as string;
                        if (!string.IsNullOrEmpty(template))
                        {
                            // 如果有模板（如 "list"），直接使用
                            routeInfo.Template = template;
                            return routeInfo;
                        }
                    }
                }

                // 有HTTP特性但没有模板，使用方法名
                routeInfo.Template = action.ActionName;
            }
            else
            {
                // 没有HTTP特性，使用方法名，支持POST和GET
                routeInfo.Template = action.ActionName;
                routeInfo.HttpMethods.Add("GET");
                routeInfo.HttpMethods.Add("POST");
            }

            return routeInfo;
        }

        /// <summary>
        /// 配置参数的模型绑定源
        /// </summary>
        private void ConfigureParameterBindingSources(ActionModel action)
        {
            // 获取HTTP方法
            var httpMethods = action.Attributes
                .Where(attr => attr.GetType().Name.StartsWith("Http") && attr.GetType().Name.EndsWith("Attribute"))
                .Select(attr => ModuleUtilities.ExtractHttpMethod(attr.GetType().Name))
                .Where(method => !string.IsNullOrEmpty(method))
                .ToList();

            // 如果没有HTTP方法特性，默认支持GET和POST
            if (!httpMethods.Any())
            {
                httpMethods.Add("GET");
                httpMethods.Add("POST");
            }

            foreach (var parameter in action.Parameters)
            {
                // 检查参数是否已经有绑定源特性
                var hasBindingAttribute = parameter.Attributes.Any(attr =>
                    attr.GetType().Name.Contains("FromBody") ||
                    attr.GetType().Name.Contains("FromQuery") ||
                    attr.GetType().Name.Contains("FromRoute") ||
                    attr.GetType().Name.Contains("FromForm") ||
                    attr.GetType().Name.Contains("FromHeader"));

                // 如果没有绑定特性，自动配置绑定源
                if (!hasBindingAttribute)
                {
                    parameter.BindingInfo = parameter.BindingInfo ?? new Microsoft.AspNetCore.Mvc.ModelBinding.BindingInfo();

                    // 对于PUT、POST、PATCH方法
                    if (httpMethods.Any(method => method == "PUT" || method == "POST" || method == "PATCH"))
                    {
                        if (IsComplexType(parameter.ParameterType))
                        {
                            // 复杂类型从请求体绑定
                            parameter.BindingInfo.BindingSource = Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource.Body;
                            Console.WriteLine($"✓ 为参数 {parameter.Name} ({parameter.ParameterType.Name}) 配置从请求体绑定");
                        }
                        else
                        {
                            // 简单类型从查询字符串绑定
                            parameter.BindingInfo.BindingSource = Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource.Query;
                            Console.WriteLine($"✓ 为参数 {parameter.Name} ({parameter.ParameterType.Name}) 配置从查询字符串绑定");
                        }
                    }
                    else
                    {
                        // GET方法的参数从查询字符串绑定
                        parameter.BindingInfo.BindingSource = Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource.Query;
                        Console.WriteLine($"✓ 为参数 {parameter.Name} ({parameter.ParameterType.Name}) 配置从查询字符串绑定");
                    }
                }
            }
        }

        /// <summary>
        /// 判断是否为复杂类型（需要从JSON绑定的类型）
        /// </summary>
        private bool IsComplexType(Type type)
        {
            // 基本类型和字符串不是复杂类型
            if (type.IsPrimitive || type == typeof(string) || type == typeof(DateTime) ||
                type == typeof(decimal) || type == typeof(Guid) || type.IsEnum)
            {
                return false;
            }

            // 可空类型检查
            if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>))
            {
                return IsComplexType(type.GetGenericArguments()[0]);
            }

            // 其他类型视为复杂类型
            return true;
        }


    }
}
