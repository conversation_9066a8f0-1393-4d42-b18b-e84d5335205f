﻿namespace SSIC.Infrastructure.Startups.HotReload
{
    /// <summary>
    /// 简化的热加载配置选项
    /// </summary>
    public class HotReloadOptions
    {
        /// <summary>
        /// 是否启用热加载功能
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// 防抖延迟（毫秒）- 设置为0禁用防抖，立即触发
        /// </summary>
        public int DebounceMs { get; set; } = 0;

        /// <summary>
        /// 是否在卸载后强制垃圾回收
        /// </summary>
        public bool ForceGarbageCollection { get; set; } = true;

        /// <summary>
        /// 是否记录详细日志
        /// </summary>
        public bool VerboseLogging { get; set; } = true;

        /// <summary>
        /// 是否启用模块重复注册检查，避免同一模块被多次加载
        /// </summary>
        public bool PreventDuplicateRegistration { get; set; } = true;

        /// <summary>
        /// 重新加载模块时是否强制先卸载旧版本
        /// </summary>
        public bool ForceUnloadBeforeReload { get; set; } = true;

        /// <summary>
        /// 模块卸载超时时间（毫秒）
        /// </summary>
        public int UnloadTimeoutMs { get; set; } = 5000;

        /// <summary>
        /// 是否启用模块版本跟踪，防止加载相同版本
        /// </summary>
        public bool EnableVersionTracking { get; set; } = true;

        /// <summary>
        /// 同一模块的最大重试加载次数
        /// </summary>
        public int MaxRetryCount { get; set; } = 3;

        /// <summary>
        /// 模块加载失败后的重试间隔（毫秒）
        /// </summary>
        public int RetryIntervalMs { get; set; } = 1000;
    }
}