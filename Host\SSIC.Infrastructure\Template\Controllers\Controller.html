﻿/*
 *Author：jxx
 *Contact：<EMAIL>
 *Date：2018-07-01
 * 此代码由框架生成，请勿随意更改
 */
using System;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using {Namespace}.IServices;
using {StartName}.Core.Controllers.Basic;
using Microsoft.AspNetCore.Mvc;
using VOL.Entity.AttributeManager;

namespace {Namespace}.Controllers
{
    /// <summary>
    /// {TableName}控制器
    /// 提供{TableName}实体的Web API接口（传统项目结构）
    /// </summary>
    [ApiExplorerSettings(IgnoreApi = true)]
    [PermissionTable(Name = "{TableName}")]
    public partial class {TableName}Controller : BaseController<I{TableName}Service>
    {
        /// <summary>
        /// 初始化{TableName}控制器
        /// </summary>
        /// <param name="service">{TableName}服务</param>
        public {TableName}Controller(I{TableName}Service service)
        : base({BaseOptions}, service)
        {
        }
    }
}
