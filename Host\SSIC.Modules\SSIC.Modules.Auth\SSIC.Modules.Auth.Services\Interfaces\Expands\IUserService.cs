/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *Service提供业务逻辑操作，如果要增加业务逻辑请在当前目录下Expands文件夹IUserService编写接口
 */
using SSIC.Infrastructure.BaseProvider;
using SSIC.Infrastructure.DependencyInjection;
using SSIC.Infrastructure.DependencyInjection.Interface;
using SSIC.Entity.Auth;

namespace SSIC.Modules.Auth.Services.Interfaces
{
    /// <summary>
    /// User服务接口扩展
    /// 在此处添加自定义的业务方法接口定义
    /// </summary>
    public partial interface IUserService
    {
        // 在此处添加自定义方法接口
        // 例如：
        // /// <summary>
        // /// 根据名称查询User
        // /// </summary>
        // /// <param name="name">名称</param>
        // /// <returns></returns>
        // Task<List<User>> GetByNameAsync(string name);
    }
}
