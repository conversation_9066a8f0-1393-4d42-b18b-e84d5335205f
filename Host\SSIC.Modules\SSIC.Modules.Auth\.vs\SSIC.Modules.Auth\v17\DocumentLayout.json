{"Version": 1, "WorkspaceRootPath": "F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{557B01FD-7B5E-0A40-230A-236507DB8CCE}|SSIC.Modules.Auth.Services\\SSIC.Modules.Auth.Services.csproj|f:\\ssic\\host\\ssic.modules\\ssic.modules.auth\\ssic.modules.auth.services\\settings\\dbinfosettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{557B01FD-7B5E-0A40-230A-236507DB8CCE}|SSIC.Modules.Auth.Services\\SSIC.Modules.Auth.Services.csproj|solutionrelative:ssic.modules.auth.services\\settings\\dbinfosettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{557B01FD-7B5E-0A40-230A-236507DB8CCE}|SSIC.Modules.Auth.Services\\SSIC.Modules.Auth.Services.csproj|f:\\ssic\\host\\ssic.modules\\ssic.modules.auth\\ssic.modules.auth.services\\settings\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{557B01FD-7B5E-0A40-230A-236507DB8CCE}|SSIC.Modules.Auth.Services\\SSIC.Modules.Auth.Services.csproj|solutionrelative:ssic.modules.auth.services\\settings\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{557B01FD-7B5E-0A40-230A-236507DB8CCE}|SSIC.Modules.Auth.Services\\SSIC.Modules.Auth.Services.csproj|f:\\ssic\\host\\ssic.modules\\ssic.modules.auth\\ssic.modules.auth.services\\settings\\modulesettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{557B01FD-7B5E-0A40-230A-236507DB8CCE}|SSIC.Modules.Auth.Services\\SSIC.Modules.Auth.Services.csproj|solutionrelative:ssic.modules.auth.services\\settings\\modulesettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{557B01FD-7B5E-0A40-230A-236507DB8CCE}|SSIC.Modules.Auth.Services\\SSIC.Modules.Auth.Services.csproj|f:\\ssic\\host\\ssic.modules\\ssic.modules.auth\\ssic.modules.auth.services\\settings\\corssettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{557B01FD-7B5E-0A40-230A-236507DB8CCE}|SSIC.Modules.Auth.Services\\SSIC.Modules.Auth.Services.csproj|solutionrelative:ssic.modules.auth.services\\settings\\corssettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{E59EECA9-D062-5580-A572-7171514CA3CC}|SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Controller.csproj|f:\\ssic\\host\\ssic.modules\\ssic.modules.auth\\ssic.modules.auth.controller\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E59EECA9-D062-5580-A572-7171514CA3CC}|SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Controller.csproj|solutionrelative:ssic.modules.auth.controller\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E59EECA9-D062-5580-A572-7171514CA3CC}|SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Controller.csproj|f:\\ssic\\host\\ssic.modules\\ssic.modules.auth\\ssic.modules.auth.controller\\ssic.modules.auth.controller.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{E59EECA9-D062-5580-A572-7171514CA3CC}|SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Controller.csproj|solutionrelative:ssic.modules.auth.controller\\ssic.modules.auth.controller.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\SSIC\\Host\\SSIC.Infrastructure\\ConfigurableOptions\\Realization\\ConfigurableOptions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 13, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:131:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:132:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:130:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:133:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:134:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:135:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:136:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:129:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:128:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 3, "Title": "Corssettings.json", "DocumentMoniker": "F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.Services\\Settings\\Corssettings.json", "RelativeDocumentMoniker": "SSIC.Modules.Auth.Services\\Settings\\Corssettings.json", "ToolTip": "F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.Services\\Settings\\Corssettings.json", "RelativeToolTip": "SSIC.Modules.Auth.Services\\Settings\\Corssettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-08-19T03:27:49.589Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "DbInfosettings.json", "DocumentMoniker": "F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.Services\\Settings\\DbInfosettings.json", "RelativeDocumentMoniker": "SSIC.Modules.Auth.Services\\Settings\\DbInfosettings.json", "ToolTip": "F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.Services\\Settings\\DbInfosettings.json", "RelativeToolTip": "SSIC.Modules.Auth.Services\\Settings\\DbInfosettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-08-19T03:27:45.749Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "appsettings.json", "DocumentMoniker": "F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.Services\\Settings\\appsettings.json", "RelativeDocumentMoniker": "SSIC.Modules.Auth.Services\\Settings\\appsettings.json", "ToolTip": "F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.Services\\Settings\\appsettings.json", "RelativeToolTip": "SSIC.Modules.Auth.Services\\Settings\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-08-19T03:27:44.285Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "modulesettings.json", "DocumentMoniker": "F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.Services\\Settings\\modulesettings.json", "RelativeDocumentMoniker": "SSIC.Modules.Auth.Services\\Settings\\modulesettings.json", "ToolTip": "F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.Services\\Settings\\modulesettings.json*", "RelativeToolTip": "SSIC.Modules.Auth.Services\\Settings\\modulesettings.json*", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-08-19T03:27:22.169Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "Program.cs", "DocumentMoniker": "F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.Controller\\Program.cs", "RelativeDocumentMoniker": "SSIC.Modules.Auth.Controller\\Program.cs", "ToolTip": "F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.Controller\\Program.cs", "RelativeToolTip": "SSIC.Modules.Auth.Controller\\Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-19T03:24:18.867Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "SSIC.Modules.Auth.Controller", "DocumentMoniker": "F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Controller.csproj", "RelativeDocumentMoniker": "SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Controller.csproj", "ToolTip": "F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Controller.csproj", "RelativeToolTip": "SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Controller.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-08-19T03:24:06.071Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "ConfigurableOptions.cs", "DocumentMoniker": "F:\\SSIC\\Host\\SSIC.Infrastructure\\ConfigurableOptions\\Realization\\ConfigurableOptions.cs", "RelativeDocumentMoniker": "..\\..\\SSIC.Infrastructure\\ConfigurableOptions\\Realization\\ConfigurableOptions.cs", "ToolTip": "F:\\SSIC\\Host\\SSIC.Infrastructure\\ConfigurableOptions\\Realization\\ConfigurableOptions.cs", "RelativeToolTip": "..\\..\\SSIC.Infrastructure\\ConfigurableOptions\\Realization\\ConfigurableOptions.cs", "ViewState": "AgIAAHIAAAAAAAAAAAAswJMAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-19T03:20:19.876Z", "EditorCaption": ""}]}]}]}