[{"ContainingType": "SSIC.DevTools.Controllers.CodeGeneratorController", "Method": "GetEntities", "RelativePath": "api/CodeGenerator/entities", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SSIC.DevTools.Controllers.CodeGeneratorController", "Method": "GenerateModules", "RelativePath": "api/CodeGenerator/generate-modules", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SSIC.DevTools.Controllers.GenerateModulesRequest", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "SSIC.DevTools.Controllers.CodeGeneratorController", "Method": "GenerateTraditional", "RelativePath": "api/CodeGenerator/generate-traditional", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SSIC.DevTools.Controllers.CodeGeneratorController", "Method": "GetTemplates", "RelativePath": "api/CodeGenerator/templates", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SSIC.DevTools.Controllers.WeatherForecastController", "Method": "Get", "RelativePath": "WeatherForecast", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SSIC.DevTools.WeatherForecast, SSIC.DevTools, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "GetWeatherForecast"}, {"ContainingType": "SSIC.DevTools.Controllers.WeatherForecastController", "Method": "Hello", "RelativePath": "WeatherForecast/api/Hello", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SSIC.DevTools.Controllers.WeatherForecastController", "Method": "CreateFiles", "RelativePath": "WeatherForecast/CreateFiles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ModelName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "SSIC.DevTools.Controllers.WeatherForecastController", "Method": "Get123", "RelativePath": "WeatherForecast/Get123/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "SSIC.DevTools.WeatherForecast", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "Get123"}]