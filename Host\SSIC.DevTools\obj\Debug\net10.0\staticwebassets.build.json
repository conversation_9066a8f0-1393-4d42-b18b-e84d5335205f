{"Version": 1, "Hash": "nsoqZTFMoYd1x9299csS+oeWC0hpdBY3HMKLl/z007I=", "Source": "SSIC.DevTools", "BasePath": "/", "Mode": "Root", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "SSIC.DevTools\\wwwroot", "Source": "SSIC.DevTools", "ContentRoot": "F:\\SSIC\\Host\\SSIC.DevTools\\wwwroot\\", "BasePath": "/", "Pattern": "**"}], "Assets": [{"Identity": "F:\\SSIC\\Host\\SSIC.DevTools\\obj\\Debug\\net10.0\\compressed\\g0g5zl4bbt-{0}-sxaa5jpl0x-sxaa5jpl0x.gz", "SourceId": "SSIC.DevTools", "SourceType": "Discovered", "ContentRoot": "F:\\SSIC\\Host\\SSIC.DevTools\\obj\\Debug\\net10.0\\compressed\\", "BasePath": "/", "RelativePath": "codegen#[.{fingerprint=sxaa5jpl0x}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "F:\\SSIC\\Host\\SSIC.DevTools\\wwwroot\\codegen.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fsdl78ltv4", "Integrity": "D1DwPYnQwIFFLn1RODRGFWiNpW0IAdROr51pk6cpsHA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\SSIC\\Host\\SSIC.DevTools\\wwwroot\\codegen.html", "FileLength": 4018, "LastWriteTime": "2025-08-20T10:29:44+00:00"}, {"Identity": "F:\\SSIC\\Host\\SSIC.DevTools\\wwwroot\\codegen.html", "SourceId": "SSIC.DevTools", "SourceType": "Discovered", "ContentRoot": "F:\\SSIC\\Host\\SSIC.DevTools\\wwwroot\\", "BasePath": "/", "RelativePath": "codegen#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "sxaa5jpl0x", "Integrity": "IImG8VAR6H3oQttRVgh6xkVuBHewvnLOV/Up6rfLcPM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\codegen.html", "FileLength": 16853, "LastWriteTime": "2025-08-19T08:28:00+00:00"}], "Endpoints": [{"Route": "codegen.html", "AssetFile": "F:\\SSIC\\Host\\SSIC.DevTools\\obj\\Debug\\net10.0\\compressed\\g0g5zl4bbt-{0}-sxaa5jpl0x-sxaa5jpl0x.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000248818114"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4018"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"D1DwPYnQwIFFLn1RODRGFWiNpW0IAdROr51pk6cpsHA=\""}, {"Name": "ETag", "Value": "W/\"IImG8VAR6H3oQttRVgh6xkVuBHewvnLOV/Up6rfLcPM=\""}, {"Name": "Last-Modified", "Value": "Wed, 20 Aug 2025 10:29:44 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IImG8VAR6H3oQttRVgh6xkVuBHewvnLOV/Up6rfLcPM="}]}, {"Route": "codegen.html", "AssetFile": "F:\\SSIC\\Host\\SSIC.DevTools\\wwwroot\\codegen.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "16853"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"IImG8VAR6H3oQttRVgh6xkVuBHewvnLOV/Up6rfLcPM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 19 Aug 2025 08:28:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IImG8VAR6H3oQttRVgh6xkVuBHewvnLOV/Up6rfLcPM="}]}, {"Route": "codegen.html.gz", "AssetFile": "F:\\SSIC\\Host\\SSIC.DevTools\\obj\\Debug\\net10.0\\compressed\\g0g5zl4bbt-{0}-sxaa5jpl0x-sxaa5jpl0x.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4018"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"D1DwPYnQwIFFLn1RODRGFWiNpW0IAdROr51pk6cpsHA=\""}, {"Name": "Last-Modified", "Value": "Wed, 20 Aug 2025 10:29:44 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-D1DwPYnQwIFFLn1RODRGFWiNpW0IAdROr51pk6cpsHA="}]}, {"Route": "codegen.sxaa5jpl0x.html", "AssetFile": "F:\\SSIC\\Host\\SSIC.DevTools\\obj\\Debug\\net10.0\\compressed\\g0g5zl4bbt-{0}-sxaa5jpl0x-sxaa5jpl0x.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000248818114"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4018"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"D1DwPYnQwIFFLn1RODRGFWiNpW0IAdROr51pk6cpsHA=\""}, {"Name": "ETag", "Value": "W/\"IImG8VAR6H3oQttRVgh6xkVuBHewvnLOV/Up6rfLcPM=\""}, {"Name": "Last-Modified", "Value": "Wed, 20 Aug 2025 10:29:44 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sxaa5jpl0x"}, {"Name": "integrity", "Value": "sha256-IImG8VAR6H3oQttRVgh6xkVuBHewvnLOV/Up6rfLcPM="}, {"Name": "label", "Value": "codegen.html"}]}, {"Route": "codegen.sxaa5jpl0x.html", "AssetFile": "F:\\SSIC\\Host\\SSIC.DevTools\\wwwroot\\codegen.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "16853"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"IImG8VAR6H3oQttRVgh6xkVuBHewvnLOV/Up6rfLcPM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 19 Aug 2025 08:28:00 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sxaa5jpl0x"}, {"Name": "integrity", "Value": "sha256-IImG8VAR6H3oQttRVgh6xkVuBHewvnLOV/Up6rfLcPM="}, {"Name": "label", "Value": "codegen.html"}]}, {"Route": "codegen.sxaa5jpl0x.html.gz", "AssetFile": "F:\\SSIC\\Host\\SSIC.DevTools\\obj\\Debug\\net10.0\\compressed\\g0g5zl4bbt-{0}-sxaa5jpl0x-sxaa5jpl0x.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4018"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"D1DwPYnQwIFFLn1RODRGFWiNpW0IAdROr51pk6cpsHA=\""}, {"Name": "Last-Modified", "Value": "Wed, 20 Aug 2025 10:29:44 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sxaa5jpl0x"}, {"Name": "integrity", "Value": "sha256-D1DwPYnQwIFFLn1RODRGFWiNpW0IAdROr51pk6cpsHA="}, {"Name": "label", "Value": "codegen.html.gz"}]}]}