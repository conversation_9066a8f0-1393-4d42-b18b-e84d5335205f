﻿using Dapr;
using Dapr.Client;
using Google.Api;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Mvc.ApplicationParts;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SSIC.HostServer;
using SSIC.Infrastructure.Startups;
using SSIC.Infrastructure.Startups.HotReload;
using System.IO;
using System.Reflection;

try
{
    // Set console encoding to UTF-8 for proper Chinese character display
    Console.OutputEncoding = System.Text.Encoding.UTF8;

    var builder = WebApplication.CreateBuilder(args);
    var app = builder.AddHotReload(true).AddStartup();
    app.Run();
}
catch (Exception ex)
{
    Console.OutputEncoding = System.Text.Encoding.UTF8;
    Console.WriteLine($"Application startup error: {ex.Message}");
    Console.WriteLine($"Stack trace: {ex.StackTrace}");
    throw;
}
