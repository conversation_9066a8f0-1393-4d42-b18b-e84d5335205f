﻿/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果要增加方法请在当前目录下Expands文件夹{EntityName}Controller编写
 */
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using SSIC.Infrastructure.BaseController;
using SSIC.Modules.{BusinessName}.Services.Interfaces;
using System;

namespace SSIC.Modules.{BusinessName}.Controllers
{
    /// <summary>
    /// {EntityName}控制器{EntityDescription}
    /// 提供{EntityName}实体的Web API接口
    /// </summary>
    public partial class {EntityName}Controller : BaseController
    {
        /// <summary>
        /// 初始化{EntityName}控制器
        /// </summary>
        /// <param name="serviceProvider">服务提供者</param>
        /// <param name="logger">日志记录器</param>
        public {EntityName}Controller(IServiceProvider serviceProvider, ILogger<{EntityName}Controller> logger)
            : base(serviceProvider, logger)
        {
        }

        /// <summary>
        /// 获取{EntityName}服务
        /// </summary>
        protected I{EntityName}Service {EntityName}Service => GetService<I{EntityName}Service>();
    }
}