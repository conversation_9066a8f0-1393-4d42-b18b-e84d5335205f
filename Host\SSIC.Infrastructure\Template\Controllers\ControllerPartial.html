﻿using {StartName}.Core.Extensions;
using {StartName}.Core.Filter;
using {StartName}.Core.Utility;
using {StartName}.Entity.DomainModels;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace {Namespace}.Controllers
{
    /// <summary>
    /// {TableName}控制器扩展部分
    /// 在此处添加自定义的业务方法
    /// </summary>
    public partial class {TableName}Controller
    {
        // 在此处添加自定义的控制器方法
        // 例如：
        // /// <summary>
        // /// 自定义查询方法
        // /// </summary>
        // /// <returns></returns>
        // [HttpGet("custom")]
        // public async Task<IActionResult> CustomQueryAsync()
        // {
        //     // 实现自定义查询逻辑
        //     return Ok();
        // }
    }
}
