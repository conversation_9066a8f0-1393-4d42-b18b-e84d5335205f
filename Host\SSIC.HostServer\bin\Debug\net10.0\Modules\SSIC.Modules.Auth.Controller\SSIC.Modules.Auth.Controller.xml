<?xml version="1.0"?>
<doc>
    <assembly>
        <name>SSIC.Modules.Auth.Controller</name>
    </assembly>
    <members>
        <member name="T:SSIC.Modules.Auth.Controllers.PermissionController">
            <summary>
            Permission控制器扩展类
            在此处添加自定义的API接口方法
            </summary>
            <summary>
            Permission控制器（权限管理）
            提供Permission实体的Web API接口
            </summary>
        </member>
        <member name="M:SSIC.Modules.Auth.Controllers.PermissionController.BatchDeletePermissions(System.Int32[])">
            <summary>
            批量删除权限（带HTTP特性）
            </summary>
            <param name="permissionIds">权限ID列表</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:SSIC.Modules.Auth.Controllers.PermissionController.GetPermissionTree">
            <summary>
            获取权限树结构（不带HTTP特性）
            </summary>
            <returns>权限树</returns>
        </member>
        <member name="M:SSIC.Modules.Auth.Controllers.PermissionController.#ctor(System.IServiceProvider,Microsoft.Extensions.Logging.ILogger{SSIC.Modules.Auth.Controllers.PermissionController})">
            <summary>
            初始化Permission控制器
            </summary>
            <param name="serviceProvider">服务提供者</param>
            <param name="logger">日志记录器</param>
        </member>
        <member name="P:SSIC.Modules.Auth.Controllers.PermissionController.PermissionService">
            <summary>
            获取Permission服务
            </summary>
        </member>
        <member name="T:SSIC.Modules.Auth.Controllers.RoleController">
            <summary>
            Role控制器扩展类
            在此处添加自定义的API接口方法
            </summary>
            <summary>
            Role控制器（角色管理）
            提供Role实体的Web API接口
            </summary>
        </member>
        <member name="M:SSIC.Modules.Auth.Controllers.RoleController.AssignPermissions(System.Int32,System.Int32[])">
            <summary>
            分配角色权限（带HTTP特性）
            </summary>
            <param name="roleId">角色ID</param>
            <param name="permissionIds">权限ID列表</param>
            <returns>分配结果</returns>
        </member>
        <member name="M:SSIC.Modules.Auth.Controllers.RoleController.QueryRoleDetails">
            <summary>
            查询角色详细信息（不带HTTP特性）
            </summary>
            <returns>角色详细信息</returns>
        </member>
        <member name="M:SSIC.Modules.Auth.Controllers.RoleController.#ctor(System.IServiceProvider,Microsoft.Extensions.Logging.ILogger{SSIC.Modules.Auth.Controllers.RoleController})">
            <summary>
            初始化Role控制器
            </summary>
            <param name="serviceProvider">服务提供者</param>
            <param name="logger">日志记录器</param>
        </member>
        <member name="P:SSIC.Modules.Auth.Controllers.RoleController.RoleService">
            <summary>
            获取Role服务
            </summary>
        </member>
        <member name="T:SSIC.Modules.Auth.Controllers.UserController">
            <summary>
            User控制器扩展类
            在此处添加自定义的API接口方法
            </summary>
            <summary>
            User控制器（用户管理）
            提供User实体的Web API接口
            </summary>
        </member>
        <member name="M:SSIC.Modules.Auth.Controllers.UserController.CreateUser(System.String)">
             <summary>
             创建新用户
             </summary>
             <remarks>
             创建一个新的用户账户。
            
             示例请求：
             ```
             POST /api/auth/user/create
             Content-Type: application/json
            
             "newuser123"
             ```
             </remarks>
             <param name="userName">用户名，必须唯一且不能为空</param>
             <returns>返回创建的用户信息，包括用户ID、用户名和创建时间</returns>
             <response code="200">用户创建成功</response>
             <response code="400">用户名无效或为空</response>
             <response code="409">用户名已存在</response>
        </member>
        <member name="M:SSIC.Modules.Auth.Controllers.UserController.GetUserStatistics">
             <summary>
             获取用户统计信息
             </summary>
             <remarks>
             获取系统中用户的统计数据，包括总用户数、活跃用户数和今日新增用户数。
            
             此接口不需要特殊权限，返回系统级别的统计信息。
            
             示例响应：
             ```json
             {
               "totalUsers": 156,
               "activeUsers": 89,
               "newUsersToday": 12,
               "lastUpdated": "2025-08-19T18:30:00"
             }
             ```
             </remarks>
             <returns>返回用户统计信息，包括总数、活跃数和新增数</returns>
             <response code="200">成功获取统计信息</response>
             <response code="500">服务器内部错误</response>
        </member>
        <member name="M:SSIC.Modules.Auth.Controllers.UserController.#ctor(System.IServiceProvider,Microsoft.Extensions.Logging.ILogger{SSIC.Modules.Auth.Controllers.UserController})">
            <summary>
            初始化User控制器
            </summary>
            <param name="serviceProvider">服务提供者</param>
            <param name="logger">日志记录器</param>
        </member>
        <member name="P:SSIC.Modules.Auth.Controllers.UserController.UserService">
            <summary>
            获取User服务
            </summary>
        </member>
    </members>
</doc>
