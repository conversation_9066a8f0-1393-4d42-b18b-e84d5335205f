/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *Service提供业务逻辑操作，如果要增加业务逻辑请在当前目录下Expands文件夹{EntityName}Service编写代码
*/
using SSIC.Modules.{BusinessName}.Services.Interfaces;
using SSIC.Infrastructure.BaseProvider;
using SSIC.Entity.{BusinessName};
using FreeSql;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SSIC.Modules.{BusinessName}.Services.Implementations
{
    /// <summary>
    /// {EntityName}服务实现类{EntityDescription}
    /// 实现{EntityName}实体的业务逻辑操作
    /// </summary>
    public partial class {EntityName}Service : ServiceBase, I{EntityName}Service
    {
        /// <summary>
        /// 初始化{EntityName}服务
        /// </summary>
        /// <param name="fsql">FreeSql实例</param>
        /// <param name="logger">日志记录器</param>
        public {EntityName}Service(IFreeSql fsql, ILogger<{EntityName}Service> logger) : base(fsql, logger)
        {
        }

        // 在此处添加{EntityName}特定的业务方法实现
    }
}
