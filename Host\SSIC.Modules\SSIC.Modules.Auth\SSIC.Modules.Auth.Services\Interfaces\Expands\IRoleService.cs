/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *Service提供业务逻辑操作，如果要增加业务逻辑请在当前目录下Expands文件夹IRoleService编写接口
 */
using SSIC.Infrastructure.BaseProvider;
using SSIC.Infrastructure.DependencyInjection;
using SSIC.Infrastructure.DependencyInjection.Interface;
using SSIC.Entity.Auth;

namespace SSIC.Modules.Auth.Services.Interfaces
{
    /// <summary>
    /// Role服务接口扩展
    /// 在此处添加自定义的业务方法接口定义
    /// </summary>
    public partial interface IRoleService
    {
        // 在此处添加自定义方法接口
        // 例如：
        // /// <summary>
        // /// 根据名称查询Role
        // /// </summary>
        // /// <param name="name">名称</param>
        // /// <returns></returns>
        // Task<List<Role>> GetByNameAsync(string name);
    }
}
