﻿<Project Sdk="Microsoft.NET.Sdk">
	<Sdk Name="Aspire.AppHost.Sdk" Version="9.0.0" />

	<PropertyGroup>
		<OutputType>Exe</OutputType>
		<TargetFramework>net10.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<IsAspireHost>true</IsAspireHost>
		<UserSecretsId>31079f85-6506-48ff-8d22-65c6a98af2d3</UserSecretsId>
		<!-- 关闭依赖项验证 -->
		<!--<EnableDependencyValidation>false</EnableDependencyValidation>
		<PreserveCompilationContext>true</PreserveCompilationContext>-->
	</PropertyGroup>



	<ItemGroup>
		<PackageReference Include="Aspire.Hosting.AppHost" Version="9.4.1" />
		<PackageReference Include="Aspire.Hosting.Redis" Version="9.4.1" />
		<PackageReference Include="Aspire.Hosting.Seq" Version="9.4.1" />
		<PackageReference Include="CommunityToolkit.Aspire.Hosting.Dapr" Version="9.7.1-beta.348" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\SSIC.DevTools\SSIC.DevTools.csproj" />
		<ProjectReference Include="..\SSIC.HostServer\SSIC.HostServer.csproj" />
		<ProjectReference Include="..\SSIC.Infrastructure\SSIC.Infrastructure.csproj" IsAspireProjectResource="false" />
		<ProjectReference Include="..\SSIC.Utilities\SSIC.Utilities.csproj" IsAspireProjectResource="false" />
	</ItemGroup>

	<!--<ItemGroup>
		<Content Include="$(NuGetPackageRoot)\ssic.infrastructure\2.0.0\contentFiles\any\any\appsettings.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="$(NuGetPackageRoot)\ssic.infrastructure\2.0.0\contentFiles\any\any\Corssettings.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="$(NuGetPackageRoot)\ssic.infrastructure\2.0.0\contentFiles\any\any\DbInfosettings.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
	</ItemGroup>-->

	<ItemGroup>
	  <None Include=".dapr\components\pubsub.yaml" />
	</ItemGroup>
	<ItemGroup>
	  <None Update="Serversettings.json">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	</ItemGroup>
</Project>
