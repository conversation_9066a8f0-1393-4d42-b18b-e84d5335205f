﻿using FreeSql.DataAnnotations;
using SSIC.Entity.EntityBase;
using System;

namespace SSIC.Entity.EntityBase.Composition
{
    /// <summary>
    ///单据表常规字段+汇率，税率
    /// </summary>
    public class DocRateBasic : EntityBasic, IEntityDoc, IEntityRate
    {
        /// <summary>
        /// 单据号
        /// </summary>
        [Column(StringLength = 50)]
        public string docno { get; set; } //50

        /// <summary>
        /// 单据类型
        /// </summary>
        public int? doctype { get; set; } = 0;//3

        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTime? docdate { get; set; } = DateTime.Now;

        /// <summary>
        /// 岗位id
        /// </summary>
        public Guid? pid { get; set; }

        /// <summary>
        /// 员工ID
        /// </summary>
        public Guid? staffid { get; set; }

        /// <summary>
        /// 订单交期
        /// </summary>
        public DateTime? deliverydate { get; set; }

        /// <summary>
        /// 下单日期
        /// </summary>
        public DateTime? startdate { get; set; } = DateTime.Now;

        /// <summary>
        /// 汇率
        /// </summary>
        public decimal? exchangerate { get; set; }

        /// <summary>
        /// 税别代号
        /// </summary>
        public string taxcode { get; set; }

        /// <summary>
        /// 税别类型
        /// </summary>
        public int? taxtype { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public decimal? taxrate { get; set; }
    }
}