using Dapr.Client;
using Google.Api;
using Grpc.Net.Client.Configuration;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using SSIC.Infrastructure.Dapr;
using StackExchange.Profiling;
using static Microsoft.ApplicationInsights.MetricDimensionNames.TelemetryContext;

namespace SSIC.HostServer.Controllers
{
    [ApiController]
    [Route("[controller]")]
    public class WeatherForecast2Controller : ControllerBase
    {
        DaprClientProvider _daprClientProvider;
        private readonly IHttpContextAccessor _accessor;
        private static readonly string[] Summaries = new[]
        {
            "Freezing", "Bracing", "Chilly", "Cool", "Mild", "Warm", "<PERSON><PERSON><PERSON>", "Hot", "Sweltering", "Scorching"
        };
        private readonly ILogger<WeatherForecast2Controller> _logger;

        public WeatherForecast2Controller(ILogger<WeatherForecast2Controller> logger, DaprClientProvider daprClientProvider, IHttpContextAccessor accessor)
        {
            this._daprClientProvider = daprClientProvider;
            _logger = logger;
            _accessor= accessor;
        }


        // �����ļ�����
        

        [HttpGet(Name = "GetWeatherForecast33333")]
        public async Task<string> Get()
        {
            //for (var i = 1; i < 10000; i++)
            //{
            //    _logger.LogInformation($"Hello, Seq�����ǵ�{i}��");

            //}
            //var html = MiniProfiler.Current.RenderIncludes(_accessor.HttpContext).Value;
            //return html;
            var id = "123"; // ���ߴ������ط���ȡ ID
            var result = await _daprClientProvider.InvokeServiceWithLoadBalancingAsync<object, object>(HttpMethod.Get, "ssic-devtools", $"WeatherForecast/Get123/{id}", null);
            var result2 = await _daprClientProvider.InvokeServiceWithLoadBalancingAsync<object, object>(HttpMethod.Get, "ssic-devtools", $"WeatherForecast/Get123/{id}", null);
            _logger.LogInformation($"Hello, Seq�����ǵ�{result2.ToJson()}��", result2);
            _logger.LogError($"Hello, Seq�����ǵ�{result2.ToJson()}��");
            _logger.LogDebug("Begin GetBasketById call from method {Method} for basket id {userId}", "@22222222", id);
            return "12345";

            //return Enumerable.Range(1, 5).Select(index => new WeatherForecast
            //{
            //    Date = DateOnly.FromDateTime(DateTime.Now.AddDays(index)),
            //    TemperatureC = Random.Shared.Next(-20, 55),
            //    Summary = Summaries[Random.Shared.Next(Summaries.Length)],
            //    aaa="1234567"
            //})
            //.ToArray();

        }
    }
}
