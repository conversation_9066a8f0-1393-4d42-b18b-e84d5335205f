using FreeSql;
using SSIC.Infrastructure.DependencyInjection.Interface;
using Microsoft.Extensions.Logging;
using System;

namespace SSIC.Infrastructure.BaseProvider
{
    /// <summary>
    /// 服务基础类
    /// </summary>
    public abstract class ServiceBase : IScoped, IServiceBase
    {
        protected readonly IFreeSql _fsql;
        protected readonly ILogger? _logger;

        /// <summary>
        /// 服务名称
        /// </summary>
        public virtual string ServiceName => GetType().Name;

        protected ServiceBase(IFreeSql fsql)
        {
            _fsql = fsql ?? throw new ArgumentNullException(nameof(fsql));
        }

        protected ServiceBase(IFreeSql fsql, ILogger logger) : this(fsql)
        {
            _logger = logger;
        }

        /// <summary>
        /// 记录信息日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="args">参数</param>
        protected void LogInformation(string message, params object[] args)
        {
            _logger?.LogInformation(message, args);
        }

        /// <summary>
        /// 记录警告日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="args">参数</param>
        protected void LogWarning(string message, params object[] args)
        {
            _logger?.LogWarning(message, args);
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="message">日志消息</param>
        /// <param name="args">参数</param>
        protected void LogError(Exception exception, string message, params object[] args)
        {
            _logger?.LogError(exception, message, args);
        }
    }
}
