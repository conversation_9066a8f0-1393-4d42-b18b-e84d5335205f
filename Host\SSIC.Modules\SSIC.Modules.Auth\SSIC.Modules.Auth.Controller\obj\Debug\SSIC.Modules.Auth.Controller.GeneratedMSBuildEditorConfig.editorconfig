is_global = true
build_property.TargetFramework = net10.0
build_property.TargetFrameworkIdentifier = .NETCoreApp
build_property.TargetFrameworkVersion = v10.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = SSIC.Modules.Auth.Controller
build_property.RootNamespace = SSIC.Modules.Auth.Controller
build_property.ProjectDir = F:\SSIC\Host\SSIC.Modules\SSIC.Modules.Auth\SSIC.Modules.Auth.Controller\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 9.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = F:\SSIC\Host\SSIC.Modules\SSIC.Modules.Auth\SSIC.Modules.Auth.Controller
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 10.0
build_property.EnableCodeStyleSeverity = 
