﻿using FreeSql.DataAnnotations;
using SSIC.Entity.EntityBase;

namespace SSIC.Entity.TenantEntity
{
    /// <summary>
    /// 租户表
    /// </summary>
    [Table(Name = "t_sys_tenant")]
    public class TenantInfo : EntityBasic
    {
        /// <summary>
        /// 租户名称
        /// </summary>
        public string tenantname { get; set; }

        /// <summary>
        /// 租户地址
        /// </summary>
        public string host { get; set; }

        /// <summary>
        /// 数据库服务器地址
        /// </summary>
        public string dataservice { get; set; }

        /// <summary>
        /// 端口号
        /// </summary>
        public string dataport { get; set; }

        /// <summary>
        /// 数据库类型
        /// SqlServer = 1,
        /// PostgreSQL = 2,
        /// Oracle = 3,
        /// Sqlite = 4,
        /// OdbcOracle = 5,
        /// OdbcSqlServer = 6,
        /// OdbcMySql = 7,
        /// OdbcPostgreSQL = 8,
        /// Odbc = 9,
        /// OdbcDamning = 10,
        /// MsAccess = 11,
        /// Dameng = 12,
        /// OdbcKingbaseES = 13,
        /// ShenTong = 14,
        /// KingbaseES = 15,
        /// Firebird = 16
        /// </summary>
        public int datatype { get; set; }

        /// <summary>
        /// 数据库登陆用户ID
        /// </summary>
        public string datauid { get; set; }

        /// <summary>
        /// 数据库登陆密码
        /// </summary>
        public string datapwd { get; set; }

        /// <summary>
        /// 数据库名称
        /// </summary>
        public string dataname { get; set; }

        /// <summary>
        /// 是否默认主库
        /// </summary>
        public bool ismaster { get; set; } = false;

        /// <summary>
        /// 租户模式1.是同库,2是分库
        /// </summary>
        public int tenanttype { get; set; } = 1;

        /// <summary>
        /// 是否启用组织权限
        /// </summary>
        public bool orgmodel { get; set; } = false;
    }
}