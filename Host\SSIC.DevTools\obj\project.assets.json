{"version": 3, "targets": {"net10.0": {"AngleSharp/1.3.0": {"type": "package", "compile": {"lib/net8.0/AngleSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/AngleSharp.dll": {"related": ".xml"}}}, "AngleSharp.Css/1.0.0-beta.151": {"type": "package", "dependencies": {"AngleSharp": "[1.0.0, 2.0.0)"}, "compile": {"lib/net8.0/AngleSharp.Css.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/AngleSharp.Css.dll": {"related": ".xml"}}}, "Azure.Core/1.47.1": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "System.ClientModel": "1.5.1", "System.Memory.Data": "8.0.1"}, "compile": {"lib/net8.0/Azure.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Azure.Core.dll": {"related": ".xml"}}}, "Azure.Identity/1.14.2": {"type": "package", "dependencies": {"Azure.Core": "1.46.1", "Microsoft.Identity.Client": "4.73.1", "Microsoft.Identity.Client.Extensions.Msal": "4.73.1"}, "compile": {"lib/net8.0/Azure.Identity.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Azure.Identity.dll": {"related": ".xml"}}}, "BouncyCastle.Cryptography/2.3.1": {"type": "package", "compile": {"lib/net6.0/BouncyCastle.Cryptography.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/BouncyCastle.Cryptography.dll": {"related": ".xml"}}}, "Castle.Core/5.2.1": {"type": "package", "compile": {"lib/net6.0/Castle.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Castle.Core.dll": {"related": ".xml"}}}, "Dapr.AspNetCore/1.16.0-rc13": {"type": "package", "dependencies": {"Dapr.Client": "1.16.0-rc13", "Dapr.Common": "1.16.0-rc13", "Google.Api.CommonProtos": "2.17.0", "Google.Protobuf": "3.31.1", "Grpc.Net.Client": "2.71.0"}, "compile": {"lib/net9.0/Dapr.AspNetCore.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Dapr.AspNetCore.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Dapr.Client/1.16.0-rc13": {"type": "package", "dependencies": {"Dapr.Common": "1.16.0-rc13", "Dapr.Protos": "1.16.0-rc13", "Google.Api.CommonProtos": "2.17.0", "Google.Protobuf": "3.31.1", "Grpc.Net.Client": "2.71.0"}, "compile": {"lib/net9.0/Dapr.Client.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Dapr.Client.dll": {"related": ".xml"}}}, "Dapr.Common/1.16.0-rc13": {"type": "package", "dependencies": {"Dapr.Protos": "1.16.0-rc13", "Google.Api.CommonProtos": "2.17.0", "Google.Protobuf": "3.31.1", "Grpc.Net.Client": "2.71.0"}, "compile": {"lib/net9.0/Dapr.Common.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Dapr.Common.dll": {"related": ".xml"}}}, "Dapr.Protos/1.16.0-rc13": {"type": "package", "dependencies": {"Google.Api.CommonProtos": "2.17.0", "Google.Protobuf": "3.31.1", "Grpc.Net.Client": "2.71.0"}, "compile": {"lib/net9.0/Dapr.Protos.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Dapr.Protos.dll": {"related": ".xml"}}}, "DM.DmProvider/8.3.1.28188": {"type": "package", "compile": {"lib/net8.0/DM.DmProvider.dll": {}}, "runtime": {"lib/net8.0/DM.DmProvider.dll": {}}, "resource": {"lib/net8.0/en/DM.DmProvider.resources.dll": {"locale": "en"}, "lib/net8.0/zh-CN/DM.DmProvider.resources.dll": {"locale": "zh-CN"}, "lib/net8.0/zh-HK/DM.DmProvider.resources.dll": {"locale": "zh-HK"}, "lib/net8.0/zh-TW/DM.DmProvider.resources.dll": {"locale": "zh-TW"}}}, "DnsClient/1.8.0": {"type": "package", "compile": {"lib/net8.0/DnsClient.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DnsClient.dll": {"related": ".xml"}}}, "FreeRedis/1.4.0": {"type": "package", "compile": {"lib/netstandard2.0/FreeRedis.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/FreeRedis.dll": {"related": ".xml"}}}, "FreeScheduler/2.0.36": {"type": "package", "dependencies": {"FreeRedis": "1.4.0", "FreeSql.DbContext": "3.5.207", "IdleBus": "1.5.3", "Newtonsoft.Json": "13.0.1", "WorkQueue": "1.3.0"}, "compile": {"lib/net8.0/FreeScheduler.dll": {}}, "runtime": {"lib/net8.0/FreeScheduler.dll": {}}}, "FreeSql/3.5.213-preview20250815": {"type": "package", "compile": {"lib/netstandard2.1/FreeSql.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.1/FreeSql.dll": {"related": ".pdb;.xml"}}}, "FreeSql.All/3.5.213-preview20250815": {"type": "package", "dependencies": {"FreeSql.Provider.Dameng": "3.5.213-preview20250815", "FreeSql.Provider.MsAccess": "3.5.213-preview20250815", "FreeSql.Provider.MySql": "3.5.213-preview20250815", "FreeSql.Provider.Odbc": "3.5.213-preview20250815", "FreeSql.Provider.Oracle": "3.5.213-preview20250815", "FreeSql.Provider.PostgreSQL": "3.5.213-preview20250815", "FreeSql.Provider.SqlServer": "3.5.213-preview20250815", "FreeSql.Provider.Sqlite": "3.5.213-preview20250815", "FreeSql.Repository": "3.5.213-preview20250815"}, "compile": {"lib/netstandard2.1/FreeSql.All.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.1/FreeSql.All.dll": {"related": ".pdb"}}}, "FreeSql.Cloud/2.0.1": {"type": "package", "dependencies": {"FreeScheduler": "2.0.33", "FreeSql.DbContext": "3.5.201", "IdleBus": "1.5.3", "Newtonsoft.Json": "13.0.1"}, "compile": {"lib/netstandard2.0/FreeSql.Cloud.dll": {}}, "runtime": {"lib/netstandard2.0/FreeSql.Cloud.dll": {}}}, "FreeSql.DbContext/3.5.213-preview20250815": {"type": "package", "dependencies": {"FreeSql": "3.5.213-preview20250815"}, "compile": {"lib/net9.0/FreeSql.DbContext.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net9.0/FreeSql.DbContext.dll": {"related": ".pdb;.xml"}}}, "FreeSql.Provider.Dameng/3.5.213-preview20250815": {"type": "package", "dependencies": {"DM.DmProvider": "8.3.1.28188", "FreeSql": "3.5.213-preview20250815"}, "compile": {"lib/net6.0/FreeSql.Provider.Dameng.dll": {"related": ".pdb"}}, "runtime": {"lib/net6.0/FreeSql.Provider.Dameng.dll": {"related": ".pdb"}}}, "FreeSql.Provider.MsAccess/3.5.213-preview20250815": {"type": "package", "dependencies": {"FreeSql": "3.5.213-preview20250815", "System.Data.OleDb": "6.0.0"}, "compile": {"lib/netstandard2.0/FreeSql.Provider.MsAccess.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/FreeSql.Provider.MsAccess.dll": {"related": ".pdb"}}}, "FreeSql.Provider.MySql/3.5.213-preview20250815": {"type": "package", "dependencies": {"FreeSql": "3.5.213-preview20250815", "MySql.Data": "9.1.0"}, "compile": {"lib/net9.0/FreeSql.Provider.MySql.dll": {"related": ".pdb"}}, "runtime": {"lib/net9.0/FreeSql.Provider.MySql.dll": {"related": ".pdb"}}}, "FreeSql.Provider.Odbc/3.5.213-preview20250815": {"type": "package", "dependencies": {"FreeSql": "3.5.213-preview20250815", "System.Data.Odbc": "8.0.0"}, "compile": {"lib/netstandard2.0/FreeSql.Provider.Odbc.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/FreeSql.Provider.Odbc.dll": {"related": ".pdb"}}}, "FreeSql.Provider.Oracle/3.5.213-preview20250815": {"type": "package", "dependencies": {"FreeSql": "3.5.213-preview20250815", "Oracle.ManagedDataAccess.Core": "23.6.1"}, "compile": {"lib/net9.0/FreeSql.Provider.Oracle.dll": {"related": ".pdb"}}, "runtime": {"lib/net9.0/FreeSql.Provider.Oracle.dll": {"related": ".pdb"}}}, "FreeSql.Provider.PostgreSQL/3.5.213-preview20250815": {"type": "package", "dependencies": {"FreeSql": "3.5.213-preview20250815", "Newtonsoft.Json": "13.0.1", "Npgsql.LegacyPostgis": "5.0.18", "Npgsql.NetTopologySuite": "5.0.18"}, "compile": {"lib/net9.0/FreeSql.Provider.PostgreSQL.dll": {"related": ".pdb"}}, "runtime": {"lib/net9.0/FreeSql.Provider.PostgreSQL.dll": {"related": ".pdb"}}}, "FreeSql.Provider.Sqlite/3.5.213-preview20250815": {"type": "package", "dependencies": {"FreeSql": "3.5.213-preview20250815", "System.Data.SQLite.Core": "1.0.119"}, "compile": {"lib/netstandard2.0/FreeSql.Provider.Sqlite.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/FreeSql.Provider.Sqlite.dll": {"related": ".pdb"}}}, "FreeSql.Provider.SqlServer/3.5.213-preview20250815": {"type": "package", "dependencies": {"FreeSql": "3.5.213-preview20250815", "Microsoft.Data.SqlClient": "5.2.2"}, "compile": {"lib/net9.0/FreeSql.Provider.SqlServer.dll": {"related": ".pdb"}}, "runtime": {"lib/net9.0/FreeSql.Provider.SqlServer.dll": {"related": ".pdb"}}}, "FreeSql.Repository/3.5.213-preview20250815": {"type": "package", "dependencies": {"FreeSql.DbContext": "3.5.213-preview20250815"}, "compile": {"lib/net9.0/FreeSql.Repository.dll": {"related": ".pdb"}}, "runtime": {"lib/net9.0/FreeSql.Repository.dll": {"related": ".pdb"}}}, "Google.Api.CommonProtos/2.17.0": {"type": "package", "dependencies": {"Google.Protobuf": "[3.31.1, 4.0.0]"}, "compile": {"lib/netstandard2.0/Google.Api.CommonProtos.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Google.Api.CommonProtos.dll": {"related": ".pdb;.xml"}}, "build": {"build/_._": {}}}, "Google.Protobuf/3.31.1": {"type": "package", "compile": {"lib/net5.0/Google.Protobuf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net5.0/Google.Protobuf.dll": {"related": ".pdb;.xml"}}}, "Grpc.Core.Api/2.71.0": {"type": "package", "compile": {"lib/netstandard2.1/Grpc.Core.Api.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.1/Grpc.Core.Api.dll": {"related": ".pdb;.xml"}}}, "Grpc.Net.Client/2.71.0": {"type": "package", "dependencies": {"Grpc.Net.Common": "2.71.0"}, "compile": {"lib/net8.0/Grpc.Net.Client.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Grpc.Net.Client.dll": {"related": ".pdb;.xml"}}}, "Grpc.Net.Common/2.71.0": {"type": "package", "dependencies": {"Grpc.Core.Api": "2.71.0"}, "compile": {"lib/net8.0/Grpc.Net.Common.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Grpc.Net.Common.dll": {"related": ".pdb;.xml"}}}, "IdleBus/1.5.3": {"type": "package", "compile": {"lib/netstandard2.0/IdleBus.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/IdleBus.dll": {"related": ".xml"}}}, "K4os.Compression.LZ4/1.3.8": {"type": "package", "compile": {"lib/net6.0/K4os.Compression.LZ4.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/K4os.Compression.LZ4.dll": {"related": ".xml"}}}, "K4os.Compression.LZ4.Streams/1.3.8": {"type": "package", "dependencies": {"K4os.Compression.LZ4": "1.3.8", "K4os.Hash.xxHash": "1.0.8"}, "compile": {"lib/net6.0/K4os.Compression.LZ4.Streams.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/K4os.Compression.LZ4.Streams.dll": {"related": ".xml"}}}, "K4os.Hash.xxHash/1.0.8": {"type": "package", "compile": {"lib/net6.0/K4os.Hash.xxHash.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/K4os.Hash.xxHash.dll": {"related": ".xml"}}}, "Mapster/7.4.2-pre02": {"type": "package", "dependencies": {"Mapster.Core": "1.2.3-pre02"}, "compile": {"lib/net9.0/Mapster.dll": {}}, "runtime": {"lib/net9.0/Mapster.dll": {}}}, "Mapster.Core/1.2.3-pre02": {"type": "package", "compile": {"lib/net9.0/Mapster.Core.dll": {}}, "runtime": {"lib/net9.0/Mapster.Core.dll": {}}}, "Masa.BuildingBlocks.Configuration/1.2.0-preview.10": {"type": "package", "compile": {"lib/net10.0/Masa.BuildingBlocks.Configuration.dll": {"related": ".pdb"}}, "runtime": {"lib/net10.0/Masa.BuildingBlocks.Configuration.dll": {"related": ".pdb"}}}, "Masa.BuildingBlocks.Data/1.2.0-preview.10": {"type": "package", "dependencies": {"Masa.BuildingBlocks.Data.Contracts": "1.2.0-preview.10", "Masa.Utils.Caching.Memory": "1.2.0-preview.10", "Masa.Utils.Extensions.DotNet": "1.2.0-preview.10"}, "compile": {"lib/net10.0/Masa.BuildingBlocks.Data.dll": {"related": ".pdb"}}, "runtime": {"lib/net10.0/Masa.BuildingBlocks.Data.dll": {"related": ".pdb"}}}, "Masa.BuildingBlocks.Data.Contracts/1.2.0-preview.10": {"type": "package", "dependencies": {"Masa.Utils.Models.Config": "1.2.0-preview.10"}, "compile": {"lib/net10.0/Masa.BuildingBlocks.Data.Contracts.dll": {"related": ".pdb"}}, "runtime": {"lib/net10.0/Masa.BuildingBlocks.Data.Contracts.dll": {"related": ".pdb"}}}, "Masa.BuildingBlocks.Development.DaprStarter/1.2.0-preview.10": {"type": "package", "dependencies": {"Masa.BuildingBlocks.Data": "1.2.0-preview.10", "Masa.BuildingBlocks.Exceptions": "1.2.0-preview.10"}, "compile": {"lib/net10.0/Masa.BuildingBlocks.Development.DaprStarter.dll": {"related": ".pdb"}}, "runtime": {"lib/net10.0/Masa.BuildingBlocks.Development.DaprStarter.dll": {"related": ".pdb"}}}, "Masa.BuildingBlocks.Exceptions/1.2.0-preview.10": {"type": "package", "dependencies": {"Masa.BuildingBlocks.Data": "1.2.0-preview.10", "Masa.BuildingBlocks.Globalization.I18n": "1.2.0-preview.10", "Masa.Utils.Extensions.DotNet": "1.2.0-preview.10"}, "compile": {"lib/net10.0/Masa.BuildingBlocks.Exceptions.dll": {"related": ".pdb"}}, "runtime": {"lib/net10.0/Masa.BuildingBlocks.Exceptions.dll": {"related": ".pdb"}}}, "Masa.BuildingBlocks.Globalization.I18n/1.2.0-preview.10": {"type": "package", "dependencies": {"Masa.BuildingBlocks.Data": "1.2.0-preview.10"}, "compile": {"lib/net10.0/Masa.BuildingBlocks.Globalization.I18n.dll": {"related": ".pdb"}}, "runtime": {"lib/net10.0/Masa.BuildingBlocks.Globalization.I18n.dll": {"related": ".pdb"}}}, "Masa.Contrib.Development.DaprStarter/1.2.0-preview.10": {"type": "package", "dependencies": {"Masa.BuildingBlocks.Configuration": "1.2.0-preview.10", "Masa.BuildingBlocks.Development.DaprStarter": "1.2.0-preview.10", "Masa.Utils.Extensions.DotNet": "1.2.0-preview.10"}, "compile": {"lib/net10.0/Masa.Contrib.Development.DaprStarter.dll": {"related": ".pdb"}}, "runtime": {"lib/net10.0/Masa.Contrib.Development.DaprStarter.dll": {"related": ".pdb"}}}, "Masa.Contrib.Development.DaprStarter.AspNetCore/1.2.0-preview.10": {"type": "package", "dependencies": {"Masa.BuildingBlocks.Exceptions": "1.2.0-preview.10", "Masa.Contrib.Development.DaprStarter": "1.2.0-preview.10"}, "compile": {"lib/net10.0/Masa.Contrib.Development.DaprStarter.AspNetCore.dll": {"related": ".pdb"}}, "runtime": {"lib/net10.0/Masa.Contrib.Development.DaprStarter.AspNetCore.dll": {"related": ".pdb"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Masa.Utils.Caching.Memory/1.2.0-preview.10": {"type": "package", "compile": {"lib/net10.0/Masa.Utils.Caching.Memory.dll": {"related": ".pdb"}}, "runtime": {"lib/net10.0/Masa.Utils.Caching.Memory.dll": {"related": ".pdb"}}}, "Masa.Utils.Extensions.DotNet/1.2.0-preview.10": {"type": "package", "compile": {"lib/net10.0/Masa.Utils.Extensions.DotNet.dll": {"related": ".pdb"}}, "runtime": {"lib/net10.0/Masa.Utils.Extensions.DotNet.dll": {"related": ".pdb"}}}, "Masa.Utils.Models.Config/1.2.0-preview.10": {"type": "package", "compile": {"lib/net10.0/Masa.Utils.Models.Config.dll": {"related": ".pdb"}}, "runtime": {"lib/net10.0/Masa.Utils.Models.Config.dll": {"related": ".pdb"}}}, "Masuit.Tools.Abstractions/2025.5.0": {"type": "package", "dependencies": {"AngleSharp": "1.3.0", "AngleSharp.Css": "1.0.0-beta.151", "Castle.Core": "5.2.1", "DnsClient": "1.8.0", "Newtonsoft.Json": "13.0.3", "SharpCompress": "0.40.0", "SixLabors.ImageSharp": "3.1.11", "SixLabors.ImageSharp.Drawing": "2.1.7", "System.Diagnostics.PerformanceCounter": "9.0.8", "System.Management": "9.0.8"}, "compile": {"lib/net9.0/Masuit.Tools.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Masuit.Tools.Abstractions.dll": {"related": ".xml"}}}, "Masuit.Tools.Core/2025.5.0": {"type": "package", "dependencies": {"Masuit.Tools.Abstractions": "2025.5.0", "Microsoft.EntityFrameworkCore": "9.0.8"}, "compile": {"lib/net9.0/Masuit.Tools.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Masuit.Tools.Core.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/10.0.0-preview.7.25380.108": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.0.1"}, "compile": {"lib/net10.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"related": ".xml"}}, "runtime": {"lib/net10.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Microsoft.AspNetCore.OpenApi/9.0.1": {"type": "package", "dependencies": {"Microsoft.OpenApi": "1.6.17"}, "compile": {"lib/net9.0/Microsoft.AspNetCore.OpenApi.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.OpenApi.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "compile": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.Bcl.Cryptography/9.0.4": {"type": "package", "compile": {"lib/net9.0/Microsoft.Bcl.Cryptography.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Bcl.Cryptography.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Data.SqlClient/6.1.1": {"type": "package", "dependencies": {"Azure.Core": "1.47.1", "Azure.Identity": "1.14.2", "Microsoft.Bcl.Cryptography": "9.0.4", "Microsoft.Data.SqlClient.SNI.runtime": "6.0.2", "Microsoft.IdentityModel.JsonWebTokens": "7.7.1", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.7.1", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "9.0.4", "System.Security.Cryptography.Pkcs": "9.0.4"}, "compile": {"ref/net9.0/Microsoft.Data.SqlClient.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Data.SqlClient.dll": {"related": ".xml"}}, "resource": {"lib/net9.0/cs/Microsoft.Data.SqlClient.resources.dll": {"locale": "cs"}, "lib/net9.0/de/Microsoft.Data.SqlClient.resources.dll": {"locale": "de"}, "lib/net9.0/es/Microsoft.Data.SqlClient.resources.dll": {"locale": "es"}, "lib/net9.0/fr/Microsoft.Data.SqlClient.resources.dll": {"locale": "fr"}, "lib/net9.0/it/Microsoft.Data.SqlClient.resources.dll": {"locale": "it"}, "lib/net9.0/ja/Microsoft.Data.SqlClient.resources.dll": {"locale": "ja"}, "lib/net9.0/ko/Microsoft.Data.SqlClient.resources.dll": {"locale": "ko"}, "lib/net9.0/pl/Microsoft.Data.SqlClient.resources.dll": {"locale": "pl"}, "lib/net9.0/pt-BR/Microsoft.Data.SqlClient.resources.dll": {"locale": "pt-BR"}, "lib/net9.0/ru/Microsoft.Data.SqlClient.resources.dll": {"locale": "ru"}, "lib/net9.0/tr/Microsoft.Data.SqlClient.resources.dll": {"locale": "tr"}, "lib/net9.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Hans"}, "lib/net9.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Han<PERSON>"}}, "runtimeTargets": {"runtimes/unix/lib/net9.0/Microsoft.Data.SqlClient.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net9.0/Microsoft.Data.SqlClient.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Data.SqlClient.SNI.runtime/6.0.2": {"type": "package", "runtimeTargets": {"runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.DependencyValidation.Analyzers/0.11.0": {"type": "package"}, "Microsoft.EntityFrameworkCore/9.0.8": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.8", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.8"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.EntityFrameworkCore.props": {}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.8": {"type": "package", "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.8": {"type": "package"}, "Microsoft.Extensions.AmbientMetadata.Application/9.8.0": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.AmbientMetadata.Application.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.AmbientMetadata.Application.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.ApiDescription.Server/9.0.0": {"type": "package", "build": {"build/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "Microsoft.Extensions.Compliance.Abstractions/9.8.0": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.Compliance.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Compliance.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration/10.0.0-preview.7.25380.108": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Primitives": "10.0.0-preview.7.25380.108"}, "compile": {"lib/net10.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net10.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/10.0.0-preview.7.25380.108": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "10.0.0-preview.7.25380.108"}, "compile": {"lib/net10.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net10.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Binder/10.0.0-preview.7.25380.108": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.7.25380.108"}, "compile": {"lib/net10.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "runtime": {"lib/net10.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "build": {"buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets": {}}}, "Microsoft.Extensions.Configuration.CommandLine/10.0.0-preview.7.25380.108": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.7.25380.108"}, "compile": {"lib/net10.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"related": ".xml"}}, "runtime": {"lib/net10.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/10.0.0-preview.7.25380.108": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.7.25380.108"}, "compile": {"lib/net10.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}, "runtime": {"lib/net10.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.FileExtensions/10.0.0-preview.7.25380.108": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.FileProviders.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.FileProviders.Physical": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Primitives": "10.0.0-preview.7.25380.108"}, "compile": {"lib/net10.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net10.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Json/10.0.0-preview.7.25380.108": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.FileExtensions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.FileProviders.Abstractions": "10.0.0-preview.7.25380.108"}, "compile": {"lib/net10.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net10.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.UserSecrets/10.0.0-preview.7.25380.108": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.Json": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.FileProviders.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.FileProviders.Physical": "10.0.0-preview.7.25380.108"}, "compile": {"lib/net10.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "runtime": {"lib/net10.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Configuration.UserSecrets.props": {}, "buildTransitive/net8.0/Microsoft.Extensions.Configuration.UserSecrets.targets": {}}}, "Microsoft.Extensions.DependencyInjection/10.0.0-preview.7.25380.108": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108"}, "compile": {"lib/net10.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net10.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/10.0.0-preview.7.25380.108": {"type": "package", "compile": {"lib/net10.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net10.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.AutoActivation/9.8.0": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.AutoActivation.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.AutoActivation.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyModel/9.0.0": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Diagnostics/10.0.0-preview.7.25380.108": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Diagnostics.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Options.ConfigurationExtensions": "10.0.0-preview.7.25380.108"}, "compile": {"lib/net10.0/Microsoft.Extensions.Diagnostics.dll": {"related": ".xml"}}, "runtime": {"lib/net10.0/Microsoft.Extensions.Diagnostics.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Diagnostics.Abstractions/10.0.0-preview.7.25380.108": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Options": "10.0.0-preview.7.25380.108"}, "compile": {"lib/net10.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net10.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Diagnostics.ExceptionSummarization/9.8.0": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.Diagnostics.ExceptionSummarization.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.ExceptionSummarization.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/10.0.0-preview.7.25380.108": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "10.0.0-preview.7.25380.108"}, "compile": {"lib/net10.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net10.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Physical/10.0.0-preview.7.25380.108": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.FileSystemGlobbing": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Primitives": "10.0.0-preview.7.25380.108"}, "compile": {"lib/net10.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "runtime": {"lib/net10.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.FileSystemGlobbing/10.0.0-preview.7.25380.108": {"type": "package", "compile": {"lib/net10.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "runtime": {"lib/net10.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Hosting/10.0.0-preview.7.25380.108": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.Binder": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.CommandLine": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.EnvironmentVariables": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.FileExtensions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.Json": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.UserSecrets": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.DependencyInjection": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Diagnostics": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.FileProviders.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.FileProviders.Physical": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Hosting.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.Configuration": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.Console": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.Debug": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.EventLog": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.EventSource": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Options": "10.0.0-preview.7.25380.108"}, "compile": {"lib/net10.0/Microsoft.Extensions.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net10.0/Microsoft.Extensions.Hosting.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Hosting.Abstractions/10.0.0-preview.7.25380.108": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Diagnostics.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.FileProviders.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.7.25380.108"}, "compile": {"lib/net10.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net10.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Http.Diagnostics/9.8.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Telemetry": "9.8.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.Http.Diagnostics.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.Diagnostics.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Http.Resilience/9.8.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Http.Diagnostics": "9.8.0", "Microsoft.Extensions.Resilience": "9.8.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.Http.Resilience.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.Resilience.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Http.Resilience.targets": {}}}, "Microsoft.Extensions.Logging/10.0.0-preview.7.25380.108": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Options": "10.0.0-preview.7.25380.108"}, "compile": {"lib/net10.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net10.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/10.0.0-preview.7.25380.108": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108"}, "compile": {"lib/net10.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net10.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Logging.Configuration/10.0.0-preview.7.25380.108": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.Binder": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Options": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Options.ConfigurationExtensions": "10.0.0-preview.7.25380.108"}, "compile": {"lib/net10.0/Microsoft.Extensions.Logging.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net10.0/Microsoft.Extensions.Logging.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.Console/10.0.0-preview.7.25380.108": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.Configuration": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Options": "10.0.0-preview.7.25380.108"}, "compile": {"lib/net10.0/Microsoft.Extensions.Logging.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net10.0/Microsoft.Extensions.Logging.Console.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.Debug/10.0.0-preview.7.25380.108": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.7.25380.108"}, "compile": {"lib/net10.0/Microsoft.Extensions.Logging.Debug.dll": {"related": ".xml"}}, "runtime": {"lib/net10.0/Microsoft.Extensions.Logging.Debug.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.EventLog/10.0.0-preview.7.25380.108": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Options": "10.0.0-preview.7.25380.108", "System.Diagnostics.EventLog": "10.0.0-preview.7.25380.108"}, "compile": {"lib/net10.0/Microsoft.Extensions.Logging.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net10.0/Microsoft.Extensions.Logging.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.EventSource/10.0.0-preview.7.25380.108": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Options": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Primitives": "10.0.0-preview.7.25380.108"}, "compile": {"lib/net10.0/Microsoft.Extensions.Logging.EventSource.dll": {"related": ".xml"}}, "runtime": {"lib/net10.0/Microsoft.Extensions.Logging.EventSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Options/10.0.0-preview.7.25380.108": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Primitives": "10.0.0-preview.7.25380.108"}, "compile": {"lib/net10.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net10.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/10.0.0-preview.7.25380.108": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.Binder": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Options": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Primitives": "10.0.0-preview.7.25380.108"}, "compile": {"lib/net10.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net10.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Primitives/10.0.0-preview.7.25380.108": {"type": "package", "compile": {"lib/net10.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net10.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Resilience/9.8.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Diagnostics.ExceptionSummarization": "9.8.0", "Microsoft.Extensions.Telemetry.Abstractions": "9.8.0", "Polly.Extensions": "8.4.2", "Polly.RateLimiting": "8.4.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.Resilience.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Resilience.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.ServiceDiscovery/9.4.1": {"type": "package", "dependencies": {"Microsoft.Extensions.ServiceDiscovery.Abstractions": "9.4.1"}, "compile": {"lib/net8.0/Microsoft.Extensions.ServiceDiscovery.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.ServiceDiscovery.dll": {"related": ".xml"}}}, "Microsoft.Extensions.ServiceDiscovery.Abstractions/9.4.1": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.ServiceDiscovery.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.ServiceDiscovery.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Telemetry/9.8.0": {"type": "package", "dependencies": {"Microsoft.Extensions.AmbientMetadata.Application": "9.8.0", "Microsoft.Extensions.DependencyInjection.AutoActivation": "9.8.0", "Microsoft.Extensions.Telemetry.Abstractions": "9.8.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.Telemetry.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Telemetry.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Telemetry.Abstractions/9.8.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Compliance.Abstractions": "9.8.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.Telemetry.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Telemetry.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Telemetry.Abstractions.props": {}, "buildTransitive/net8.0/Microsoft.Extensions.Telemetry.Abstractions.targets": {}}}, "Microsoft.Identity.Client/4.73.1": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0"}, "compile": {"lib/net8.0/Microsoft.Identity.Client.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Identity.Client.dll": {"related": ".xml"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.73.1": {"type": "package", "dependencies": {"Microsoft.Identity.Client": "4.73.1", "System.Security.Cryptography.ProtectedData": "4.5.0"}, "compile": {"lib/net8.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Abstractions/8.0.1": {"type": "package", "compile": {"lib/net9.0/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.JsonWebTokens/8.0.1": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "8.0.1"}, "compile": {"lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Logging/8.0.1": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "8.0.1"}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols/8.0.1": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "8.0.1"}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.0.1": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Protocols": "8.0.1", "System.IdentityModel.Tokens.Jwt": "8.0.1"}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Tokens/8.0.1": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Logging": "8.0.1"}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}}, "Microsoft.OpenApi/1.6.24": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"related": ".pdb;.xml"}}}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"related": ".pdb;.xml"}}}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets/1.22.1": {"type": "package", "build": {"build/Microsoft.VisualStudio.Azure.Containers.Tools.Targets.props": {}, "build/Microsoft.VisualStudio.Azure.Containers.Tools.Targets.targets": {}}}, "MiniProfiler.AspNetCore/4.5.4": {"type": "package", "dependencies": {"MiniProfiler.Shared": "4.5.4"}, "compile": {"lib/net8.0/MiniProfiler.AspNetCore.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/MiniProfiler.AspNetCore.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "MiniProfiler.AspNetCore.Mvc/4.5.4": {"type": "package", "dependencies": {"MiniProfiler.AspNetCore": "4.5.4"}, "compile": {"lib/net8.0/MiniProfiler.AspNetCore.Mvc.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/MiniProfiler.AspNetCore.Mvc.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "MiniProfiler.Shared/4.5.4": {"type": "package", "compile": {"lib/net8.0/MiniProfiler.Shared.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/MiniProfiler.Shared.dll": {"related": ".xml"}}}, "MySql.Data/9.1.0": {"type": "package", "dependencies": {"BouncyCastle.Cryptography": "2.3.1", "Google.Protobuf": "3.26.1", "K4os.Compression.LZ4.Streams": "1.3.8", "System.Configuration.ConfigurationManager": "8.0.0", "System.Security.Permissions": "8.0.0", "ZstdSharp.Port": "0.8.0"}, "compile": {"lib/net8.0/MySql.Data.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/MySql.Data.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win-x64/native/comerr64.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/gssapi64.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/k5sprt64.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/krb5_64.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/krbcc64.dll": {"assetType": "native", "rid": "win-x64"}}}, "NetTopologySuite/2.0.0": {"type": "package", "compile": {"lib/netstandard2.0/NetTopologySuite.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NetTopologySuite.dll": {"related": ".xml"}}}, "NetTopologySuite.IO.PostGis/2.1.0": {"type": "package", "dependencies": {"NetTopologySuite": "[2.0.0, 3.0.0-A)"}, "compile": {"lib/netstandard2.1/NetTopologySuite.IO.PostGis.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/NetTopologySuite.IO.PostGis.dll": {"related": ".xml"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Npgsql/9.0.3": {"type": "package", "compile": {"lib/net8.0/Npgsql.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Npgsql.dll": {"related": ".xml"}}}, "Npgsql.LegacyPostgis/5.0.18": {"type": "package", "dependencies": {"Npgsql": "5.0.18"}, "compile": {"lib/netstandard2.0/Npgsql.LegacyPostgis.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Npgsql.LegacyPostgis.dll": {"related": ".xml"}}}, "Npgsql.NetTopologySuite/5.0.18": {"type": "package", "dependencies": {"NetTopologySuite.IO.PostGIS": "2.1.0", "Npgsql": "5.0.18"}, "compile": {"lib/netstandard2.0/Npgsql.NetTopologySuite.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Npgsql.NetTopologySuite.dll": {"related": ".xml"}}}, "OpenTelemetry/1.12.0": {"type": "package", "dependencies": {"OpenTelemetry.Api.ProviderBuilderExtensions": "1.12.0"}, "compile": {"lib/net9.0/OpenTelemetry.dll": {"related": ".dll-keyless.pem;.dll-keyless.sig;.xml"}}, "runtime": {"lib/net9.0/OpenTelemetry.dll": {"related": ".dll-keyless.pem;.dll-keyless.sig;.xml"}}}, "OpenTelemetry.Api/1.12.0": {"type": "package", "compile": {"lib/net9.0/OpenTelemetry.Api.dll": {"related": ".dll-keyless.pem;.dll-keyless.sig;.xml"}}, "runtime": {"lib/net9.0/OpenTelemetry.Api.dll": {"related": ".dll-keyless.pem;.dll-keyless.sig;.xml"}}}, "OpenTelemetry.Api.ProviderBuilderExtensions/1.12.0": {"type": "package", "dependencies": {"OpenTelemetry.Api": "1.12.0"}, "compile": {"lib/net9.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll": {"related": ".dll-keyless.pem;.dll-keyless.sig;.xml"}}, "runtime": {"lib/net9.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll": {"related": ".dll-keyless.pem;.dll-keyless.sig;.xml"}}}, "OpenTelemetry.Exporter.OpenTelemetryProtocol/1.12.0": {"type": "package", "dependencies": {"OpenTelemetry": "1.12.0"}, "compile": {"lib/net9.0/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll": {"related": ".dll-keyless.pem;.dll-keyless.sig;.xml"}}, "runtime": {"lib/net9.0/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll": {"related": ".dll-keyless.pem;.dll-keyless.sig;.xml"}}}, "OpenTelemetry.Exporter.Zipkin/1.12.0": {"type": "package", "dependencies": {"OpenTelemetry": "1.12.0"}, "compile": {"lib/net9.0/OpenTelemetry.Exporter.Zipkin.dll": {"related": ".dll-keyless.pem;.dll-keyless.sig;.xml"}}, "runtime": {"lib/net9.0/OpenTelemetry.Exporter.Zipkin.dll": {"related": ".dll-keyless.pem;.dll-keyless.sig;.xml"}}}, "OpenTelemetry.Extensions.Hosting/1.12.0": {"type": "package", "dependencies": {"OpenTelemetry": "1.12.0"}, "compile": {"lib/net9.0/OpenTelemetry.Extensions.Hosting.dll": {"related": ".dll-keyless.pem;.dll-keyless.sig;.xml"}}, "runtime": {"lib/net9.0/OpenTelemetry.Extensions.Hosting.dll": {"related": ".dll-keyless.pem;.dll-keyless.sig;.xml"}}}, "OpenTelemetry.Instrumentation.AspNetCore/1.12.0": {"type": "package", "dependencies": {"OpenTelemetry.Api.ProviderBuilderExtensions": "[1.12.0, 2.0.0)"}, "compile": {"lib/net8.0/OpenTelemetry.Instrumentation.AspNetCore.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/OpenTelemetry.Instrumentation.AspNetCore.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "OpenTelemetry.Instrumentation.GrpcCore/1.0.0-beta.6": {"type": "package", "dependencies": {"Google.Protobuf": "[3.15.0, 4.0.0)", "Grpc.Core.Api": "[2.23.0, 3.0.0)", "OpenTelemetry.Api": "[1.8.1, 2.0.0)"}, "compile": {"lib/netstandard2.0/OpenTelemetry.Instrumentation.GrpcCore.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/OpenTelemetry.Instrumentation.GrpcCore.dll": {"related": ".xml"}}}, "OpenTelemetry.Instrumentation.GrpcNetClient/1.12.0-beta.1": {"type": "package", "dependencies": {"OpenTelemetry": "[1.12.0, 2.0.0)"}, "compile": {"lib/net8.0/OpenTelemetry.Instrumentation.GrpcNetClient.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/OpenTelemetry.Instrumentation.GrpcNetClient.dll": {"related": ".xml"}}}, "OpenTelemetry.Instrumentation.Http/1.12.0": {"type": "package", "dependencies": {"OpenTelemetry.Api.ProviderBuilderExtensions": "[1.12.0, 2.0.0)"}, "compile": {"lib/net8.0/OpenTelemetry.Instrumentation.Http.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/OpenTelemetry.Instrumentation.Http.dll": {"related": ".xml"}}}, "OpenTelemetry.Instrumentation.Process/1.12.0-beta.1": {"type": "package", "dependencies": {"OpenTelemetry.Api": "[1.12.0, 2.0.0)"}, "compile": {"lib/netstandard2.0/OpenTelemetry.Instrumentation.Process.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/OpenTelemetry.Instrumentation.Process.dll": {"related": ".xml"}}}, "OpenTelemetry.Instrumentation.Runtime/1.12.0": {"type": "package", "dependencies": {"OpenTelemetry.Api": "[1.12.0, 2.0.0)"}, "compile": {"lib/net8.0/OpenTelemetry.Instrumentation.Runtime.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/OpenTelemetry.Instrumentation.Runtime.dll": {"related": ".xml"}}}, "Oracle.ManagedDataAccess.Core/23.6.1": {"type": "package", "dependencies": {"System.Diagnostics.PerformanceCounter": "8.0.0", "System.DirectoryServices.Protocols": "8.0.0", "System.Security.Cryptography.Pkcs": "8.0.0"}, "compile": {"lib/net8.0/Oracle.ManagedDataAccess.dll": {}}, "runtime": {"lib/net8.0/Oracle.ManagedDataAccess.dll": {}}}, "Polly.Core/8.4.2": {"type": "package", "compile": {"lib/net8.0/Polly.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Polly.Core.dll": {"related": ".pdb;.xml"}}}, "Polly.Extensions/8.4.2": {"type": "package", "dependencies": {"Polly.Core": "8.4.2"}, "compile": {"lib/net8.0/Polly.Extensions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Polly.Extensions.dll": {"related": ".pdb;.xml"}}}, "Polly.RateLimiting/8.4.2": {"type": "package", "dependencies": {"Polly.Core": "8.4.2"}, "compile": {"lib/net8.0/Polly.RateLimiting.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Polly.RateLimiting.dll": {"related": ".pdb;.xml"}}}, "QRCoder/1.6.0": {"type": "package", "compile": {"lib/net6.0/QRCoder.dll": {}}, "runtime": {"lib/net6.0/QRCoder.dll": {}}}, "Scalar.AspNetCore/2.6.9": {"type": "package", "compile": {"lib/net9.0/Scalar.AspNetCore.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Scalar.AspNetCore.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Serilog/4.3.1-dev-02373": {"type": "package", "compile": {"lib/net9.0/Serilog.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Serilog.dll": {"related": ".xml"}}, "build": {"build/_._": {}}}, "Serilog.AspNetCore/9.0.0": {"type": "package", "dependencies": {"Serilog": "4.2.0", "Serilog.Extensions.Hosting": "9.0.0", "Serilog.Formatting.Compact": "3.0.0", "Serilog.Settings.Configuration": "9.0.0", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.Debug": "3.0.0", "Serilog.Sinks.File": "6.0.0"}, "compile": {"lib/net9.0/Serilog.AspNetCore.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Serilog.AspNetCore.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Serilog.Enrichers.Environment/3.0.1": {"type": "package", "dependencies": {"Serilog": "4.0.0"}, "compile": {"lib/net8.0/Serilog.Enrichers.Environment.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Enrichers.Environment.dll": {"related": ".xml"}}}, "Serilog.Enrichers.Thread/4.0.0": {"type": "package", "dependencies": {"Serilog": "4.0.0"}, "compile": {"lib/net8.0/Serilog.Enrichers.Thread.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Enrichers.Thread.dll": {"related": ".xml"}}}, "Serilog.Expressions/5.1.0-dev-02301": {"type": "package", "dependencies": {"Serilog": "4.2.0"}, "compile": {"lib/net9.0/Serilog.Expressions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Serilog.Expressions.dll": {"related": ".xml"}}}, "Serilog.Extensions.Hosting/9.0.1-dev-02307": {"type": "package", "dependencies": {"Serilog": "4.2.0", "Serilog.Extensions.Logging": "9.0.0"}, "compile": {"lib/net9.0/Serilog.Extensions.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Serilog.Extensions.Hosting.dll": {"related": ".xml"}}}, "Serilog.Extensions.Logging/9.0.3-dev-02320": {"type": "package", "dependencies": {"Serilog": "4.2.0"}, "compile": {"lib/net9.0/Serilog.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Serilog.Extensions.Logging.dll": {"related": ".xml"}}}, "Serilog.Formatting.Compact/3.0.0": {"type": "package", "dependencies": {"Serilog": "4.0.0"}, "compile": {"lib/net8.0/Serilog.Formatting.Compact.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Formatting.Compact.dll": {"related": ".xml"}}}, "Serilog.Settings.Configuration/9.0.1-dev-02317": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyModel": "9.0.0", "Serilog": "4.2.0"}, "compile": {"lib/net9.0/Serilog.Settings.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Serilog.Settings.Configuration.dll": {"related": ".xml"}}}, "Serilog.Sinks.Console/6.0.1-dev-00953": {"type": "package", "dependencies": {"Serilog": "4.0.0"}, "compile": {"lib/net8.0/Serilog.Sinks.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Sinks.Console.dll": {"related": ".xml"}}}, "Serilog.Sinks.Debug/3.0.0": {"type": "package", "dependencies": {"Serilog": "4.0.0"}, "compile": {"lib/net8.0/Serilog.Sinks.Debug.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Sinks.Debug.dll": {"related": ".xml"}}}, "Serilog.Sinks.File/6.0.0": {"type": "package", "dependencies": {"Serilog": "4.0.0"}, "compile": {"lib/net8.0/Serilog.Sinks.File.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Sinks.File.dll": {"related": ".xml"}}}, "Serilog.Sinks.OpenTelemetry/4.2.1-dev-02306": {"type": "package", "dependencies": {"Google.Protobuf": "3.30.1", "Grpc.Net.Client": "2.71.0", "Serilog": "4.2.0"}, "compile": {"lib/net9.0/Serilog.Sinks.OpenTelemetry.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Serilog.Sinks.OpenTelemetry.dll": {"related": ".xml"}}}, "Serilog.Sinks.Seq/9.0.0": {"type": "package", "dependencies": {"Serilog": "4.2.0", "Serilog.Sinks.File": "6.0.0"}, "compile": {"lib/net6.0/Serilog.Sinks.Seq.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Serilog.Sinks.Seq.dll": {"related": ".xml"}}}, "SharpCompress/0.40.0": {"type": "package", "dependencies": {"ZstdSharp.Port": "0.8.5"}, "compile": {"lib/net8.0/SharpCompress.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/SharpCompress.dll": {"related": ".pdb"}}}, "SixLabors.Fonts/2.1.3": {"type": "package", "compile": {"lib/net6.0/SixLabors.Fonts.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/SixLabors.Fonts.dll": {"related": ".xml"}}}, "SixLabors.ImageSharp/3.1.11": {"type": "package", "compile": {"lib/net6.0/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "build": {"build/_._": {}}}, "SixLabors.ImageSharp.Drawing/2.1.7": {"type": "package", "dependencies": {"SixLabors.Fonts": "2.1.3", "SixLabors.ImageSharp": "3.1.11"}, "compile": {"lib/net6.0/SixLabors.ImageSharp.Drawing.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/SixLabors.ImageSharp.Drawing.dll": {"related": ".xml"}}}, "Stub.System.Data.SQLite.Core.NetStandard/1.0.119": {"type": "package", "compile": {"lib/netstandard2.1/System.Data.SQLite.dll": {"related": ".dll.altconfig;.xml"}}, "runtime": {"lib/netstandard2.1/System.Data.SQLite.dll": {"related": ".dll.altconfig;.xml"}}, "runtimeTargets": {"runtimes/linux-x64/native/SQLite.Interop.dll": {"assetType": "native", "rid": "linux-x64"}, "runtimes/osx-x64/native/SQLite.Interop.dll": {"assetType": "native", "rid": "osx-x64"}, "runtimes/win-x64/native/SQLite.Interop.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/SQLite.Interop.dll": {"assetType": "native", "rid": "win-x86"}}}, "Swashbuckle.AspNetCore/9.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.ApiDescription.Server": "9.0.0", "Swashbuckle.AspNetCore.Swagger": "9.0.3", "Swashbuckle.AspNetCore.SwaggerGen": "9.0.3", "Swashbuckle.AspNetCore.SwaggerUI": "9.0.3"}, "build": {"build/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "Swashbuckle.AspNetCore.Swagger/9.0.3": {"type": "package", "dependencies": {"Microsoft.OpenApi": "1.6.23"}, "compile": {"lib/net9.0/Swashbuckle.AspNetCore.Swagger.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net9.0/Swashbuckle.AspNetCore.Swagger.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Swashbuckle.AspNetCore.SwaggerGen/9.0.3": {"type": "package", "dependencies": {"Swashbuckle.AspNetCore.Swagger": "9.0.3"}, "compile": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"related": ".pdb;.xml"}}}, "Swashbuckle.AspNetCore.SwaggerUI/9.0.3": {"type": "package", "compile": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "System.ClientModel/1.5.1": {"type": "package", "dependencies": {"System.Memory.Data": "8.0.1"}, "compile": {"lib/net8.0/System.ClientModel.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.ClientModel.dll": {"related": ".xml"}}}, "System.CodeDom/9.0.8": {"type": "package", "compile": {"lib/net9.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.CodeDom.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Configuration.ConfigurationManager/9.0.8": {"type": "package", "dependencies": {"System.Security.Cryptography.ProtectedData": "9.0.8"}, "compile": {"lib/net9.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Data.Odbc/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Data.Odbc.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Data.Odbc.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/freebsd/lib/net8.0/System.Data.Odbc.dll": {"assetType": "runtime", "rid": "freebsd"}, "runtimes/illumos/lib/net8.0/System.Data.Odbc.dll": {"assetType": "runtime", "rid": "illumos"}, "runtimes/ios/lib/net8.0/System.Data.Odbc.dll": {"assetType": "runtime", "rid": "ios"}, "runtimes/linux/lib/net8.0/System.Data.Odbc.dll": {"assetType": "runtime", "rid": "linux"}, "runtimes/osx/lib/net8.0/System.Data.Odbc.dll": {"assetType": "runtime", "rid": "osx"}, "runtimes/solaris/lib/net8.0/System.Data.Odbc.dll": {"assetType": "runtime", "rid": "solaris"}, "runtimes/tvos/lib/net8.0/System.Data.Odbc.dll": {"assetType": "runtime", "rid": "tvos"}, "runtimes/win/lib/net8.0/System.Data.Odbc.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Data.OleDb/6.0.0": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "6.0.0", "System.Diagnostics.PerformanceCounter": "6.0.0"}, "compile": {"lib/net6.0/System.Data.OleDb.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Data.OleDb.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Data.OleDb.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Data.SQLite.Core/1.0.119": {"type": "package", "dependencies": {"Stub.System.Data.SQLite.Core.NetStandard": "[1.0.119]"}}, "System.Diagnostics.EventLog/10.0.0-preview.7.25380.108": {"type": "package", "compile": {"lib/net10.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net10.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net10.0/System.Diagnostics.EventLog.Messages.dll": {"assetType": "runtime", "rid": "win"}, "runtimes/win/lib/net10.0/System.Diagnostics.EventLog.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Diagnostics.PerformanceCounter/9.0.8": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "9.0.8"}, "compile": {"lib/net9.0/System.Diagnostics.PerformanceCounter.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Diagnostics.PerformanceCounter.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Diagnostics.PerformanceCounter.dll": {"assetType": "runtime", "rid": "win"}}}, "System.DirectoryServices.Protocols/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.DirectoryServices.Protocols.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.DirectoryServices.Protocols.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/linux/lib/net8.0/System.DirectoryServices.Protocols.dll": {"assetType": "runtime", "rid": "linux"}, "runtimes/osx/lib/net8.0/System.DirectoryServices.Protocols.dll": {"assetType": "runtime", "rid": "osx"}, "runtimes/win/lib/net8.0/System.DirectoryServices.Protocols.dll": {"assetType": "runtime", "rid": "win"}}}, "System.IdentityModel.Tokens.Jwt/8.0.1": {"type": "package", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.0.1", "Microsoft.IdentityModel.Tokens": "8.0.1"}, "compile": {"lib/net9.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}}, "System.Management/9.0.8": {"type": "package", "dependencies": {"System.CodeDom": "9.0.8"}, "compile": {"lib/net9.0/System.Management.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Management.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Management.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Memory.Data/8.0.1": {"type": "package", "compile": {"lib/net8.0/System.Memory.Data.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Memory.Data.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Security.Cryptography.Pkcs/9.0.4": {"type": "package", "compile": {"lib/net9.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Security.Cryptography.Pkcs.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.ProtectedData/9.0.8": {"type": "package", "compile": {"lib/net9.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Security.Permissions/8.0.0": {"type": "package", "dependencies": {"System.Windows.Extensions": "8.0.0"}, "compile": {"lib/net8.0/System.Security.Permissions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Security.Permissions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Windows.Extensions/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Windows.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Windows.Extensions.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Windows.Extensions.dll": {"assetType": "runtime", "rid": "win"}}}, "WorkQueue/1.3.0": {"type": "package", "compile": {"lib/netstandard2.0/WorkQueue.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/WorkQueue.dll": {"related": ".xml"}}}, "ZstdSharp.Port/0.8.5": {"type": "package", "compile": {"lib/net9.0/ZstdSharp.dll": {}}, "runtime": {"lib/net9.0/ZstdSharp.dll": {}}}, "SSIC.Entity/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v10.0", "dependencies": {"FreeSql": "3.5.213-preview20250815", "Microsoft.DependencyValidation.Analyzers": "0.11.0"}, "compile": {"bin/placeholder/SSIC.Entity.dll": {}}, "runtime": {"bin/placeholder/SSIC.Entity.dll": {}}}, "SSIC.Infrastructure/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v10.0", "dependencies": {"Dapr.AspNetCore": "1.16.0-rc13", "FreeRedis": "1.4.0", "FreeScheduler": "2.0.36", "FreeSql.All": "3.5.213-preview20250815", "FreeSql.Cloud": "2.0.1", "Mapster": "7.4.2-pre02", "Masa.Contrib.Development.DaprStarter.AspNetCore": "1.2.0-preview.10", "Microsoft.AspNetCore.Authentication.JwtBearer": "10.0.0-preview.7.25380.108", "Microsoft.AspNetCore.OpenApi": "9.0.1", "Microsoft.Data.SqlClient": "6.1.1", "Microsoft.DependencyValidation.Analyzers": "0.11.0", "Microsoft.Extensions.Configuration": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.Json": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.DependencyInjection": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Hosting": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Http.Resilience": "9.8.0", "Microsoft.Extensions.ServiceDiscovery": "9.4.1", "Microsoft.OpenApi": "1.6.24", "MiniProfiler.AspNetCore": "4.5.4", "MiniProfiler.AspNetCore.Mvc": "4.5.4", "Npgsql": "9.0.3", "OpenTelemetry": "1.12.0", "OpenTelemetry.Exporter.OpenTelemetryProtocol": "1.12.0", "OpenTelemetry.Exporter.Zipkin": "1.12.0", "OpenTelemetry.Extensions.Hosting": "1.12.0", "OpenTelemetry.Instrumentation.AspNetCore": "1.12.0", "OpenTelemetry.Instrumentation.GrpcCore": "1.0.0-beta.6", "OpenTelemetry.Instrumentation.GrpcNetClient": "1.12.0-beta.1", "OpenTelemetry.Instrumentation.Http": "1.12.0", "OpenTelemetry.Instrumentation.Process": "1.12.0-beta.1", "OpenTelemetry.Instrumentation.Runtime": "1.12.0", "SSIC.Entity": "1.0.0", "Scalar.AspNetCore": "2.6.9", "Serilog": "4.3.1-dev-02373", "Serilog.AspNetCore": "9.0.0", "Serilog.Enrichers.Environment": "3.0.1", "Serilog.Enrichers.Thread": "4.0.0", "Serilog.Expressions": "5.1.0-dev-02301", "Serilog.Extensions.Hosting": "9.0.1-dev-02307", "Serilog.Extensions.Logging": "9.0.3-dev-02320", "Serilog.Settings.Configuration": "9.0.1-dev-02317", "Serilog.Sinks.Console": "6.0.1-dev-00953", "Serilog.Sinks.OpenTelemetry": "4.2.1-dev-02306", "Serilog.Sinks.Seq": "9.0.0", "SixLabors.ImageSharp": "3.1.11", "Swashbuckle.AspNetCore": "9.0.3"}, "compile": {"bin/placeholder/SSIC.Infrastructure.dll": {}}, "runtime": {"bin/placeholder/SSIC.Infrastructure.dll": {}}}, "SSIC.Utilities/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v10.0", "dependencies": {"Masuit.Tools.Core": "2025.5.0", "Microsoft.DependencyValidation.Analyzers": "0.11.0", "QRCoder": "1.6.0"}, "compile": {"bin/placeholder/SSIC.Utilities.dll": {}}, "runtime": {"bin/placeholder/SSIC.Utilities.dll": {}}}}}, "libraries": {"AngleSharp/1.3.0": {"sha512": "iHzfn4cK6CmhuURNdEpmSQCq5/HZFldEpkbnmqT9My8+6l2Sz3F+NxoqRA8z/jTkWB+SAu5boRdp4v/WtyjuIQ==", "type": "package", "path": "anglesharp/1.3.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "anglesharp.1.3.0.nupkg.sha512", "anglesharp.nuspec", "lib/net462/AngleSharp.dll", "lib/net462/AngleSharp.xml", "lib/net472/AngleSharp.dll", "lib/net472/AngleSharp.xml", "lib/net8.0/AngleSharp.dll", "lib/net8.0/AngleSharp.xml", "lib/netstandard2.0/AngleSharp.dll", "lib/netstandard2.0/AngleSharp.xml", "logo.png"]}, "AngleSharp.Css/1.0.0-beta.151": {"sha512": "oEnqXQcwpc/kkUIi2rxWHfFrmKlcJFpZZZjhEIHVc+aEJFo3U+5cptOHByQh+FW0PCW6ssVJ+GGBgbgyN8YPiw==", "type": "package", "path": "anglesharp.css/1.0.0-beta.151", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "anglesharp.css.1.0.0-beta.151.nupkg.sha512", "anglesharp.css.nuspec", "lib/net461/AngleSharp.Css.dll", "lib/net461/AngleSharp.Css.xml", "lib/net472/AngleSharp.Css.dll", "lib/net472/AngleSharp.Css.xml", "lib/net6.0/AngleSharp.Css.dll", "lib/net6.0/AngleSharp.Css.xml", "lib/net7.0/AngleSharp.Css.dll", "lib/net7.0/AngleSharp.Css.xml", "lib/net8.0/AngleSharp.Css.dll", "lib/net8.0/AngleSharp.Css.xml", "lib/netstandard2.0/AngleSharp.Css.dll", "lib/netstandard2.0/AngleSharp.Css.xml", "logo.png"]}, "Azure.Core/1.47.1": {"sha512": "oPcncSsDHuxB8SC522z47xbp2+ttkcKv2YZ90KXhRKN0YQd2+7l1UURT9EBzUNEXtkLZUOAB5xbByMTrYRh3yA==", "type": "package", "path": "azure.core/1.47.1", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.core.1.47.1.nupkg.sha512", "azure.core.nuspec", "azureicon.png", "lib/net462/Azure.Core.dll", "lib/net462/Azure.Core.xml", "lib/net472/Azure.Core.dll", "lib/net472/Azure.Core.xml", "lib/net8.0/Azure.Core.dll", "lib/net8.0/Azure.Core.xml", "lib/netstandard2.0/Azure.Core.dll", "lib/netstandard2.0/Azure.Core.xml"]}, "Azure.Identity/1.14.2": {"sha512": "YhNMwOTwT+I2wIcJKSdP0ADyB2aK+JaYWZxO8LSRDm5w77LFr0ykR9xmt2ZV5T1gaI7xU6iNFIh/yW1dAlpddQ==", "type": "package", "path": "azure.identity/1.14.2", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.identity.1.14.2.nupkg.sha512", "azure.identity.nuspec", "azureicon.png", "lib/net8.0/Azure.Identity.dll", "lib/net8.0/Azure.Identity.xml", "lib/netstandard2.0/Azure.Identity.dll", "lib/netstandard2.0/Azure.Identity.xml"]}, "BouncyCastle.Cryptography/2.3.1": {"sha512": "buwoISwecYke3CmgG1AQSg+sNZjJeIb93vTAtJiHZX35hP/teYMxsfg0NDXGUKjGx6BKBTNKc77O2M3vKvlXZQ==", "type": "package", "path": "bouncycastle.cryptography/2.3.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "bouncycastle.cryptography.2.3.1.nupkg.sha512", "bouncycastle.cryptography.nuspec", "lib/net461/BouncyCastle.Cryptography.dll", "lib/net461/BouncyCastle.Cryptography.xml", "lib/net6.0/BouncyCastle.Cryptography.dll", "lib/net6.0/BouncyCastle.Cryptography.xml", "lib/netstandard2.0/BouncyCastle.Cryptography.dll", "lib/netstandard2.0/BouncyCastle.Cryptography.xml", "packageIcon.png"]}, "Castle.Core/5.2.1": {"sha512": "wHARzQA695jwwKreOzNsq54KiGqKP38tv8hi8e2FXDEC/sA6BtrX90tVPDkOfVu13PbEzr00TCV8coikl+D1Iw==", "type": "package", "path": "castle.core/5.2.1", "files": [".nupkg.metadata", ".signature.p7s", "ASL - Apache Software Foundation License.txt", "CHANGELOG.md", "LICENSE", "castle-logo.png", "castle.core.5.2.1.nupkg.sha512", "castle.core.nuspec", "lib/net462/Castle.Core.dll", "lib/net462/Castle.Core.xml", "lib/net6.0/Castle.Core.dll", "lib/net6.0/Castle.Core.xml", "lib/netstandard2.0/Castle.Core.dll", "lib/netstandard2.0/Castle.Core.xml", "lib/netstandard2.1/Castle.Core.dll", "lib/netstandard2.1/Castle.Core.xml", "readme.txt"]}, "Dapr.AspNetCore/1.16.0-rc13": {"sha512": "BaHIUgBlwwSDYoTgDg+i0forhUaqIUgcqAvVFK0UiD7IRqnbSW9Fb7At81Q2K3cEjX+xdPUG62rdTLDKOQU5bA==", "type": "package", "path": "dapr.aspnetcore/1.16.0-rc13", "files": [".nupkg.metadata", ".signature.p7s", "dapr.aspnetcore.1.16.0-rc13.nupkg.sha512", "dapr.aspnetcore.nuspec", "images/logo-transparent.png", "lib/net8.0/Dapr.AspNetCore.dll", "lib/net8.0/Dapr.AspNetCore.xml", "lib/net9.0/Dapr.AspNetCore.dll", "lib/net9.0/Dapr.AspNetCore.xml"]}, "Dapr.Client/1.16.0-rc13": {"sha512": "B/SepgLIhnvbb1VNyrLr6aaLbtgVmVCCcvOJj6CYaEAhG7u4+VHN6YQ6b8Skxtmx+wZwtu8OUodn2L8XWz0XKg==", "type": "package", "path": "dapr.client/1.16.0-rc13", "files": [".nupkg.metadata", ".signature.p7s", "dapr.client.1.16.0-rc13.nupkg.sha512", "dapr.client.nuspec", "images/logo-transparent.png", "lib/net8.0/Dapr.Client.dll", "lib/net8.0/Dapr.Client.xml", "lib/net9.0/Dapr.Client.dll", "lib/net9.0/Dapr.Client.xml"]}, "Dapr.Common/1.16.0-rc13": {"sha512": "hcvop0/wCNJzkcJyoMngplKx0htlf6ZwZLBWZB/XE3glkFq5mDB0a1XLt+ARifhH0r4CqwyBJBiGL5ahIDbK6Q==", "type": "package", "path": "dapr.common/1.16.0-rc13", "files": [".nupkg.metadata", ".signature.p7s", "dapr.common.1.16.0-rc13.nupkg.sha512", "dapr.common.nuspec", "images/logo-transparent.png", "lib/net8.0/Dapr.Common.dll", "lib/net8.0/Dapr.Common.xml", "lib/net9.0/Dapr.Common.dll", "lib/net9.0/Dapr.Common.xml"]}, "Dapr.Protos/1.16.0-rc13": {"sha512": "jIbSQLuQoJhwVsM6jzHtj7DFsuj+jUOXRU4MYg/Qu5GF2eGdoVnBZQI88NhJTk4yCFqOGTjKoCKGKyHFhKn0aQ==", "type": "package", "path": "dapr.protos/1.16.0-rc13", "files": [".nupkg.metadata", ".signature.p7s", "dapr.protos.1.16.0-rc13.nupkg.sha512", "dapr.protos.nuspec", "images/logo-transparent.png", "lib/net8.0/Dapr.Protos.dll", "lib/net8.0/Dapr.Protos.xml", "lib/net9.0/Dapr.Protos.dll", "lib/net9.0/Dapr.Protos.xml"]}, "DM.DmProvider/8.3.1.28188": {"sha512": "yd8bw6ClaoP5vAAPQ6BHiRqFqB41lKXyvzgKu05YfFQkiDzK/RQQBj7jmgWkeTFt1cxJvebHv//xBGfnHa2gnw==", "type": "package", "path": "dm.d<PERSON><PERSON><PERSON>/8.3.1.28188", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "dm.dmprovider.8.3.1.28188.nupkg.sha512", "dm.dmprovider.nuspec", "icon.png", "lib/net40/DM.DmProvider.dll", "lib/net40/en/DM.DmProvider.resources.dll", "lib/net40/zh-CN/DM.DmProvider.resources.dll", "lib/net40/zh-HK/DM.DmProvider.resources.dll", "lib/net40/zh-TW/DM.DmProvider.resources.dll", "lib/net45/DM.DmProvider.dll", "lib/net45/en/DM.DmProvider.resources.dll", "lib/net45/zh-CN/DM.DmProvider.resources.dll", "lib/net45/zh-HK/DM.DmProvider.resources.dll", "lib/net45/zh-TW/DM.DmProvider.resources.dll", "lib/net5.0/DM.DmProvider.dll", "lib/net5.0/en/DM.DmProvider.resources.dll", "lib/net5.0/zh-CN/DM.DmProvider.resources.dll", "lib/net5.0/zh-HK/DM.DmProvider.resources.dll", "lib/net5.0/zh-TW/DM.DmProvider.resources.dll", "lib/net6.0/DM.DmProvider.dll", "lib/net6.0/en/DM.DmProvider.resources.dll", "lib/net6.0/zh-CN/DM.DmProvider.resources.dll", "lib/net6.0/zh-HK/DM.DmProvider.resources.dll", "lib/net6.0/zh-TW/DM.DmProvider.resources.dll", "lib/net7.0/DM.DmProvider.dll", "lib/net7.0/en/DM.DmProvider.resources.dll", "lib/net7.0/zh-CN/DM.DmProvider.resources.dll", "lib/net7.0/zh-HK/DM.DmProvider.resources.dll", "lib/net7.0/zh-TW/DM.DmProvider.resources.dll", "lib/net8.0/DM.DmProvider.dll", "lib/net8.0/en/DM.DmProvider.resources.dll", "lib/net8.0/zh-CN/DM.DmProvider.resources.dll", "lib/net8.0/zh-HK/DM.DmProvider.resources.dll", "lib/net8.0/zh-TW/DM.DmProvider.resources.dll", "lib/netcoreapp2.1/DM.DmProvider.dll", "lib/netcoreapp2.1/en/DM.DmProvider.resources.dll", "lib/netcoreapp2.1/zh-CN/DM.DmProvider.resources.dll", "lib/netcoreapp2.1/zh-HK/DM.DmProvider.resources.dll", "lib/netcoreapp2.1/zh-TW/DM.DmProvider.resources.dll", "lib/netcoreapp3.1/DM.DmProvider.dll", "lib/netcoreapp3.1/en/DM.DmProvider.resources.dll", "lib/netcoreapp3.1/zh-CN/DM.DmProvider.resources.dll", "lib/netcoreapp3.1/zh-HK/DM.DmProvider.resources.dll", "lib/netcoreapp3.1/zh-TW/DM.DmProvider.resources.dll", "lib/netstandard2.0/DM.DmProvider.dll", "lib/netstandard2.0/en/DM.DmProvider.resources.dll", "lib/netstandard2.0/zh-CN/DM.DmProvider.resources.dll", "lib/netstandard2.0/zh-HK/DM.DmProvider.resources.dll", "lib/netstandard2.0/zh-TW/DM.DmProvider.resources.dll"]}, "DnsClient/1.8.0": {"sha512": "RRwtaCXkXWsx0mmsReGDqCbRLtItfUbkRJlet1FpdciVhyMGKcPd57T1+8Jki9ojHlq9fntVhXQroOOgRak8DQ==", "type": "package", "path": "dnsclient/1.8.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "dnsclient.1.8.0.nupkg.sha512", "dnsclient.nuspec", "icon.png", "lib/net472/DnsClient.dll", "lib/net472/DnsClient.xml", "lib/net6.0/DnsClient.dll", "lib/net6.0/DnsClient.xml", "lib/net8.0/DnsClient.dll", "lib/net8.0/DnsClient.xml", "lib/netstandard2.0/DnsClient.dll", "lib/netstandard2.0/DnsClient.xml", "lib/netstandard2.1/DnsClient.dll", "lib/netstandard2.1/DnsClient.xml"]}, "FreeRedis/1.4.0": {"sha512": "E74KyJwkyjh+T8HgbvP5wlRFXRW11bn7BXms5eeh5l6HRGhRP0+Vitl8NtSsqjYSTgylPow7PeRP7rIpcDHplQ==", "type": "package", "path": "freeredis/1.4.0", "files": [".nupkg.metadata", ".signature.p7s", "freeredis.1.4.0.nupkg.sha512", "freeredis.nuspec", "lib/net40/FreeRedis.dll", "lib/net40/FreeRedis.xml", "lib/net451/FreeRedis.dll", "lib/net451/FreeRedis.xml", "lib/netstandard2.0/FreeRedis.dll", "lib/netstandard2.0/FreeRedis.xml", "readme.md"]}, "FreeScheduler/2.0.36": {"sha512": "nGbiHwA2EwRiB9xmwxiHZROyTyqEh9SNkdlNZWKpoOKrPXu6jkie6/2c6Rn2xQckIt489ODoQwHukmBYKUJzjg==", "type": "package", "path": "freescheduler/2.0.36", "files": [".nupkg.metadata", ".signature.p7s", "freescheduler.2.0.36.nupkg.sha512", "freescheduler.nuspec", "lib/net40/FreeScheduler.dll", "lib/net5.0/FreeScheduler.dll", "lib/net6.0/FreeScheduler.dll", "lib/net7.0/FreeScheduler.dll", "lib/net8.0/FreeScheduler.dll", "lib/netcoreapp3.1/FreeScheduler.dll", "lib/netstandard2.0/FreeScheduler.dll", "lib/netstandard2.0/FreeScheduler.xml", "readme.md"]}, "FreeSql/3.5.213-preview20250815": {"sha512": "3w+ngNJypO1smTww/JIDtC2qlEcdLJ/nYGwvsCtR5hYonTuektKQCms9uXPcYt7to2B0zPbubKqicYsclyzUtA==", "type": "package", "path": "freesql/3.5.213-preview20250815", "files": [".nupkg.metadata", ".signature.p7s", "freesql.3.5.213-preview20250815.nupkg.sha512", "freesql.nuspec", "lib/net40/FreeSql.dll", "lib/net40/FreeSql.pdb", "lib/net40/FreeSql.xml", "lib/net45/FreeSql.dll", "lib/net45/FreeSql.pdb", "lib/net45/FreeSql.xml", "lib/net451/FreeSql.dll", "lib/net451/FreeSql.pdb", "lib/net451/FreeSql.xml", "lib/netstandard2.0/FreeSql.dll", "lib/netstandard2.0/FreeSql.pdb", "lib/netstandard2.0/FreeSql.xml", "lib/netstandard2.1/FreeSql.dll", "lib/netstandard2.1/FreeSql.pdb", "lib/netstandard2.1/FreeSql.xml", "logo.png", "readme.md"]}, "FreeSql.All/3.5.213-preview20250815": {"sha512": "bIKNKwyqmZCBN+7Gen7NU0AWcn04vt3OYmNQS5J0EFpTgbTP5tkSrlOTSqdaSDkc3jOfffIo53xzNVJc2T0t4A==", "type": "package", "path": "freesql.all/3.5.213-preview20250815", "files": [".nupkg.metadata", ".signature.p7s", "freesql.all.3.5.213-preview20250815.nupkg.sha512", "freesql.all.nuspec", "lib/net40/FreeSql.All.dll", "lib/net40/FreeSql.All.pdb", "lib/net45/FreeSql.All.dll", "lib/net45/FreeSql.All.pdb", "lib/netstandard2.0/FreeSql.All.dll", "lib/netstandard2.0/FreeSql.All.pdb", "lib/netstandard2.1/FreeSql.All.dll", "lib/netstandard2.1/FreeSql.All.pdb", "logo.png", "readme.md"]}, "FreeSql.Cloud/2.0.1": {"sha512": "Mof1EcdfQLPGnerhbDaDiWQ1maRBSXC3Pm0DvGxsPvqpMm6Zxeh1Lgvh8+ubBjFzXLZATgqgb1c5S+VX1QOjBA==", "type": "package", "path": "freesql.cloud/2.0.1", "files": [".nupkg.metadata", ".signature.p7s", "freesql.cloud.2.0.1.nupkg.sha512", "freesql.cloud.nuspec", "lib/net40/FreeSql.Cloud.dll", "lib/net461/FreeSql.Cloud.dll", "lib/netstandard2.0/FreeSql.Cloud.dll", "readme.md"]}, "FreeSql.DbContext/3.5.213-preview20250815": {"sha512": "lLJqnMpjfknY0pbDCs4XUL/xHIdQTtdtbNoGH84lMd472im0EjsG0ehAQmIk2LiRefWwMp9U8zk0buv7YgcnEQ==", "type": "package", "path": "freesql.dbcontext/3.5.213-preview20250815", "files": [".nupkg.metadata", ".signature.p7s", "freesql.dbcontext.3.5.213-preview20250815.nupkg.sha512", "freesql.dbcontext.nuspec", "lib/net40/FreeSql.DbContext.dll", "lib/net40/FreeSql.DbContext.pdb", "lib/net40/FreeSql.DbContext.xml", "lib/net45/FreeSql.DbContext.dll", "lib/net45/FreeSql.DbContext.pdb", "lib/net45/FreeSql.DbContext.xml", "lib/net5.0/FreeSql.DbContext.dll", "lib/net5.0/FreeSql.DbContext.pdb", "lib/net5.0/FreeSql.DbContext.xml", "lib/net6.0/FreeSql.DbContext.dll", "lib/net6.0/FreeSql.DbContext.pdb", "lib/net6.0/FreeSql.DbContext.xml", "lib/net7.0/FreeSql.DbContext.dll", "lib/net7.0/FreeSql.DbContext.pdb", "lib/net7.0/FreeSql.DbContext.xml", "lib/net8.0/FreeSql.DbContext.dll", "lib/net8.0/FreeSql.DbContext.pdb", "lib/net8.0/FreeSql.DbContext.xml", "lib/net9.0/FreeSql.DbContext.dll", "lib/net9.0/FreeSql.DbContext.pdb", "lib/net9.0/FreeSql.DbContext.xml", "lib/netcoreapp3.1/FreeSql.DbContext.dll", "lib/netcoreapp3.1/FreeSql.DbContext.pdb", "lib/netcoreapp3.1/FreeSql.DbContext.xml", "lib/netstandard2.0/FreeSql.DbContext.dll", "lib/netstandard2.0/FreeSql.DbContext.pdb", "lib/netstandard2.0/FreeSql.DbContext.xml", "lib/netstandard2.1/FreeSql.DbContext.dll", "lib/netstandard2.1/FreeSql.DbContext.pdb", "lib/netstandard2.1/FreeSql.DbContext.xml", "logo.png", "readme.md"]}, "FreeSql.Provider.Dameng/3.5.213-preview20250815": {"sha512": "CcYGxHoBdsw7u5X4x377TKngFK6YulRcyWMTwAcW9Tv+whhQcVaHEIZKJJ6zazUCDs5GtKHKiQATvALpIXOkkw==", "type": "package", "path": "freesql.provider.dameng/3.5.213-preview20250815", "files": [".nupkg.metadata", ".signature.p7s", "freesql.provider.dameng.3.5.213-preview20250815.nupkg.sha512", "freesql.provider.dameng.nuspec", "lib/net40/FreeSql.Provider.Dameng.dll", "lib/net40/FreeSql.Provider.Dameng.pdb", "lib/net45/FreeSql.Provider.Dameng.dll", "lib/net45/FreeSql.Provider.Dameng.pdb", "lib/net6.0/FreeSql.Provider.Dameng.dll", "lib/net6.0/FreeSql.Provider.Dameng.pdb", "lib/netcoreapp3.1/FreeSql.Provider.Dameng.dll", "lib/netcoreapp3.1/FreeSql.Provider.Dameng.pdb", "lib/netstandard2.0/FreeSql.Provider.Dameng.dll", "lib/netstandard2.0/FreeSql.Provider.Dameng.pdb", "logo.png", "readme.md"]}, "FreeSql.Provider.MsAccess/3.5.213-preview20250815": {"sha512": "7W40a2CQ75h2a86G3J4J6koiZQd8i21FBNj9LHO4U3A+U+c91jjAA6U3vIGFto48DvihCvucESPGHjI5umYQxg==", "type": "package", "path": "freesql.provider.msaccess/3.5.213-preview20250815", "files": [".nupkg.metadata", ".signature.p7s", "freesql.provider.msaccess.3.5.213-preview20250815.nupkg.sha512", "freesql.provider.msaccess.nuspec", "lib/net40/FreeSql.Provider.MsAccess.dll", "lib/net40/FreeSql.Provider.MsAccess.pdb", "lib/net45/FreeSql.Provider.MsAccess.dll", "lib/net45/FreeSql.Provider.MsAccess.pdb", "lib/netstandard2.0/FreeSql.Provider.MsAccess.dll", "lib/netstandard2.0/FreeSql.Provider.MsAccess.pdb", "logo.png", "readme.md"]}, "FreeSql.Provider.MySql/3.5.213-preview20250815": {"sha512": "H2erqJ3XkZHk2cfPhWi3rGg0HMJpM8Cw3tmGwz+KICjMfO8BnvGTAgcZEDOQPlTvkvuN8miM/EQ7yOl/m0iqMw==", "type": "package", "path": "freesql.provider.mysql/3.5.213-preview20250815", "files": [".nupkg.metadata", ".signature.p7s", "freesql.provider.mysql.3.5.213-preview20250815.nupkg.sha512", "freesql.provider.mysql.nuspec", "lib/net40/FreeSql.Provider.MySql.dll", "lib/net40/FreeSql.Provider.MySql.pdb", "lib/net45/FreeSql.Provider.MySql.dll", "lib/net45/FreeSql.Provider.MySql.pdb", "lib/net451/FreeSql.Provider.MySql.dll", "lib/net451/FreeSql.Provider.MySql.pdb", "lib/net452/FreeSql.Provider.MySql.dll", "lib/net452/FreeSql.Provider.MySql.pdb", "lib/net6.0/FreeSql.Provider.MySql.dll", "lib/net6.0/FreeSql.Provider.MySql.pdb", "lib/net7.0/FreeSql.Provider.MySql.dll", "lib/net7.0/FreeSql.Provider.MySql.pdb", "lib/net8.0/FreeSql.Provider.MySql.dll", "lib/net8.0/FreeSql.Provider.MySql.pdb", "lib/net9.0/FreeSql.Provider.MySql.dll", "lib/net9.0/FreeSql.Provider.MySql.pdb", "lib/netstandard2.0/FreeSql.Provider.MySql.dll", "lib/netstandard2.0/FreeSql.Provider.MySql.pdb", "lib/netstandard2.1/FreeSql.Provider.MySql.dll", "lib/netstandard2.1/FreeSql.Provider.MySql.pdb", "logo.png", "readme.md"]}, "FreeSql.Provider.Odbc/3.5.213-preview20250815": {"sha512": "eKoK7D4Z3btug70g+lp8i93i1m05IHKu9Yvl9Ay/Vd+td/d/LlNDhThdvv4tTyFcJUPfOUnblTS3Hdaj8D9aXg==", "type": "package", "path": "freesql.provider.odbc/3.5.213-preview20250815", "files": [".nupkg.metadata", ".signature.p7s", "freesql.provider.odbc.3.5.213-preview20250815.nupkg.sha512", "freesql.provider.odbc.nuspec", "lib/net40/FreeSql.Provider.Odbc.dll", "lib/net40/FreeSql.Provider.Odbc.pdb", "lib/net45/FreeSql.Provider.Odbc.dll", "lib/net45/FreeSql.Provider.Odbc.pdb", "lib/netstandard2.0/FreeSql.Provider.Odbc.dll", "lib/netstandard2.0/FreeSql.Provider.Odbc.pdb", "logo.png", "readme.md"]}, "FreeSql.Provider.Oracle/3.5.213-preview20250815": {"sha512": "iflMJRaUcUuoV1r4Uj0gfnKFX86R5yCHlXpt/ynOKFtTQ1hOz4QZyiDr1DX0k3Tk3BwxWG5A2/ZG3FZCLCpKrA==", "type": "package", "path": "freesql.provider.oracle/3.5.213-preview20250815", "files": [".nupkg.metadata", ".signature.p7s", "freesql.provider.oracle.3.5.213-preview20250815.nupkg.sha512", "freesql.provider.oracle.nuspec", "lib/net40/FreeSql.Provider.Oracle.dll", "lib/net40/FreeSql.Provider.Oracle.pdb", "lib/net45/FreeSql.Provider.Oracle.dll", "lib/net45/FreeSql.Provider.Oracle.pdb", "lib/net5.0/FreeSql.Provider.Oracle.dll", "lib/net5.0/FreeSql.Provider.Oracle.pdb", "lib/net6.0/FreeSql.Provider.Oracle.dll", "lib/net6.0/FreeSql.Provider.Oracle.pdb", "lib/net7.0/FreeSql.Provider.Oracle.dll", "lib/net7.0/FreeSql.Provider.Oracle.pdb", "lib/net8.0/FreeSql.Provider.Oracle.dll", "lib/net8.0/FreeSql.Provider.Oracle.pdb", "lib/net9.0/FreeSql.Provider.Oracle.dll", "lib/net9.0/FreeSql.Provider.Oracle.pdb", "lib/netcoreapp3.1/FreeSql.Provider.Oracle.dll", "lib/netcoreapp3.1/FreeSql.Provider.Oracle.pdb", "lib/netstandard2.1/FreeSql.Provider.Oracle.dll", "lib/netstandard2.1/FreeSql.Provider.Oracle.pdb", "logo.png", "readme.md"]}, "FreeSql.Provider.PostgreSQL/3.5.213-preview20250815": {"sha512": "XFKhZcW10lCRCwArtlPRJqKOtefdtQHUOmOXS+QuL0cPfzIXbPXaSMjNTEz7DGvqFRATa/dVEuzr8fYkTNPYHA==", "type": "package", "path": "freesql.provider.postgresql/3.5.213-preview20250815", "files": [".nupkg.metadata", ".signature.p7s", "freesql.provider.postgresql.3.5.213-preview20250815.nupkg.sha512", "freesql.provider.postgresql.nuspec", "lib/net45/FreeSql.Provider.PostgreSQL.dll", "lib/net45/FreeSql.Provider.PostgreSQL.pdb", "lib/net451/FreeSql.Provider.PostgreSQL.dll", "lib/net451/FreeSql.Provider.PostgreSQL.pdb", "lib/net452/FreeSql.Provider.PostgreSQL.dll", "lib/net452/FreeSql.Provider.PostgreSQL.pdb", "lib/net461/FreeSql.Provider.PostgreSQL.dll", "lib/net461/FreeSql.Provider.PostgreSQL.pdb", "lib/net6.0/FreeSql.Provider.PostgreSQL.dll", "lib/net6.0/FreeSql.Provider.PostgreSQL.pdb", "lib/net7.0/FreeSql.Provider.PostgreSQL.dll", "lib/net7.0/FreeSql.Provider.PostgreSQL.pdb", "lib/net8.0/FreeSql.Provider.PostgreSQL.dll", "lib/net8.0/FreeSql.Provider.PostgreSQL.pdb", "lib/net9.0/FreeSql.Provider.PostgreSQL.dll", "lib/net9.0/FreeSql.Provider.PostgreSQL.pdb", "lib/netstandard2.0/FreeSql.Provider.PostgreSQL.dll", "lib/netstandard2.0/FreeSql.Provider.PostgreSQL.pdb", "logo.png", "readme.md"]}, "FreeSql.Provider.Sqlite/3.5.213-preview20250815": {"sha512": "SvJOGiswHlnyRFtlQ7HI1K9NJOuKb1oBq13RB74a0BuwXcYp3ufhkqdRLuel9lAj1PHRu4/EGuJZvy353VbgKQ==", "type": "package", "path": "freesql.provider.sqlite/3.5.213-preview20250815", "files": [".nupkg.metadata", ".signature.p7s", "freesql.provider.sqlite.3.5.213-preview20250815.nupkg.sha512", "freesql.provider.sqlite.nuspec", "lib/net40/FreeSql.Provider.Sqlite.dll", "lib/net40/FreeSql.Provider.Sqlite.pdb", "lib/net45/FreeSql.Provider.Sqlite.dll", "lib/net45/FreeSql.Provider.Sqlite.pdb", "lib/netstandard2.0/FreeSql.Provider.Sqlite.dll", "lib/netstandard2.0/FreeSql.Provider.Sqlite.pdb", "logo.png", "readme.md"]}, "FreeSql.Provider.SqlServer/3.5.213-preview20250815": {"sha512": "ZIGHM6wBqsh81lw0h3wI8W/nvEnv66IuAB/5zjR3TM1YroOmjJHBVDHPahSBKeXzqlkiR8umYUIOoAHueXpUZg==", "type": "package", "path": "freesql.provider.sqlserver/3.5.213-preview20250815", "files": [".nupkg.metadata", ".signature.p7s", "freesql.provider.sqlserver.3.5.213-preview20250815.nupkg.sha512", "freesql.provider.sqlserver.nuspec", "lib/net40/FreeSql.Provider.SqlServer.dll", "lib/net40/FreeSql.Provider.SqlServer.pdb", "lib/net45/FreeSql.Provider.SqlServer.dll", "lib/net45/FreeSql.Provider.SqlServer.pdb", "lib/net451/FreeSql.Provider.SqlServer.dll", "lib/net451/FreeSql.Provider.SqlServer.pdb", "lib/net6.0/FreeSql.Provider.SqlServer.dll", "lib/net6.0/FreeSql.Provider.SqlServer.pdb", "lib/net7.0/FreeSql.Provider.SqlServer.dll", "lib/net7.0/FreeSql.Provider.SqlServer.pdb", "lib/net8.0/FreeSql.Provider.SqlServer.dll", "lib/net8.0/FreeSql.Provider.SqlServer.pdb", "lib/net9.0/FreeSql.Provider.SqlServer.dll", "lib/net9.0/FreeSql.Provider.SqlServer.pdb", "lib/netstandard2.0/FreeSql.Provider.SqlServer.dll", "lib/netstandard2.0/FreeSql.Provider.SqlServer.pdb", "lib/netstandard2.1/FreeSql.Provider.SqlServer.dll", "lib/netstandard2.1/FreeSql.Provider.SqlServer.pdb", "logo.png", "readme.md"]}, "FreeSql.Repository/3.5.213-preview20250815": {"sha512": "k4+u1taPKgZfoWPLyJbMsWXqziBQu2mlizMT5h7uXBKhZ0HQA7QviJolS+2BX52lsEy2aX1HfjyMjPYd1BpAYA==", "type": "package", "path": "freesql.repository/3.5.213-preview20250815", "files": [".nupkg.metadata", ".signature.p7s", "freesql.repository.3.5.213-preview20250815.nupkg.sha512", "freesql.repository.nuspec", "lib/net40/FreeSql.Repository.dll", "lib/net40/FreeSql.Repository.pdb", "lib/net45/FreeSql.Repository.dll", "lib/net45/FreeSql.Repository.pdb", "lib/net5.0/FreeSql.Repository.dll", "lib/net5.0/FreeSql.Repository.pdb", "lib/net6.0/FreeSql.Repository.dll", "lib/net6.0/FreeSql.Repository.pdb", "lib/net7.0/FreeSql.Repository.dll", "lib/net7.0/FreeSql.Repository.pdb", "lib/net8.0/FreeSql.Repository.dll", "lib/net8.0/FreeSql.Repository.pdb", "lib/net9.0/FreeSql.Repository.dll", "lib/net9.0/FreeSql.Repository.pdb", "lib/netcoreapp3.1/FreeSql.Repository.dll", "lib/netcoreapp3.1/FreeSql.Repository.pdb", "lib/netstandard2.0/FreeSql.Repository.dll", "lib/netstandard2.0/FreeSql.Repository.pdb", "lib/netstandard2.1/FreeSql.Repository.dll", "lib/netstandard2.1/FreeSql.Repository.pdb", "logo.png", "readme.md"]}, "Google.Api.CommonProtos/2.17.0": {"sha512": "elfQPknFr495hm7vdy6ZlgyQh6yzZq9TU7sS35L/Fj/fqjM/mUGau9gVJLhvQEtUlPjtR80hpn/m9HvBMyCXIw==", "type": "package", "path": "google.api.commonprotos/2.17.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "NuGetIcon.png", "build/Google.Api.CommonProtos.targets", "content/protos/google/api/annotations.proto", "content/protos/google/api/auth.proto", "content/protos/google/api/backend.proto", "content/protos/google/api/billing.proto", "content/protos/google/api/client.proto", "content/protos/google/api/config_change.proto", "content/protos/google/api/consumer.proto", "content/protos/google/api/context.proto", "content/protos/google/api/control.proto", "content/protos/google/api/distribution.proto", "content/protos/google/api/documentation.proto", "content/protos/google/api/endpoint.proto", "content/protos/google/api/error_reason.proto", "content/protos/google/api/field_behavior.proto", "content/protos/google/api/field_info.proto", "content/protos/google/api/http.proto", "content/protos/google/api/httpbody.proto", "content/protos/google/api/label.proto", "content/protos/google/api/launch_stage.proto", "content/protos/google/api/log.proto", "content/protos/google/api/logging.proto", "content/protos/google/api/metric.proto", "content/protos/google/api/monitored_resource.proto", "content/protos/google/api/monitoring.proto", "content/protos/google/api/policy.proto", "content/protos/google/api/quota.proto", "content/protos/google/api/resource.proto", "content/protos/google/api/routing.proto", "content/protos/google/api/service.proto", "content/protos/google/api/source_info.proto", "content/protos/google/api/system_parameter.proto", "content/protos/google/api/usage.proto", "content/protos/google/api/visibility.proto", "content/protos/google/rpc/code.proto", "content/protos/google/rpc/context/attribute_context.proto", "content/protos/google/rpc/context/audit_context.proto", "content/protos/google/rpc/error_details.proto", "content/protos/google/rpc/http.proto", "content/protos/google/rpc/status.proto", "content/protos/google/type/calendar_period.proto", "content/protos/google/type/color.proto", "content/protos/google/type/date.proto", "content/protos/google/type/datetime.proto", "content/protos/google/type/dayofweek.proto", "content/protos/google/type/decimal.proto", "content/protos/google/type/expr.proto", "content/protos/google/type/fraction.proto", "content/protos/google/type/interval.proto", "content/protos/google/type/latlng.proto", "content/protos/google/type/localized_text.proto", "content/protos/google/type/money.proto", "content/protos/google/type/month.proto", "content/protos/google/type/phone_number.proto", "content/protos/google/type/postal_address.proto", "content/protos/google/type/quaternion.proto", "content/protos/google/type/timeofday.proto", "google.api.commonprotos.2.17.0.nupkg.sha512", "google.api.commonprotos.nuspec", "lib/net461/Google.Api.CommonProtos.dll", "lib/net461/Google.Api.CommonProtos.pdb", "lib/net461/Google.Api.CommonProtos.xml", "lib/netstandard2.0/Google.Api.CommonProtos.dll", "lib/netstandard2.0/Google.Api.CommonProtos.pdb", "lib/netstandard2.0/Google.Api.CommonProtos.xml"]}, "Google.Protobuf/3.31.1": {"sha512": "gSnJbUmGiOTdWddPhqzrEscHq9Ls6sqRDPB9WptckyjTUyx70JOOAaDLkFff8gManZNN3hllQ4aQInnQyq/Z/A==", "type": "package", "path": "google.protobuf/3.31.1", "files": [".nupkg.metadata", ".signature.p7s", "google.protobuf.3.31.1.nupkg.sha512", "google.protobuf.nuspec", "lib/net45/Google.Protobuf.dll", "lib/net45/Google.Protobuf.pdb", "lib/net45/Google.Protobuf.xml", "lib/net5.0/Google.Protobuf.dll", "lib/net5.0/Google.Protobuf.pdb", "lib/net5.0/Google.Protobuf.xml", "lib/netstandard1.1/Google.Protobuf.dll", "lib/netstandard1.1/Google.Protobuf.pdb", "lib/netstandard1.1/Google.Protobuf.xml", "lib/netstandard2.0/Google.Protobuf.dll", "lib/netstandard2.0/Google.Protobuf.pdb", "lib/netstandard2.0/Google.Protobuf.xml"]}, "Grpc.Core.Api/2.71.0": {"sha512": "QquqUC37yxsDzd1QaDRsH2+uuznWPTS8CVE2Yzwl3CvU4geTNkolQXoVN812M2IwT6zpv3jsZRc9ExJFNFslTg==", "type": "package", "path": "grpc.core.api/2.71.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "grpc.core.api.2.71.0.nupkg.sha512", "grpc.core.api.nuspec", "lib/net462/Grpc.Core.Api.dll", "lib/net462/Grpc.Core.Api.pdb", "lib/net462/Grpc.Core.Api.xml", "lib/netstandard2.0/Grpc.Core.Api.dll", "lib/netstandard2.0/Grpc.Core.Api.pdb", "lib/netstandard2.0/Grpc.Core.Api.xml", "lib/netstandard2.1/Grpc.Core.Api.dll", "lib/netstandard2.1/Grpc.Core.Api.pdb", "lib/netstandard2.1/Grpc.Core.Api.xml", "packageIcon.png"]}, "Grpc.Net.Client/2.71.0": {"sha512": "U1vr20r5ngoT9nlb7wejF28EKN+taMhJsV9XtK9MkiepTZwnKxxiarriiMfCHuDAfPUm9XUjFMn/RIuJ4YY61w==", "type": "package", "path": "grpc.net.client/2.71.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "grpc.net.client.2.71.0.nupkg.sha512", "grpc.net.client.nuspec", "lib/net462/Grpc.Net.Client.dll", "lib/net462/Grpc.Net.Client.pdb", "lib/net462/Grpc.Net.Client.xml", "lib/net6.0/Grpc.Net.Client.dll", "lib/net6.0/Grpc.Net.Client.pdb", "lib/net6.0/Grpc.Net.Client.xml", "lib/net7.0/Grpc.Net.Client.dll", "lib/net7.0/Grpc.Net.Client.pdb", "lib/net7.0/Grpc.Net.Client.xml", "lib/net8.0/Grpc.Net.Client.dll", "lib/net8.0/Grpc.Net.Client.pdb", "lib/net8.0/Grpc.Net.Client.xml", "lib/netstandard2.0/Grpc.Net.Client.dll", "lib/netstandard2.0/Grpc.Net.Client.pdb", "lib/netstandard2.0/Grpc.Net.Client.xml", "lib/netstandard2.1/Grpc.Net.Client.dll", "lib/netstandard2.1/Grpc.Net.Client.pdb", "lib/netstandard2.1/Grpc.Net.Client.xml", "packageIcon.png"]}, "Grpc.Net.Common/2.71.0": {"sha512": "v0c8R97TwRYwNXlC8GyRXwYTCNufpDfUtj9la+wUrZFzVWkFJuNAltU+c0yI3zu0jl54k7en6u2WKgZgd57r2Q==", "type": "package", "path": "grpc.net.common/2.71.0", "files": [".nupkg.metadata", ".signature.p7s", "grpc.net.common.2.71.0.nupkg.sha512", "grpc.net.common.nuspec", "lib/net6.0/Grpc.Net.Common.dll", "lib/net6.0/Grpc.Net.Common.pdb", "lib/net6.0/Grpc.Net.Common.xml", "lib/net7.0/Grpc.Net.Common.dll", "lib/net7.0/Grpc.Net.Common.pdb", "lib/net7.0/Grpc.Net.Common.xml", "lib/net8.0/Grpc.Net.Common.dll", "lib/net8.0/Grpc.Net.Common.pdb", "lib/net8.0/Grpc.Net.Common.xml", "lib/netstandard2.0/Grpc.Net.Common.dll", "lib/netstandard2.0/Grpc.Net.Common.pdb", "lib/netstandard2.0/Grpc.Net.Common.xml", "lib/netstandard2.1/Grpc.Net.Common.dll", "lib/netstandard2.1/Grpc.Net.Common.pdb", "lib/netstandard2.1/Grpc.Net.Common.xml", "packageIcon.png"]}, "IdleBus/1.5.3": {"sha512": "jMXWcNXGsUrES2QYyrygKx4YFNsGX0NrYHcMVK5lNH4L8UB8RltLb/SXMPLjVzh+usXXRXcEJTqLNZgy+rq/xw==", "type": "package", "path": "idlebus/1.5.3", "files": [".nupkg.metadata", ".signature.p7s", "idlebus.1.5.3.nupkg.sha512", "idlebus.nuspec", "lib/net40/IdleBus.dll", "lib/netstandard2.0/IdleBus.dll", "lib/netstandard2.0/IdleBus.xml"]}, "K4os.Compression.LZ4/1.3.8": {"sha512": "LhwlPa7c1zs1OV2XadMtAWdImjLIsqFJPoRcIWAadSRn0Ri1DepK65UbWLPmt4riLqx2d40xjXRk0ogpqNtK7g==", "type": "package", "path": "k4os.compression.lz4/1.3.8", "files": [".nupkg.metadata", ".signature.p7s", "k4os.compression.lz4.1.3.8.nupkg.sha512", "k4os.compression.lz4.nuspec", "lib/net462/K4os.Compression.LZ4.dll", "lib/net462/K4os.Compression.LZ4.xml", "lib/net5.0/K4os.Compression.LZ4.dll", "lib/net5.0/K4os.Compression.LZ4.xml", "lib/net6.0/K4os.Compression.LZ4.dll", "lib/net6.0/K4os.Compression.LZ4.xml", "lib/netstandard2.0/K4os.Compression.LZ4.dll", "lib/netstandard2.0/K4os.Compression.LZ4.xml", "lib/netstandard2.1/K4os.Compression.LZ4.dll", "lib/netstandard2.1/K4os.Compression.LZ4.xml"]}, "K4os.Compression.LZ4.Streams/1.3.8": {"sha512": "P15qr8dZAeo9GvYbUIPEYFQ0MEJ0i5iqr37wsYeRC3la2uCldOoeCa6to0CZ1taiwxIV+Mk8NGuZi+4iWivK9w==", "type": "package", "path": "k4os.compression.lz4.streams/1.3.8", "files": [".nupkg.metadata", ".signature.p7s", "k4os.compression.lz4.streams.1.3.8.nupkg.sha512", "k4os.compression.lz4.streams.nuspec", "lib/net462/K4os.Compression.LZ4.Streams.dll", "lib/net462/K4os.Compression.LZ4.Streams.xml", "lib/net5.0/K4os.Compression.LZ4.Streams.dll", "lib/net5.0/K4os.Compression.LZ4.Streams.xml", "lib/net6.0/K4os.Compression.LZ4.Streams.dll", "lib/net6.0/K4os.Compression.LZ4.Streams.xml", "lib/netstandard2.0/K4os.Compression.LZ4.Streams.dll", "lib/netstandard2.0/K4os.Compression.LZ4.Streams.xml", "lib/netstandard2.1/K4os.Compression.LZ4.Streams.dll", "lib/netstandard2.1/K4os.Compression.LZ4.Streams.xml"]}, "K4os.Hash.xxHash/1.0.8": {"sha512": "Wp2F7BamQ2Q/7Hk834nV9vRQapgcr8kgv9Jvfm8J3D0IhDqZMMl+a2yxUq5ltJitvXvQfB8W6K4F4fCbw/P6YQ==", "type": "package", "path": "k4os.hash.xxhash/1.0.8", "files": [".nupkg.metadata", ".signature.p7s", "k4os.hash.xxhash.1.0.8.nupkg.sha512", "k4os.hash.xxhash.nuspec", "lib/net462/K4os.Hash.xxHash.dll", "lib/net462/K4os.Hash.xxHash.xml", "lib/net5.0/K4os.Hash.xxHash.dll", "lib/net5.0/K4os.Hash.xxHash.xml", "lib/net6.0/K4os.Hash.xxHash.dll", "lib/net6.0/K4os.Hash.xxHash.xml", "lib/netstandard2.0/K4os.Hash.xxHash.dll", "lib/netstandard2.0/K4os.Hash.xxHash.xml", "lib/netstandard2.1/K4os.Hash.xxHash.dll", "lib/netstandard2.1/K4os.Hash.xxHash.xml"]}, "Mapster/7.4.2-pre02": {"sha512": "ry6YPGImX4aL0+Jj0rn46vqYOxzeyKOdlr6LUPvby1NLc68E5T2gRhDVWxvFexgZGOIW/U8NM87kygE4xl7Qvw==", "type": "package", "path": "mapster/7.4.2-pre02", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net8.0/Mapster.dll", "lib/net9.0/Mapster.dll", "mapster.7.4.2-pre02.nupkg.sha512", "mapster.nuspec"]}, "Mapster.Core/1.2.3-pre02": {"sha512": "FDVPfGvCz1RRb1PYeoddiJftxo+ANAifiaqkZbH2kijZT2GcoR1w6byLQ8Czc9YBuNZqHHV4LKeXZA+eoFuCTw==", "type": "package", "path": "mapster.core/1.2.3-pre02", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net8.0/Mapster.Core.dll", "lib/net9.0/Mapster.Core.dll", "mapster.core.1.2.3-pre02.nupkg.sha512", "mapster.core.nuspec"]}, "Masa.BuildingBlocks.Configuration/1.2.0-preview.10": {"sha512": "v88mphyr/htxFUkT0vW2vgj1/sLGnXt1kl/K2cWVPEC5NswVHM3l7Jq9cWpzloeVTg/8GjjNHzb8IpuOzedHvg==", "type": "package", "path": "masa.buildingblocks.configuration/1.2.0-preview.10", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net10.0/Masa.BuildingBlocks.Configuration.dll", "lib/net10.0/Masa.BuildingBlocks.Configuration.pdb", "lib/net6.0/Masa.BuildingBlocks.Configuration.dll", "lib/net6.0/Masa.BuildingBlocks.Configuration.pdb", "lib/net7.0/Masa.BuildingBlocks.Configuration.dll", "lib/net7.0/Masa.BuildingBlocks.Configuration.pdb", "lib/net8.0/Masa.BuildingBlocks.Configuration.dll", "lib/net8.0/Masa.BuildingBlocks.Configuration.pdb", "lib/net9.0/Masa.BuildingBlocks.Configuration.dll", "lib/net9.0/Masa.BuildingBlocks.Configuration.pdb", "masa.buildingblocks.configuration.1.2.0-preview.10.nupkg.sha512", "masa.buildingblocks.configuration.nuspec", "packageIcon.png"]}, "Masa.BuildingBlocks.Data/1.2.0-preview.10": {"sha512": "tWug8fTJotWKttvha1g1ZEmy/FcLmWDKDLuHMTMktjgISJRw/EOHjLRRzvKwK0FIbzo7fQIBTchdMHh++nK25w==", "type": "package", "path": "masa.buildingblocks.data/1.2.0-preview.10", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net10.0/Masa.BuildingBlocks.Data.dll", "lib/net10.0/Masa.BuildingBlocks.Data.pdb", "lib/net6.0/Masa.BuildingBlocks.Data.dll", "lib/net6.0/Masa.BuildingBlocks.Data.pdb", "lib/net7.0/Masa.BuildingBlocks.Data.dll", "lib/net7.0/Masa.BuildingBlocks.Data.pdb", "lib/net8.0/Masa.BuildingBlocks.Data.dll", "lib/net8.0/Masa.BuildingBlocks.Data.pdb", "lib/net9.0/Masa.BuildingBlocks.Data.dll", "lib/net9.0/Masa.BuildingBlocks.Data.pdb", "masa.buildingblocks.data.1.2.0-preview.10.nupkg.sha512", "masa.buildingblocks.data.nuspec", "packageIcon.png"]}, "Masa.BuildingBlocks.Data.Contracts/1.2.0-preview.10": {"sha512": "m7tI1FwV073FfL5H5Ee6l2+Wq5errGriSI3y03JexVF9t3ecYLXeiw3vigcwofSv/jFJJb021nLqz86C6XL/uw==", "type": "package", "path": "masa.buildingblocks.data.contracts/1.2.0-preview.10", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net10.0/Masa.BuildingBlocks.Data.Contracts.dll", "lib/net10.0/Masa.BuildingBlocks.Data.Contracts.pdb", "lib/net6.0/Masa.BuildingBlocks.Data.Contracts.dll", "lib/net6.0/Masa.BuildingBlocks.Data.Contracts.pdb", "lib/net7.0/Masa.BuildingBlocks.Data.Contracts.dll", "lib/net7.0/Masa.BuildingBlocks.Data.Contracts.pdb", "lib/net8.0/Masa.BuildingBlocks.Data.Contracts.dll", "lib/net8.0/Masa.BuildingBlocks.Data.Contracts.pdb", "lib/net9.0/Masa.BuildingBlocks.Data.Contracts.dll", "lib/net9.0/Masa.BuildingBlocks.Data.Contracts.pdb", "masa.buildingblocks.data.contracts.1.2.0-preview.10.nupkg.sha512", "masa.buildingblocks.data.contracts.nuspec", "packageIcon.png"]}, "Masa.BuildingBlocks.Development.DaprStarter/1.2.0-preview.10": {"sha512": "I2Ibpft/LYI85jwXDjopNF4xufsGqO8fcV9Mh3ap8Zq/TBsqJfiO+2oHLhwv0H5AabsAH4coKcHk/WgGPKyakg==", "type": "package", "path": "masa.buildingblocks.development.daprstarter/1.2.0-preview.10", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net10.0/Masa.BuildingBlocks.Development.DaprStarter.dll", "lib/net10.0/Masa.BuildingBlocks.Development.DaprStarter.pdb", "lib/net6.0/Masa.BuildingBlocks.Development.DaprStarter.dll", "lib/net6.0/Masa.BuildingBlocks.Development.DaprStarter.pdb", "lib/net7.0/Masa.BuildingBlocks.Development.DaprStarter.dll", "lib/net7.0/Masa.BuildingBlocks.Development.DaprStarter.pdb", "lib/net8.0/Masa.BuildingBlocks.Development.DaprStarter.dll", "lib/net8.0/Masa.BuildingBlocks.Development.DaprStarter.pdb", "lib/net9.0/Masa.BuildingBlocks.Development.DaprStarter.dll", "lib/net9.0/Masa.BuildingBlocks.Development.DaprStarter.pdb", "masa.buildingblocks.development.daprstarter.1.2.0-preview.10.nupkg.sha512", "masa.buildingblocks.development.daprstarter.nuspec", "packageIcon.png"]}, "Masa.BuildingBlocks.Exceptions/1.2.0-preview.10": {"sha512": "RUuLq9BToJcEHAm6pzgMrGT2sS/E07J12v9RBTSGBzuKuJW+5ClbMLKIQ6uhn9szrcZEOemuGZOeuAQroKVtsw==", "type": "package", "path": "masa.buildingblocks.exceptions/1.2.0-preview.10", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net10.0/Masa.BuildingBlocks.Exceptions.dll", "lib/net10.0/Masa.BuildingBlocks.Exceptions.pdb", "lib/net6.0/Masa.BuildingBlocks.Exceptions.dll", "lib/net6.0/Masa.BuildingBlocks.Exceptions.pdb", "lib/net7.0/Masa.BuildingBlocks.Exceptions.dll", "lib/net7.0/Masa.BuildingBlocks.Exceptions.pdb", "lib/net8.0/Masa.BuildingBlocks.Exceptions.dll", "lib/net8.0/Masa.BuildingBlocks.Exceptions.pdb", "lib/net9.0/Masa.BuildingBlocks.Exceptions.dll", "lib/net9.0/Masa.BuildingBlocks.Exceptions.pdb", "masa.buildingblocks.exceptions.1.2.0-preview.10.nupkg.sha512", "masa.buildingblocks.exceptions.nuspec", "packageIcon.png"]}, "Masa.BuildingBlocks.Globalization.I18n/1.2.0-preview.10": {"sha512": "4pe+6v0kT8ajAiilDizteiO8XiUwFpn5CrP9nP6QPFxhKv+R2WXZjFcqXhlVGBg+Dh9sAUchYdPX/6PCtdNsng==", "type": "package", "path": "masa.buildingblocks.globalization.i18n/1.2.0-preview.10", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net10.0/Masa.BuildingBlocks.Globalization.I18n.dll", "lib/net10.0/Masa.BuildingBlocks.Globalization.I18n.pdb", "lib/net6.0/Masa.BuildingBlocks.Globalization.I18n.dll", "lib/net6.0/Masa.BuildingBlocks.Globalization.I18n.pdb", "lib/net7.0/Masa.BuildingBlocks.Globalization.I18n.dll", "lib/net7.0/Masa.BuildingBlocks.Globalization.I18n.pdb", "lib/net8.0/Masa.BuildingBlocks.Globalization.I18n.dll", "lib/net8.0/Masa.BuildingBlocks.Globalization.I18n.pdb", "lib/net9.0/Masa.BuildingBlocks.Globalization.I18n.dll", "lib/net9.0/Masa.BuildingBlocks.Globalization.I18n.pdb", "masa.buildingblocks.globalization.i18n.1.2.0-preview.10.nupkg.sha512", "masa.buildingblocks.globalization.i18n.nuspec", "packageIcon.png"]}, "Masa.Contrib.Development.DaprStarter/1.2.0-preview.10": {"sha512": "Zb8g9m9V04FJFxdgoj4zg1irVhnKyDjAQqU+nikf1e66Tomcx9voqKpVofJ7sRy7RvZLz7G1O18FcTXu/UcvfQ==", "type": "package", "path": "masa.contrib.development.daprstarter/1.2.0-preview.10", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net10.0/Masa.Contrib.Development.DaprStarter.dll", "lib/net10.0/Masa.Contrib.Development.DaprStarter.pdb", "lib/net6.0/Masa.Contrib.Development.DaprStarter.dll", "lib/net6.0/Masa.Contrib.Development.DaprStarter.pdb", "lib/net7.0/Masa.Contrib.Development.DaprStarter.dll", "lib/net7.0/Masa.Contrib.Development.DaprStarter.pdb", "lib/net8.0/Masa.Contrib.Development.DaprStarter.dll", "lib/net8.0/Masa.Contrib.Development.DaprStarter.pdb", "lib/net9.0/Masa.Contrib.Development.DaprStarter.dll", "lib/net9.0/Masa.Contrib.Development.DaprStarter.pdb", "masa.contrib.development.daprstarter.1.2.0-preview.10.nupkg.sha512", "masa.contrib.development.daprstarter.nuspec", "packageIcon.png"]}, "Masa.Contrib.Development.DaprStarter.AspNetCore/1.2.0-preview.10": {"sha512": "qq3N9nTH+/GtCcXmmMGhSvdKbPSC6p1LJc0TxS0vgSNXULcSw+znxIwjzemIkQRmK+6uXpsuOo4ZGClmIz0cRg==", "type": "package", "path": "masa.contrib.development.daprstarter.aspnetcore/1.2.0-preview.10", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net10.0/Masa.Contrib.Development.DaprStarter.AspNetCore.dll", "lib/net10.0/Masa.Contrib.Development.DaprStarter.AspNetCore.pdb", "lib/net6.0/Masa.Contrib.Development.DaprStarter.AspNetCore.dll", "lib/net6.0/Masa.Contrib.Development.DaprStarter.AspNetCore.pdb", "lib/net7.0/Masa.Contrib.Development.DaprStarter.AspNetCore.dll", "lib/net7.0/Masa.Contrib.Development.DaprStarter.AspNetCore.pdb", "lib/net8.0/Masa.Contrib.Development.DaprStarter.AspNetCore.dll", "lib/net8.0/Masa.Contrib.Development.DaprStarter.AspNetCore.pdb", "lib/net9.0/Masa.Contrib.Development.DaprStarter.AspNetCore.dll", "lib/net9.0/Masa.Contrib.Development.DaprStarter.AspNetCore.pdb", "masa.contrib.development.daprstarter.aspnetcore.1.2.0-preview.10.nupkg.sha512", "masa.contrib.development.daprstarter.aspnetcore.nuspec", "packageIcon.png"]}, "Masa.Utils.Caching.Memory/1.2.0-preview.10": {"sha512": "zpE6V1CzNgzXzHpJVjJowTNs5A7bF5E3RJ+JsZ4/YDLYj52D+h1G8Qz41z8pK/sDdJANI2hiXCAjqaLWHlwX1Q==", "type": "package", "path": "masa.utils.caching.memory/1.2.0-preview.10", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net10.0/Masa.Utils.Caching.Memory.dll", "lib/net10.0/Masa.Utils.Caching.Memory.pdb", "lib/net6.0/Masa.Utils.Caching.Memory.dll", "lib/net6.0/Masa.Utils.Caching.Memory.pdb", "lib/net7.0/Masa.Utils.Caching.Memory.dll", "lib/net7.0/Masa.Utils.Caching.Memory.pdb", "lib/net8.0/Masa.Utils.Caching.Memory.dll", "lib/net8.0/Masa.Utils.Caching.Memory.pdb", "lib/net9.0/Masa.Utils.Caching.Memory.dll", "lib/net9.0/Masa.Utils.Caching.Memory.pdb", "masa.utils.caching.memory.1.2.0-preview.10.nupkg.sha512", "masa.utils.caching.memory.nuspec", "packageIcon.png"]}, "Masa.Utils.Extensions.DotNet/1.2.0-preview.10": {"sha512": "8H6g8V8oHWQwGjDgi5CpM5ObI4xBfBzziQEvK4cgBq81us4oqP1QqKktAF4YUCr9Ld7aA0W4cwys49t/TUJy7w==", "type": "package", "path": "masa.utils.extensions.dotnet/1.2.0-preview.10", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net10.0/Masa.Utils.Extensions.DotNet.dll", "lib/net10.0/Masa.Utils.Extensions.DotNet.pdb", "lib/net6.0/Masa.Utils.Extensions.DotNet.dll", "lib/net6.0/Masa.Utils.Extensions.DotNet.pdb", "lib/net7.0/Masa.Utils.Extensions.DotNet.dll", "lib/net7.0/Masa.Utils.Extensions.DotNet.pdb", "lib/net8.0/Masa.Utils.Extensions.DotNet.dll", "lib/net8.0/Masa.Utils.Extensions.DotNet.pdb", "lib/net9.0/Masa.Utils.Extensions.DotNet.dll", "lib/net9.0/Masa.Utils.Extensions.DotNet.pdb", "masa.utils.extensions.dotnet.1.2.0-preview.10.nupkg.sha512", "masa.utils.extensions.dotnet.nuspec", "packageIcon.png"]}, "Masa.Utils.Models.Config/1.2.0-preview.10": {"sha512": "MIjltbUH6sBov06Y3+WWBeYKjBZSPiiWjeaVOKyYBn5uWeEhSEYJenSHUKnSPZYTI7d3HDWRjJ9AD0aagbj4bg==", "type": "package", "path": "masa.utils.models.config/1.2.0-preview.10", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net10.0/Masa.Utils.Models.Config.dll", "lib/net10.0/Masa.Utils.Models.Config.pdb", "lib/net6.0/Masa.Utils.Models.Config.dll", "lib/net6.0/Masa.Utils.Models.Config.pdb", "lib/net7.0/Masa.Utils.Models.Config.dll", "lib/net7.0/Masa.Utils.Models.Config.pdb", "lib/net8.0/Masa.Utils.Models.Config.dll", "lib/net8.0/Masa.Utils.Models.Config.pdb", "lib/net9.0/Masa.Utils.Models.Config.dll", "lib/net9.0/Masa.Utils.Models.Config.pdb", "masa.utils.models.config.1.2.0-preview.10.nupkg.sha512", "masa.utils.models.config.nuspec", "packageIcon.png"]}, "Masuit.Tools.Abstractions/2025.5.0": {"sha512": "GHUH5VrQU+sqW+y+sd4T4ySv8O3F95BMNKcCLpt9QA3khRO2scV6eSI2j6xkZPGnaBkdIONz5wTxXrlDQIoGNQ==", "type": "package", "path": "masuit.tools.abstractions/2025.5.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net461/Masuit.Tools.Abstractions.dll", "lib/net461/Masuit.Tools.Abstractions.xml", "lib/net6.0/Masuit.Tools.Abstractions.dll", "lib/net6.0/Masuit.Tools.Abstractions.xml", "lib/net8.0/Masuit.Tools.Abstractions.dll", "lib/net8.0/Masuit.Tools.Abstractions.xml", "lib/net9.0/Masuit.Tools.Abstractions.dll", "lib/net9.0/Masuit.Tools.Abstractions.xml", "lib/netstandard2.0/Masuit.Tools.Abstractions.dll", "lib/netstandard2.0/Masuit.Tools.Abstractions.xml", "lib/netstandard2.1/Masuit.Tools.Abstractions.dll", "lib/netstandard2.1/Masuit.Tools.Abstractions.xml", "masuit.tools.abstractions.2025.5.0.nupkg.sha512", "masuit.tools.abstractions.nuspec"]}, "Masuit.Tools.Core/2025.5.0": {"sha512": "JtWJz6R+vh2oOpNQSwm1ElFNmO3cKIqCor4tEoUHSq4axSigsuLuJlaviOZ+c3EyF8ELrKvjH42bTz2girDxRg==", "type": "package", "path": "masuit.tools.core/2025.5.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net6.0/Masuit.Tools.Core.dll", "lib/net6.0/Masuit.Tools.Core.xml", "lib/net8.0/Masuit.Tools.Core.dll", "lib/net8.0/Masuit.Tools.Core.xml", "lib/net9.0/Masuit.Tools.Core.dll", "lib/net9.0/Masuit.Tools.Core.xml", "lib/netstandard2.0/Masuit.Tools.Core.dll", "lib/netstandard2.0/Masuit.Tools.Core.xml", "lib/netstandard2.1/Masuit.Tools.Core.dll", "lib/netstandard2.1/Masuit.Tools.Core.xml", "masuit.tools.core.2025.5.0.nupkg.sha512", "masuit.tools.core.nuspec"]}, "Microsoft.AspNetCore.Authentication.JwtBearer/10.0.0-preview.7.25380.108": {"sha512": "pSUTOnT72Xm2bFk/C+9zESkIySQabWdXZ9v5gmao4bh+fjcqqmnuNC/6sNimYtvR1rbEBML8z4TFjeW5ve7k1w==", "type": "package", "path": "microsoft.aspnetcore.authentication.jwtbearer/10.0.0-preview.7.25380.108", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "lib/net10.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll", "lib/net10.0/Microsoft.AspNetCore.Authentication.JwtBearer.xml", "microsoft.aspnetcore.authentication.jwtbearer.10.0.0-preview.7.25380.108.nupkg.sha512", "microsoft.aspnetcore.authentication.jwtbearer.nuspec"]}, "Microsoft.AspNetCore.OpenApi/9.0.1": {"sha512": "xRJe8UrLnOGs6hOBrT/4r74q97626H0mABb/DV0smlReIx6uQCENAe+TUqF6hD3NtT4sB+qrvWhAej6kxPxgew==", "type": "package", "path": "microsoft.aspnetcore.openapi/9.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "lib/net9.0/Microsoft.AspNetCore.OpenApi.dll", "lib/net9.0/Microsoft.AspNetCore.OpenApi.xml", "microsoft.aspnetcore.openapi.9.0.1.nupkg.sha512", "microsoft.aspnetcore.openapi.nuspec"]}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"sha512": "3WA9q9yVqJp222P3x1wYIGDAkpjAku0TMUaaQV22g6L67AI0LdOIrVS7Ht2vJfLHGSPVuqN94vIr15qn+HEkHw==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Bcl.AsyncInterfaces.targets", "buildTransitive/net462/_._", "lib/net462/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net462/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Bcl.Cryptography/9.0.4": {"sha512": "YgZYAWzyNuPVtPq6WNm0bqOWNjYaWgl5mBWTGZyNoXitYBUYSp6iUB9AwK0V1mo793qRJUXz2t6UZrWITZSvuQ==", "type": "package", "path": "microsoft.bcl.cryptography/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Bcl.Cryptography.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Bcl.Cryptography.targets", "lib/net462/Microsoft.Bcl.Cryptography.dll", "lib/net462/Microsoft.Bcl.Cryptography.xml", "lib/net8.0/Microsoft.Bcl.Cryptography.dll", "lib/net8.0/Microsoft.Bcl.Cryptography.xml", "lib/net9.0/Microsoft.Bcl.Cryptography.dll", "lib/net9.0/Microsoft.Bcl.Cryptography.xml", "lib/netstandard2.0/Microsoft.Bcl.Cryptography.dll", "lib/netstandard2.0/Microsoft.Bcl.Cryptography.xml", "microsoft.bcl.cryptography.9.0.4.nupkg.sha512", "microsoft.bcl.cryptography.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Data.SqlClient/6.1.1": {"sha512": "syGQmIUPAYYHAHyTD8FCkTNThpQWvoA7crnIQRMfp8dyB5A2cWU3fQexlRTFkVmV7S0TjVmthi0LJEFVjHo8AQ==", "type": "package", "path": "microsoft.data.sqlclient/6.1.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "dotnet.png", "lib/net462/Microsoft.Data.SqlClient.dll", "lib/net462/Microsoft.Data.SqlClient.xml", "lib/net462/cs/Microsoft.Data.SqlClient.resources.dll", "lib/net462/de/Microsoft.Data.SqlClient.resources.dll", "lib/net462/es/Microsoft.Data.SqlClient.resources.dll", "lib/net462/fr/Microsoft.Data.SqlClient.resources.dll", "lib/net462/it/Microsoft.Data.SqlClient.resources.dll", "lib/net462/ja/Microsoft.Data.SqlClient.resources.dll", "lib/net462/ko/Microsoft.Data.SqlClient.resources.dll", "lib/net462/pl/Microsoft.Data.SqlClient.resources.dll", "lib/net462/pt-BR/Microsoft.Data.SqlClient.resources.dll", "lib/net462/ru/Microsoft.Data.SqlClient.resources.dll", "lib/net462/tr/Microsoft.Data.SqlClient.resources.dll", "lib/net462/zh-<PERSON>/Microsoft.Data.SqlClient.resources.dll", "lib/net462/zh-Hant/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/Microsoft.Data.SqlClient.dll", "lib/net8.0/Microsoft.Data.SqlClient.xml", "lib/net8.0/cs/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/de/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/es/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/fr/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/it/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/ja/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/ko/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/pl/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/pt-BR/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/ru/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/tr/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/Microsoft.Data.SqlClient.dll", "lib/net9.0/Microsoft.Data.SqlClient.xml", "lib/net9.0/cs/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/de/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/es/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/fr/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/it/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/ja/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/ko/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/pl/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/pt-BR/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/ru/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/tr/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.0/Microsoft.Data.SqlClient.dll", "lib/netstandard2.0/Microsoft.Data.SqlClient.xml", "microsoft.data.sqlclient.6.1.1.nupkg.sha512", "microsoft.data.sqlclient.nuspec", "ref/net462/Microsoft.Data.SqlClient.dll", "ref/net462/Microsoft.Data.SqlClient.xml", "ref/net8.0/Microsoft.Data.SqlClient.dll", "ref/net8.0/Microsoft.Data.SqlClient.xml", "ref/net9.0/Microsoft.Data.SqlClient.dll", "ref/net9.0/Microsoft.Data.SqlClient.xml", "ref/netstandard2.0/Microsoft.Data.SqlClient.dll", "ref/netstandard2.0/Microsoft.Data.SqlClient.xml", "runtimes/unix/lib/net8.0/Microsoft.Data.SqlClient.dll", "runtimes/unix/lib/net9.0/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/net462/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/net8.0/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/net9.0/Microsoft.Data.SqlClient.dll"]}, "Microsoft.Data.SqlClient.SNI.runtime/6.0.2": {"sha512": "f+pRODTWX7Y67jXO3T5S2dIPZ9qMJNySjlZT/TKmWVNWe19N8jcWmHaqHnnchaq3gxEKv1SWVY5EFzOD06l41w==", "type": "package", "path": "microsoft.data.sqlclient.sni.runtime/6.0.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "dotnet.png", "microsoft.data.sqlclient.sni.runtime.6.0.2.nupkg.sha512", "microsoft.data.sqlclient.sni.runtime.nuspec", "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll", "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll", "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll"]}, "Microsoft.DependencyValidation.Analyzers/0.11.0": {"sha512": "3xBVhMsM4o9cDpsvmlv78T1FG+cTH8i6bjl1Irk6hoIwEc6Ekoz4mKAR6Hn5M/2G2ZsHNrYBNwGkgF7aiw74UA==", "type": "package", "path": "microsoft.dependencyvalidation.analyzers/0.11.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/Microsoft.DependencyValidation.Analyzers.dll", "analyzers/dotnet/cs/Microsoft.DependencyValidation.Analyzers.resources.dll", "analyzers/dotnet/de/Microsoft.DependencyValidation.Analyzers.resources.dll", "analyzers/dotnet/es/Microsoft.DependencyValidation.Analyzers.resources.dll", "analyzers/dotnet/fr/Microsoft.DependencyValidation.Analyzers.resources.dll", "analyzers/dotnet/it/Microsoft.DependencyValidation.Analyzers.resources.dll", "analyzers/dotnet/ja/Microsoft.DependencyValidation.Analyzers.resources.dll", "analyzers/dotnet/ko/Microsoft.DependencyValidation.Analyzers.resources.dll", "analyzers/dotnet/pl/Microsoft.DependencyValidation.Analyzers.resources.dll", "analyzers/dotnet/pt-BR/Microsoft.DependencyValidation.Analyzers.resources.dll", "analyzers/dotnet/ru/Microsoft.DependencyValidation.Analyzers.resources.dll", "analyzers/dotnet/tr/Microsoft.DependencyValidation.Analyzers.resources.dll", "analyzers/dotnet/zh-HANS/Microsoft.DependencyValidation.Analyzers.resources.dll", "analyzers/dotnet/zh-HANT/Microsoft.DependencyValidation.Analyzers.resources.dll", "microsoft.dependencyvalidation.analyzers.0.11.0.nupkg.sha512", "microsoft.dependencyvalidation.analyzers.nuspec", "tools/install.ps1", "tools/uninstall.ps1"]}, "Microsoft.EntityFrameworkCore/9.0.8": {"sha512": "bNGdPhN762+BIIO5MFYLjafRqkSS1MqLOc/erd55InvLnFxt9H3N5JNsuag1ZHyBor1VtD42U0CHpgqkWeAYgQ==", "type": "package", "path": "microsoft.entityframeworkcore/9.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net8.0/Microsoft.EntityFrameworkCore.props", "lib/net8.0/Microsoft.EntityFrameworkCore.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.xml", "microsoft.entityframeworkcore.9.0.8.nupkg.sha512", "microsoft.entityframeworkcore.nuspec"]}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.8": {"sha512": "B2yfAIQRRAQ4zvvWqh+HudD+juV3YoLlpXnrog3tU0PM9AFpuq6xo0+mEglN1P43WgdcUiF+65CWBcZe35s15Q==", "type": "package", "path": "microsoft.entityframeworkcore.abstractions/9.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.xml", "microsoft.entityframeworkcore.abstractions.9.0.8.nupkg.sha512", "microsoft.entityframeworkcore.abstractions.nuspec"]}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.8": {"sha512": "2EYStCXt4Hi9p3J3EYMQbItJDtASJd064Kcs8C8hj8Jt5srILrR9qlaL0Ryvk8NrWQoCQvIELsmiuqLEZMLvGA==", "type": "package", "path": "microsoft.entityframeworkcore.analyzers/9.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "analyzers/dotnet/cs/Microsoft.EntityFrameworkCore.Analyzers.dll", "docs/PACKAGE.md", "microsoft.entityframeworkcore.analyzers.9.0.8.nupkg.sha512", "microsoft.entityframeworkcore.analyzers.nuspec"]}, "Microsoft.Extensions.AmbientMetadata.Application/9.8.0": {"sha512": "NEzH1h6sNqBMZiy3JCsPbAOCKN9dOQ65aAhXK2CuqmfM8pcsYQH8jrwSJjvGf8G8m0FY6B31AgkI6/cE8Xzvvg==", "type": "package", "path": "microsoft.extensions.ambientmetadata.application/9.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "buildTransitive/net462/Microsoft.Extensions.AmbientMetadata.Application.targets", "buildTransitive/net8.0/_._", "lib/net462/Microsoft.Extensions.AmbientMetadata.Application.dll", "lib/net462/Microsoft.Extensions.AmbientMetadata.Application.xml", "lib/net8.0/Microsoft.Extensions.AmbientMetadata.Application.dll", "lib/net8.0/Microsoft.Extensions.AmbientMetadata.Application.xml", "lib/net9.0/Microsoft.Extensions.AmbientMetadata.Application.dll", "lib/net9.0/Microsoft.Extensions.AmbientMetadata.Application.xml", "lib/netstandard2.0/Microsoft.Extensions.AmbientMetadata.Application.dll", "lib/netstandard2.0/Microsoft.Extensions.AmbientMetadata.Application.xml", "microsoft.extensions.ambientmetadata.application.9.8.0.nupkg.sha512", "microsoft.extensions.ambientmetadata.application.nuspec"]}, "Microsoft.Extensions.ApiDescription.Server/9.0.0": {"sha512": "1Kzzf7pRey40VaUkHN9/uWxrKVkLu2AQjt+GVeeKLLpiEHAJ1xZRsLSh4ZZYEnyS7Kt2OBOPmsXNdU+wbcOl5w==", "type": "package", "path": "microsoft.extensions.apidescription.server/9.0.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/Microsoft.Extensions.ApiDescription.Server.props", "build/Microsoft.Extensions.ApiDescription.Server.targets", "buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.props", "buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.targets", "microsoft.extensions.apidescription.server.9.0.0.nupkg.sha512", "microsoft.extensions.apidescription.server.nuspec", "tools/Newtonsoft.Json.dll", "tools/dotnet-getdocument.deps.json", "tools/dotnet-getdocument.dll", "tools/dotnet-getdocument.runtimeconfig.json", "tools/net462-x86/GetDocument.Insider.exe", "tools/net462-x86/GetDocument.Insider.exe.config", "tools/net462-x86/Microsoft.OpenApi.dll", "tools/net462-x86/Microsoft.Win32.Primitives.dll", "tools/net462-x86/System.AppContext.dll", "tools/net462-x86/System.Buffers.dll", "tools/net462-x86/System.Collections.Concurrent.dll", "tools/net462-x86/System.Collections.NonGeneric.dll", "tools/net462-x86/System.Collections.Specialized.dll", "tools/net462-x86/System.Collections.dll", "tools/net462-x86/System.ComponentModel.EventBasedAsync.dll", "tools/net462-x86/System.ComponentModel.Primitives.dll", "tools/net462-x86/System.ComponentModel.TypeConverter.dll", "tools/net462-x86/System.ComponentModel.dll", "tools/net462-x86/System.Console.dll", "tools/net462-x86/System.Data.Common.dll", "tools/net462-x86/System.Diagnostics.Contracts.dll", "tools/net462-x86/System.Diagnostics.Debug.dll", "tools/net462-x86/System.Diagnostics.DiagnosticSource.dll", "tools/net462-x86/System.Diagnostics.FileVersionInfo.dll", "tools/net462-x86/System.Diagnostics.Process.dll", "tools/net462-x86/System.Diagnostics.StackTrace.dll", "tools/net462-x86/System.Diagnostics.TextWriterTraceListener.dll", "tools/net462-x86/System.Diagnostics.Tools.dll", "tools/net462-x86/System.Diagnostics.TraceSource.dll", "tools/net462-x86/System.Diagnostics.Tracing.dll", "tools/net462-x86/System.Drawing.Primitives.dll", "tools/net462-x86/System.Dynamic.Runtime.dll", "tools/net462-x86/System.Globalization.Calendars.dll", "tools/net462-x86/System.Globalization.Extensions.dll", "tools/net462-x86/System.Globalization.dll", "tools/net462-x86/System.IO.Compression.ZipFile.dll", "tools/net462-x86/System.IO.Compression.dll", "tools/net462-x86/System.IO.FileSystem.DriveInfo.dll", "tools/net462-x86/System.IO.FileSystem.Primitives.dll", "tools/net462-x86/System.IO.FileSystem.Watcher.dll", "tools/net462-x86/System.IO.FileSystem.dll", "tools/net462-x86/System.IO.IsolatedStorage.dll", "tools/net462-x86/System.IO.MemoryMappedFiles.dll", "tools/net462-x86/System.IO.Pipes.dll", "tools/net462-x86/System.IO.UnmanagedMemoryStream.dll", "tools/net462-x86/System.IO.dll", "tools/net462-x86/System.Linq.Expressions.dll", "tools/net462-x86/System.Linq.Parallel.dll", "tools/net462-x86/System.Linq.Queryable.dll", "tools/net462-x86/System.Linq.dll", "tools/net462-x86/System.Memory.dll", "tools/net462-x86/System.Net.Http.dll", "tools/net462-x86/System.Net.NameResolution.dll", "tools/net462-x86/System.Net.NetworkInformation.dll", "tools/net462-x86/System.Net.Ping.dll", "tools/net462-x86/System.Net.Primitives.dll", "tools/net462-x86/System.Net.Requests.dll", "tools/net462-x86/System.Net.Security.dll", "tools/net462-x86/System.Net.Sockets.dll", "tools/net462-x86/System.Net.WebHeaderCollection.dll", "tools/net462-x86/System.Net.WebSockets.Client.dll", "tools/net462-x86/System.Net.WebSockets.dll", "tools/net462-x86/System.Numerics.Vectors.dll", "tools/net462-x86/System.ObjectModel.dll", "tools/net462-x86/System.Reflection.Extensions.dll", "tools/net462-x86/System.Reflection.Primitives.dll", "tools/net462-x86/System.Reflection.dll", "tools/net462-x86/System.Resources.Reader.dll", "tools/net462-x86/System.Resources.ResourceManager.dll", "tools/net462-x86/System.Resources.Writer.dll", "tools/net462-x86/System.Runtime.CompilerServices.Unsafe.dll", "tools/net462-x86/System.Runtime.CompilerServices.VisualC.dll", "tools/net462-x86/System.Runtime.Extensions.dll", "tools/net462-x86/System.Runtime.Handles.dll", "tools/net462-x86/System.Runtime.InteropServices.RuntimeInformation.dll", "tools/net462-x86/System.Runtime.InteropServices.dll", "tools/net462-x86/System.Runtime.Numerics.dll", "tools/net462-x86/System.Runtime.Serialization.Formatters.dll", "tools/net462-x86/System.Runtime.Serialization.Json.dll", "tools/net462-x86/System.Runtime.Serialization.Primitives.dll", "tools/net462-x86/System.Runtime.Serialization.Xml.dll", "tools/net462-x86/System.Runtime.dll", "tools/net462-x86/System.Security.Claims.dll", "tools/net462-x86/System.Security.Cryptography.Algorithms.dll", "tools/net462-x86/System.Security.Cryptography.Csp.dll", "tools/net462-x86/System.Security.Cryptography.Encoding.dll", "tools/net462-x86/System.Security.Cryptography.Primitives.dll", "tools/net462-x86/System.Security.Cryptography.X509Certificates.dll", "tools/net462-x86/System.Security.Principal.dll", "tools/net462-x86/System.Security.SecureString.dll", "tools/net462-x86/System.Text.Encoding.Extensions.dll", "tools/net462-x86/System.Text.Encoding.dll", "tools/net462-x86/System.Text.RegularExpressions.dll", "tools/net462-x86/System.Threading.Overlapped.dll", "tools/net462-x86/System.Threading.Tasks.Parallel.dll", "tools/net462-x86/System.Threading.Tasks.dll", "tools/net462-x86/System.Threading.Thread.dll", "tools/net462-x86/System.Threading.ThreadPool.dll", "tools/net462-x86/System.Threading.Timer.dll", "tools/net462-x86/System.Threading.dll", "tools/net462-x86/System.ValueTuple.dll", "tools/net462-x86/System.Xml.ReaderWriter.dll", "tools/net462-x86/System.Xml.XDocument.dll", "tools/net462-x86/System.Xml.XPath.XDocument.dll", "tools/net462-x86/System.Xml.XPath.dll", "tools/net462-x86/System.Xml.XmlDocument.dll", "tools/net462-x86/System.Xml.XmlSerializer.dll", "tools/net462-x86/netstandard.dll", "tools/net462/GetDocument.Insider.exe", "tools/net462/GetDocument.Insider.exe.config", "tools/net462/Microsoft.OpenApi.dll", "tools/net462/Microsoft.Win32.Primitives.dll", "tools/net462/System.AppContext.dll", "tools/net462/System.Buffers.dll", "tools/net462/System.Collections.Concurrent.dll", "tools/net462/System.Collections.NonGeneric.dll", "tools/net462/System.Collections.Specialized.dll", "tools/net462/System.Collections.dll", "tools/net462/System.ComponentModel.EventBasedAsync.dll", "tools/net462/System.ComponentModel.Primitives.dll", "tools/net462/System.ComponentModel.TypeConverter.dll", "tools/net462/System.ComponentModel.dll", "tools/net462/System.Console.dll", "tools/net462/System.Data.Common.dll", "tools/net462/System.Diagnostics.Contracts.dll", "tools/net462/System.Diagnostics.Debug.dll", "tools/net462/System.Diagnostics.DiagnosticSource.dll", "tools/net462/System.Diagnostics.FileVersionInfo.dll", "tools/net462/System.Diagnostics.Process.dll", "tools/net462/System.Diagnostics.StackTrace.dll", "tools/net462/System.Diagnostics.TextWriterTraceListener.dll", "tools/net462/System.Diagnostics.Tools.dll", "tools/net462/System.Diagnostics.TraceSource.dll", "tools/net462/System.Diagnostics.Tracing.dll", "tools/net462/System.Drawing.Primitives.dll", "tools/net462/System.Dynamic.Runtime.dll", "tools/net462/System.Globalization.Calendars.dll", "tools/net462/System.Globalization.Extensions.dll", "tools/net462/System.Globalization.dll", "tools/net462/System.IO.Compression.ZipFile.dll", "tools/net462/System.IO.Compression.dll", "tools/net462/System.IO.FileSystem.DriveInfo.dll", "tools/net462/System.IO.FileSystem.Primitives.dll", "tools/net462/System.IO.FileSystem.Watcher.dll", "tools/net462/System.IO.FileSystem.dll", "tools/net462/System.IO.IsolatedStorage.dll", "tools/net462/System.IO.MemoryMappedFiles.dll", "tools/net462/System.IO.Pipes.dll", "tools/net462/System.IO.UnmanagedMemoryStream.dll", "tools/net462/System.IO.dll", "tools/net462/System.Linq.Expressions.dll", "tools/net462/System.Linq.Parallel.dll", "tools/net462/System.Linq.Queryable.dll", "tools/net462/System.Linq.dll", "tools/net462/System.Memory.dll", "tools/net462/System.Net.Http.dll", "tools/net462/System.Net.NameResolution.dll", "tools/net462/System.Net.NetworkInformation.dll", "tools/net462/System.Net.Ping.dll", "tools/net462/System.Net.Primitives.dll", "tools/net462/System.Net.Requests.dll", "tools/net462/System.Net.Security.dll", "tools/net462/System.Net.Sockets.dll", "tools/net462/System.Net.WebHeaderCollection.dll", "tools/net462/System.Net.WebSockets.Client.dll", "tools/net462/System.Net.WebSockets.dll", "tools/net462/System.Numerics.Vectors.dll", "tools/net462/System.ObjectModel.dll", "tools/net462/System.Reflection.Extensions.dll", "tools/net462/System.Reflection.Primitives.dll", "tools/net462/System.Reflection.dll", "tools/net462/System.Resources.Reader.dll", "tools/net462/System.Resources.ResourceManager.dll", "tools/net462/System.Resources.Writer.dll", "tools/net462/System.Runtime.CompilerServices.Unsafe.dll", "tools/net462/System.Runtime.CompilerServices.VisualC.dll", "tools/net462/System.Runtime.Extensions.dll", "tools/net462/System.Runtime.Handles.dll", "tools/net462/System.Runtime.InteropServices.RuntimeInformation.dll", "tools/net462/System.Runtime.InteropServices.dll", "tools/net462/System.Runtime.Numerics.dll", "tools/net462/System.Runtime.Serialization.Formatters.dll", "tools/net462/System.Runtime.Serialization.Json.dll", "tools/net462/System.Runtime.Serialization.Primitives.dll", "tools/net462/System.Runtime.Serialization.Xml.dll", "tools/net462/System.Runtime.dll", "tools/net462/System.Security.Claims.dll", "tools/net462/System.Security.Cryptography.Algorithms.dll", "tools/net462/System.Security.Cryptography.Csp.dll", "tools/net462/System.Security.Cryptography.Encoding.dll", "tools/net462/System.Security.Cryptography.Primitives.dll", "tools/net462/System.Security.Cryptography.X509Certificates.dll", "tools/net462/System.Security.Principal.dll", "tools/net462/System.Security.SecureString.dll", "tools/net462/System.Text.Encoding.Extensions.dll", "tools/net462/System.Text.Encoding.dll", "tools/net462/System.Text.RegularExpressions.dll", "tools/net462/System.Threading.Overlapped.dll", "tools/net462/System.Threading.Tasks.Parallel.dll", "tools/net462/System.Threading.Tasks.dll", "tools/net462/System.Threading.Thread.dll", "tools/net462/System.Threading.ThreadPool.dll", "tools/net462/System.Threading.Timer.dll", "tools/net462/System.Threading.dll", "tools/net462/System.ValueTuple.dll", "tools/net462/System.Xml.ReaderWriter.dll", "tools/net462/System.Xml.XDocument.dll", "tools/net462/System.Xml.XPath.XDocument.dll", "tools/net462/System.Xml.XPath.dll", "tools/net462/System.Xml.XmlDocument.dll", "tools/net462/System.Xml.XmlSerializer.dll", "tools/net462/netstandard.dll", "tools/net9.0/GetDocument.Insider.deps.json", "tools/net9.0/GetDocument.Insider.dll", "tools/net9.0/GetDocument.Insider.exe", "tools/net9.0/GetDocument.Insider.runtimeconfig.json", "tools/net9.0/Microsoft.AspNetCore.Connections.Abstractions.dll", "tools/net9.0/Microsoft.AspNetCore.Connections.Abstractions.xml", "tools/net9.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll", "tools/net9.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.xml", "tools/net9.0/Microsoft.AspNetCore.Http.Features.dll", "tools/net9.0/Microsoft.AspNetCore.Http.Features.xml", "tools/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll", "tools/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "tools/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "tools/net9.0/Microsoft.Extensions.Features.dll", "tools/net9.0/Microsoft.Extensions.Features.xml", "tools/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "tools/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll", "tools/net9.0/Microsoft.Extensions.Logging.Abstractions.dll", "tools/net9.0/Microsoft.Extensions.Options.dll", "tools/net9.0/Microsoft.Extensions.Primitives.dll", "tools/net9.0/Microsoft.Net.Http.Headers.dll", "tools/net9.0/Microsoft.Net.Http.Headers.xml", "tools/net9.0/Microsoft.OpenApi.dll", "tools/netcoreapp2.1/GetDocument.Insider.deps.json", "tools/netcoreapp2.1/GetDocument.Insider.dll", "tools/netcoreapp2.1/GetDocument.Insider.runtimeconfig.json", "tools/netcoreapp2.1/Microsoft.OpenApi.dll", "tools/netcoreapp2.1/System.Diagnostics.DiagnosticSource.dll"]}, "Microsoft.Extensions.Compliance.Abstractions/9.8.0": {"sha512": "PNmBfkdM4GzZRpq40UIO0xPLHIgfZAQ246NGu1S5hOCtIZHBjCREqYpygM6PIGJ+I81QXm4Be058AWym4SmXTQ==", "type": "package", "path": "microsoft.extensions.compliance.abstractions/9.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "lib/net8.0/Microsoft.Extensions.Compliance.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Compliance.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Compliance.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Compliance.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Compliance.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Compliance.Abstractions.xml", "microsoft.extensions.compliance.abstractions.9.8.0.nupkg.sha512", "microsoft.extensions.compliance.abstractions.nuspec"]}, "Microsoft.Extensions.Configuration/10.0.0-preview.7.25380.108": {"sha512": "bqeb/og5BMVpJ0cAfVgHhyNPfQa/uF/t6MhoQ9Z0kAUuOvk+hCfnSKlx2CZUzrkrWN6zK/jRXZW8RsbIxtzexw==", "type": "package", "path": "microsoft.extensions.configuration/10.0.0-preview.7.25380.108", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.targets", "lib/net10.0/Microsoft.Extensions.Configuration.dll", "lib/net10.0/Microsoft.Extensions.Configuration.xml", "lib/net462/Microsoft.Extensions.Configuration.dll", "lib/net462/Microsoft.Extensions.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Configuration.xml", "lib/net9.0/Microsoft.Extensions.Configuration.dll", "lib/net9.0/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.10.0.0-preview.7.25380.108.nupkg.sha512", "microsoft.extensions.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/10.0.0-preview.7.25380.108": {"sha512": "Tos2R09p02UbSg97w7QNr+dNTKuBtmsWKy4+awTnn2d34CXDpsKkAdDis6gLmhuCjz4c0HyB5S0l2ahaqu+u7A==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/10.0.0-preview.7.25380.108", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net10.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net10.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.10.0.0-preview.7.25380.108.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Binder/10.0.0-preview.7.25380.108": {"sha512": "BAcZtIevY+MgiTX9t9iuz8CoYmGPoL1QeAlhNav8EVJmNlhKR/H7nESjxJifejcCRuwQ+dcXlFtHo1xrCMaZWA==", "type": "package", "path": "microsoft.extensions.configuration.binder/10.0.0-preview.7.25380.108", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.dll", "analyzers/dotnet/cs/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/de/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/es/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/it/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets", "lib/net10.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net10.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/net462/Microsoft.Extensions.Configuration.Binder.dll", "lib/net462/Microsoft.Extensions.Configuration.Binder.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.xml", "microsoft.extensions.configuration.binder.10.0.0-preview.7.25380.108.nupkg.sha512", "microsoft.extensions.configuration.binder.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.CommandLine/10.0.0-preview.7.25380.108": {"sha512": "wrRFfwx7avg204vRHD1C7//zo9axtSLqNydbPXVN6r1tpQUsTz2Mbd6QsYMo+zoNUSvFD7fPFZPsG8pkmbX50A==", "type": "package", "path": "microsoft.extensions.configuration.commandline/10.0.0-preview.7.25380.108", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.CommandLine.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.CommandLine.targets", "lib/net10.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net10.0/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/net462/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net462/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/net9.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net9.0/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.xml", "microsoft.extensions.configuration.commandline.10.0.0-preview.7.25380.108.nupkg.sha512", "microsoft.extensions.configuration.commandline.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.EnvironmentVariables/10.0.0-preview.7.25380.108": {"sha512": "bIh84M0Clp94aGE827+cw3Ld3E+LN68Goqk1oyHTAkOMftp3YbkGB/yEHGdydsNEi3kfiLw5lZdu3Nh1Agt9gg==", "type": "package", "path": "microsoft.extensions.configuration.environmentvariables/10.0.0-preview.7.25380.108", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.EnvironmentVariables.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.targets", "lib/net10.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net10.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/net462/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net462/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/net9.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net9.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "microsoft.extensions.configuration.environmentvariables.10.0.0-preview.7.25380.108.nupkg.sha512", "microsoft.extensions.configuration.environmentvariables.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.FileExtensions/10.0.0-preview.7.25380.108": {"sha512": "eGgKM6UgrXTTGNDkPsXE4gofRMK+LPv2PIzMRUOVFLB2iAjoVhSgTTDBiQgVSXZW279FCaoILD8wC7zz5+sZBA==", "type": "package", "path": "microsoft.extensions.configuration.fileextensions/10.0.0-preview.7.25380.108", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.FileExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.FileExtensions.targets", "lib/net10.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net10.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "microsoft.extensions.configuration.fileextensions.10.0.0-preview.7.25380.108.nupkg.sha512", "microsoft.extensions.configuration.fileextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Json/10.0.0-preview.7.25380.108": {"sha512": "vMUtXxczM+wt2rZT3AtDHRG4m0Wzn4R+cTFSr4RDq3VPy1z+qeX+xa+a9Ft73R5ODy/T0N5F1fXxYN6h1fvh6w==", "type": "package", "path": "microsoft.extensions.configuration.json/10.0.0-preview.7.25380.108", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Json.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Json.targets", "lib/net10.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net10.0/Microsoft.Extensions.Configuration.Json.xml", "lib/net462/Microsoft.Extensions.Configuration.Json.dll", "lib/net462/Microsoft.Extensions.Configuration.Json.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Json.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.xml", "microsoft.extensions.configuration.json.10.0.0-preview.7.25380.108.nupkg.sha512", "microsoft.extensions.configuration.json.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.UserSecrets/10.0.0-preview.7.25380.108": {"sha512": "X6M5ARSre9pHhQcrdIoPGCrGe2Xh6iM4AYwJGXRrgG6+blFTc04Iau7tvnjOhCDaEFr2g2cxMJI1wiLlJFOECg==", "type": "package", "path": "microsoft.extensions.configuration.usersecrets/10.0.0-preview.7.25380.108", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/net462/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/net462/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/net8.0/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/net8.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "lib/net10.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net10.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/net462/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net462/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/net9.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net9.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "microsoft.extensions.configuration.usersecrets.10.0.0-preview.7.25380.108.nupkg.sha512", "microsoft.extensions.configuration.usersecrets.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/10.0.0-preview.7.25380.108": {"sha512": "UVMEx1ZOOugXCNxXARPsmtu7B3RzYShFoeGvmO4wA1OmcW8cOSXr7QNTiCTp3uXNrx14daefmDX/BlsExlZyVg==", "type": "package", "path": "microsoft.extensions.dependencyinjection/10.0.0-preview.7.25380.108", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net10.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net10.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.10.0.0-preview.7.25380.108.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/10.0.0-preview.7.25380.108": {"sha512": "0oSQ8o2O8eMxaInqR1GykEzzlerBTN3xQMsEtaWA4zbf1LmrqV7H9ctTTjK4oMeWMCTb9mfYoN9fsVWbAhkTXA==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/10.0.0-preview.7.25380.108", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net10.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net10.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.10.0.0-preview.7.25380.108.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.AutoActivation/9.8.0": {"sha512": "aFPPydGE83MCtxUdjE5iBU7UNLSioB1m/4gWMO9Plh8mXu5umQLPKXwq/Bzd+tSeoKmkzJgqecwRn6C1aXzwGA==", "type": "package", "path": "microsoft.extensions.dependencyinjection.autoactivation/9.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "buildTransitive/net462/Microsoft.Extensions.DependencyInjection.AutoActivation.targets", "buildTransitive/net8.0/_._", "lib/net462/Microsoft.Extensions.DependencyInjection.AutoActivation.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.AutoActivation.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.AutoActivation.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.AutoActivation.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.AutoActivation.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.AutoActivation.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.AutoActivation.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.AutoActivation.xml", "microsoft.extensions.dependencyinjection.autoactivation.9.8.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.autoactivation.nuspec"]}, "Microsoft.Extensions.DependencyModel/9.0.0": {"sha512": "saxr2XzwgDU77LaQfYFXmddEDRUKHF4DaGMZkNB3qjdVSZlax3//dGJagJkKrGMIPNZs2jVFXITyCCR6UHJNdA==", "type": "package", "path": "microsoft.extensions.dependencymodel/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyModel.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyModel.targets", "lib/net462/Microsoft.Extensions.DependencyModel.dll", "lib/net462/Microsoft.Extensions.DependencyModel.xml", "lib/net8.0/Microsoft.Extensions.DependencyModel.dll", "lib/net8.0/Microsoft.Extensions.DependencyModel.xml", "lib/net9.0/Microsoft.Extensions.DependencyModel.dll", "lib/net9.0/Microsoft.Extensions.DependencyModel.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.xml", "microsoft.extensions.dependencymodel.9.0.0.nupkg.sha512", "microsoft.extensions.dependencymodel.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Diagnostics/10.0.0-preview.7.25380.108": {"sha512": "hbUpAah1f1gSx7NT7GKnmnzdQiP6POB7Mo8HNGZQ5qdcsZm6c3e6toK92u4kMkgJ9Cp0mRgL2wIyDfT6ixS/qw==", "type": "package", "path": "microsoft.extensions.diagnostics/10.0.0-preview.7.25380.108", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Diagnostics.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Diagnostics.targets", "lib/net10.0/Microsoft.Extensions.Diagnostics.dll", "lib/net10.0/Microsoft.Extensions.Diagnostics.xml", "lib/net462/Microsoft.Extensions.Diagnostics.dll", "lib/net462/Microsoft.Extensions.Diagnostics.xml", "lib/net8.0/Microsoft.Extensions.Diagnostics.dll", "lib/net8.0/Microsoft.Extensions.Diagnostics.xml", "lib/net9.0/Microsoft.Extensions.Diagnostics.dll", "lib/net9.0/Microsoft.Extensions.Diagnostics.xml", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.dll", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.xml", "microsoft.extensions.diagnostics.10.0.0-preview.7.25380.108.nupkg.sha512", "microsoft.extensions.diagnostics.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Diagnostics.Abstractions/10.0.0-preview.7.25380.108": {"sha512": "4KCRTDM1TUA1+zwUBefsM9vjCRzAjkhLOsITu5X/z8GxdwxvhVRQL3svELII4AwcWG2PUvAqWHIGnjmKlYJmIQ==", "type": "package", "path": "microsoft.extensions.diagnostics.abstractions/10.0.0-preview.7.25380.108", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Diagnostics.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Diagnostics.Abstractions.targets", "lib/net10.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net10.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net462/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net462/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "microsoft.extensions.diagnostics.abstractions.10.0.0-preview.7.25380.108.nupkg.sha512", "microsoft.extensions.diagnostics.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Diagnostics.ExceptionSummarization/9.8.0": {"sha512": "uOFTYKABSr+H5C9V5mxTf0hluApAP+p1JNfELARilqq7kujQtsJpW/wUmFhqRYUuOmh1O7IbWllVXNV1qMtDOQ==", "type": "package", "path": "microsoft.extensions.diagnostics.exceptionsummarization/9.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "buildTransitive/net462/Microsoft.Extensions.Diagnostics.ExceptionSummarization.targets", "buildTransitive/net8.0/_._", "lib/net462/Microsoft.Extensions.Diagnostics.ExceptionSummarization.dll", "lib/net462/Microsoft.Extensions.Diagnostics.ExceptionSummarization.xml", "lib/net8.0/Microsoft.Extensions.Diagnostics.ExceptionSummarization.dll", "lib/net8.0/Microsoft.Extensions.Diagnostics.ExceptionSummarization.xml", "lib/net9.0/Microsoft.Extensions.Diagnostics.ExceptionSummarization.dll", "lib/net9.0/Microsoft.Extensions.Diagnostics.ExceptionSummarization.xml", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.ExceptionSummarization.dll", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.ExceptionSummarization.xml", "microsoft.extensions.diagnostics.exceptionsummarization.9.8.0.nupkg.sha512", "microsoft.extensions.diagnostics.exceptionsummarization.nuspec"]}, "Microsoft.Extensions.FileProviders.Abstractions/10.0.0-preview.7.25380.108": {"sha512": "lvXcu7o7ETZ0SRPTOMhMuzyABFSiTwQdye1ATqdQH4u0n46s0QtTMN8u8zrHdIDtU5zv8hv3PqnOmmQt6lMzEQ==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/10.0.0-preview.7.25380.108", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Abstractions.targets", "lib/net10.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net10.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.10.0.0-preview.7.25380.108.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Physical/10.0.0-preview.7.25380.108": {"sha512": "CCWJM/f0hEldsKdB+QXJpcyELI0VRHbIuWWejCk9pNQBTuPOJHX5Lxsn4Lt8d30DGosTfkAQWLpy/wh+TGT6xA==", "type": "package", "path": "microsoft.extensions.fileproviders.physical/10.0.0-preview.7.25380.108", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Physical.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Physical.targets", "lib/net10.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net10.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net462/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net462/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net9.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.xml", "microsoft.extensions.fileproviders.physical.10.0.0-preview.7.25380.108.nupkg.sha512", "microsoft.extensions.fileproviders.physical.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileSystemGlobbing/10.0.0-preview.7.25380.108": {"sha512": "HsDTy1yAhOyznpgtbaUpiHojeMDrmkwAI/iW6G9PksLfhJAFXT1PffKoWMG2+fS4rWo6yoaro2cmJH0RYBKJ0Q==", "type": "package", "path": "microsoft.extensions.filesystemglobbing/10.0.0-preview.7.25380.108", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileSystemGlobbing.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileSystemGlobbing.targets", "lib/net10.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net10.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.xml", "microsoft.extensions.filesystemglobbing.10.0.0-preview.7.25380.108.nupkg.sha512", "microsoft.extensions.filesystemglobbing.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Hosting/10.0.0-preview.7.25380.108": {"sha512": "VcqnTGY8bMnFxZQb3cjcXnXy4x7dJgp85UlujErNh34xh4ZislQ2XhX7ak0gUnRh8c9ZTI3xj4groyqBl/DNHA==", "type": "package", "path": "microsoft.extensions.hosting/10.0.0-preview.7.25380.108", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Hosting.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Hosting.targets", "lib/net10.0/Microsoft.Extensions.Hosting.dll", "lib/net10.0/Microsoft.Extensions.Hosting.xml", "lib/net462/Microsoft.Extensions.Hosting.dll", "lib/net462/Microsoft.Extensions.Hosting.xml", "lib/net8.0/Microsoft.Extensions.Hosting.dll", "lib/net8.0/Microsoft.Extensions.Hosting.xml", "lib/net9.0/Microsoft.Extensions.Hosting.dll", "lib/net9.0/Microsoft.Extensions.Hosting.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.xml", "microsoft.extensions.hosting.10.0.0-preview.7.25380.108.nupkg.sha512", "microsoft.extensions.hosting.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Hosting.Abstractions/10.0.0-preview.7.25380.108": {"sha512": "J6zRebs9tVxWat6q3Z8v1fknqYhUbyUVoZYiGqhu7g+ChVYeCdp/YL2qSvC/Ap/KThXu6+C+R40gdDPCjUq5EQ==", "type": "package", "path": "microsoft.extensions.hosting.abstractions/10.0.0-preview.7.25380.108", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Hosting.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Hosting.Abstractions.targets", "lib/net10.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net10.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.xml", "microsoft.extensions.hosting.abstractions.10.0.0-preview.7.25380.108.nupkg.sha512", "microsoft.extensions.hosting.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Http.Diagnostics/9.8.0": {"sha512": "uRb8lu0tNmE8OO6Zq/SfQg+pv9q67g8l2kadMEqpXTy2qw9+6+rBpgE8MU/rTgPd7iOOlQThY65CABD/7f3dow==", "type": "package", "path": "microsoft.extensions.http.diagnostics/9.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "buildTransitive/net462/Microsoft.Extensions.Http.Diagnostics.targets", "buildTransitive/net8.0/_._", "lib/net462/Microsoft.Extensions.Http.Diagnostics.dll", "lib/net462/Microsoft.Extensions.Http.Diagnostics.xml", "lib/net8.0/Microsoft.Extensions.Http.Diagnostics.dll", "lib/net8.0/Microsoft.Extensions.Http.Diagnostics.xml", "lib/net9.0/Microsoft.Extensions.Http.Diagnostics.dll", "lib/net9.0/Microsoft.Extensions.Http.Diagnostics.xml", "lib/netstandard2.0/Microsoft.Extensions.Http.Diagnostics.dll", "lib/netstandard2.0/Microsoft.Extensions.Http.Diagnostics.xml", "microsoft.extensions.http.diagnostics.9.8.0.nupkg.sha512", "microsoft.extensions.http.diagnostics.nuspec"]}, "Microsoft.Extensions.Http.Resilience/9.8.0": {"sha512": "ruZA0njkKRk2W6V0OqfC+rgKFe0pT/fl7MOYaGoSVAZgjW8r0wrrsEp2c+YqOXGhQfAuyyvZq1Wg1EiL3+0ncQ==", "type": "package", "path": "microsoft.extensions.http.resilience/9.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "buildTransitive/net462/Microsoft.Extensions.Http.Resilience.net462.targets", "buildTransitive/net462/Microsoft.Extensions.Http.Resilience.targets", "buildTransitive/net8.0/Microsoft.Extensions.Http.Resilience.targets", "lib/net462/Microsoft.Extensions.Http.Resilience.dll", "lib/net462/Microsoft.Extensions.Http.Resilience.xml", "lib/net8.0/Microsoft.Extensions.Http.Resilience.dll", "lib/net8.0/Microsoft.Extensions.Http.Resilience.xml", "lib/net9.0/Microsoft.Extensions.Http.Resilience.dll", "lib/net9.0/Microsoft.Extensions.Http.Resilience.xml", "lib/netstandard2.0/Microsoft.Extensions.Http.Resilience.dll", "lib/netstandard2.0/Microsoft.Extensions.Http.Resilience.xml", "microsoft.extensions.http.resilience.9.8.0.nupkg.sha512", "microsoft.extensions.http.resilience.nuspec"]}, "Microsoft.Extensions.Logging/10.0.0-preview.7.25380.108": {"sha512": "vktcXpKfaF3TEC5OoAv2ySdImeBErcbFwUduuFIGvKOdYGw1B1N8ZpG5GLxApMXFgygdMcAFrkotDWzOF1npUA==", "type": "package", "path": "microsoft.extensions.logging/10.0.0-preview.7.25380.108", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets", "lib/net10.0/Microsoft.Extensions.Logging.dll", "lib/net10.0/Microsoft.Extensions.Logging.xml", "lib/net462/Microsoft.Extensions.Logging.dll", "lib/net462/Microsoft.Extensions.Logging.xml", "lib/net8.0/Microsoft.Extensions.Logging.dll", "lib/net8.0/Microsoft.Extensions.Logging.xml", "lib/net9.0/Microsoft.Extensions.Logging.dll", "lib/net9.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.10.0.0-preview.7.25380.108.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/10.0.0-preview.7.25380.108": {"sha512": "qfx62EG7wvjym/GusNX+kzqPByIVYiXyqBYaObQlIch5YukiVd4ovUJHALYt11jpcxpv9nmjgmqahIluf2j5xA==", "type": "package", "path": "microsoft.extensions.logging.abstractions/10.0.0-preview.7.25380.108", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net10.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net10.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.10.0.0-preview.7.25380.108.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Configuration/10.0.0-preview.7.25380.108": {"sha512": "nfivpgjltKnYWCT62WtNgaRyyTv92nRYkqJabEQIvmtlQrjuYNb/kiECy74+jd4t4AQPx3hlPn9hWiLGvIEHhg==", "type": "package", "path": "microsoft.extensions.logging.configuration/10.0.0-preview.7.25380.108", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Configuration.targets", "lib/net10.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/net10.0/Microsoft.Extensions.Logging.Configuration.xml", "lib/net462/Microsoft.Extensions.Logging.Configuration.dll", "lib/net462/Microsoft.Extensions.Logging.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Logging.Configuration.xml", "lib/net9.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/net9.0/Microsoft.Extensions.Logging.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.xml", "microsoft.extensions.logging.configuration.10.0.0-preview.7.25380.108.nupkg.sha512", "microsoft.extensions.logging.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Console/10.0.0-preview.7.25380.108": {"sha512": "VfOeXETfctJamiTwovg1SmyfEboi8klawzpE6hv45f8KPajn/PPTuYRCQjaQWiP0praYdAJWrV4TjvDF0+xxeg==", "type": "package", "path": "microsoft.extensions.logging.console/10.0.0-preview.7.25380.108", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Console.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Console.targets", "lib/net10.0/Microsoft.Extensions.Logging.Console.dll", "lib/net10.0/Microsoft.Extensions.Logging.Console.xml", "lib/net462/Microsoft.Extensions.Logging.Console.dll", "lib/net462/Microsoft.Extensions.Logging.Console.xml", "lib/net8.0/Microsoft.Extensions.Logging.Console.dll", "lib/net8.0/Microsoft.Extensions.Logging.Console.xml", "lib/net9.0/Microsoft.Extensions.Logging.Console.dll", "lib/net9.0/Microsoft.Extensions.Logging.Console.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Console.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Console.xml", "microsoft.extensions.logging.console.10.0.0-preview.7.25380.108.nupkg.sha512", "microsoft.extensions.logging.console.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Debug/10.0.0-preview.7.25380.108": {"sha512": "R1XFfHifpXKXtIGDt/QrvwnqPEOMEMdDppBMVjb5UpI3RSBKriTWdKeaJIcc1gx6e56aVO2xOT3EtfnK6Xb3Ig==", "type": "package", "path": "microsoft.extensions.logging.debug/10.0.0-preview.7.25380.108", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Debug.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Debug.targets", "lib/net10.0/Microsoft.Extensions.Logging.Debug.dll", "lib/net10.0/Microsoft.Extensions.Logging.Debug.xml", "lib/net462/Microsoft.Extensions.Logging.Debug.dll", "lib/net462/Microsoft.Extensions.Logging.Debug.xml", "lib/net8.0/Microsoft.Extensions.Logging.Debug.dll", "lib/net8.0/Microsoft.Extensions.Logging.Debug.xml", "lib/net9.0/Microsoft.Extensions.Logging.Debug.dll", "lib/net9.0/Microsoft.Extensions.Logging.Debug.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.xml", "microsoft.extensions.logging.debug.10.0.0-preview.7.25380.108.nupkg.sha512", "microsoft.extensions.logging.debug.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.EventLog/10.0.0-preview.7.25380.108": {"sha512": "pa6ggb9nqfU+DPLI2NXRaUDmkrw3mvmXcnh4NQrgcvzNQPB1PW8HySNm+KvzX/nK/UmPtJEa3NbOhmpDBdrbMA==", "type": "package", "path": "microsoft.extensions.logging.eventlog/10.0.0-preview.7.25380.108", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.EventLog.targets", "lib/net10.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/net10.0/Microsoft.Extensions.Logging.EventLog.xml", "lib/net462/Microsoft.Extensions.Logging.EventLog.dll", "lib/net462/Microsoft.Extensions.Logging.EventLog.xml", "lib/net8.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/net8.0/Microsoft.Extensions.Logging.EventLog.xml", "lib/net9.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/net9.0/Microsoft.Extensions.Logging.EventLog.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventLog.xml", "microsoft.extensions.logging.eventlog.10.0.0-preview.7.25380.108.nupkg.sha512", "microsoft.extensions.logging.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.EventSource/10.0.0-preview.7.25380.108": {"sha512": "K0Qydvu3fnotw1kynMz6kDt8FX+VnKxOjX1lVFjKKkyHroHitpz8YUOuA+TlbXQFZQyv3bO7nDimKVkcvPFcVQ==", "type": "package", "path": "microsoft.extensions.logging.eventsource/10.0.0-preview.7.25380.108", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.EventSource.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.EventSource.targets", "lib/net10.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/net10.0/Microsoft.Extensions.Logging.EventSource.xml", "lib/net462/Microsoft.Extensions.Logging.EventSource.dll", "lib/net462/Microsoft.Extensions.Logging.EventSource.xml", "lib/net8.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/net8.0/Microsoft.Extensions.Logging.EventSource.xml", "lib/net9.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/net9.0/Microsoft.Extensions.Logging.EventSource.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventSource.xml", "microsoft.extensions.logging.eventsource.10.0.0-preview.7.25380.108.nupkg.sha512", "microsoft.extensions.logging.eventsource.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options/10.0.0-preview.7.25380.108": {"sha512": "kG3XdCpBiMSxPsAeMCy8YhHpe3sUlSoBGuZQfTcC/VaWKrvpQ5OrbhBCfb/SOPLzWexijSsDwtgjYenRvqE91Q==", "type": "package", "path": "microsoft.extensions.options/10.0.0-preview.7.25380.108", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/Microsoft.Extensions.Options.targets", "buildTransitive/net8.0/Microsoft.Extensions.Options.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets", "lib/net10.0/Microsoft.Extensions.Options.dll", "lib/net10.0/Microsoft.Extensions.Options.xml", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net8.0/Microsoft.Extensions.Options.dll", "lib/net8.0/Microsoft.Extensions.Options.xml", "lib/net9.0/Microsoft.Extensions.Options.dll", "lib/net9.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.10.0.0-preview.7.25380.108.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options.ConfigurationExtensions/10.0.0-preview.7.25380.108": {"sha512": "z05jUKh7+6FTFE+Obdg6wEQqvdxNf+ty5YUZ9VjNyzYcoN9hZMbX7RucTifl4CZCJXadlv79C7ZCSmHIK4x0pw==", "type": "package", "path": "microsoft.extensions.options.configurationextensions/10.0.0-preview.7.25380.108", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Options.ConfigurationExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.ConfigurationExtensions.targets", "lib/net10.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net10.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net462/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net462/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "microsoft.extensions.options.configurationextensions.10.0.0-preview.7.25380.108.nupkg.sha512", "microsoft.extensions.options.configurationextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/10.0.0-preview.7.25380.108": {"sha512": "0oC37fX0irgi29vN8JQdvXn1jZI6upCIs8Q8J+loetDrWrB3fwYBqbUdz/mMS41G+kkjCSaGRfZM8dBiZqqMpw==", "type": "package", "path": "microsoft.extensions.primitives/10.0.0-preview.7.25380.108", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net10.0/Microsoft.Extensions.Primitives.dll", "lib/net10.0/Microsoft.Extensions.Primitives.xml", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/net9.0/Microsoft.Extensions.Primitives.dll", "lib/net9.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.10.0.0-preview.7.25380.108.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Resilience/9.8.0": {"sha512": "GLgn4TR+9QaDquaLR94tgsvSftXOJaBiepqFsWQV9VBH6CFK2hY6oTSudyIDYub+qAszMdgDxW9FwFj1tkXyxA==", "type": "package", "path": "microsoft.extensions.resilience/9.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "buildTransitive/net462/Microsoft.Extensions.Resilience.targets", "buildTransitive/net8.0/_._", "lib/net462/Microsoft.Extensions.Resilience.dll", "lib/net462/Microsoft.Extensions.Resilience.xml", "lib/net8.0/Microsoft.Extensions.Resilience.dll", "lib/net8.0/Microsoft.Extensions.Resilience.xml", "lib/net9.0/Microsoft.Extensions.Resilience.dll", "lib/net9.0/Microsoft.Extensions.Resilience.xml", "lib/netstandard2.0/Microsoft.Extensions.Resilience.dll", "lib/netstandard2.0/Microsoft.Extensions.Resilience.xml", "microsoft.extensions.resilience.9.8.0.nupkg.sha512", "microsoft.extensions.resilience.nuspec"]}, "Microsoft.Extensions.ServiceDiscovery/9.4.1": {"sha512": "+pWd7AwksTkDSvWBlfFzC2z9giDMYpRhV6ymoPcOgjNbMPV9ceqQ1Lt+vJ2OYB/g7n4qNfUk527XBew9EnRXKA==", "type": "package", "path": "microsoft.extensions.servicediscovery/9.4.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net8.0/Microsoft.Extensions.ServiceDiscovery.dll", "lib/net8.0/Microsoft.Extensions.ServiceDiscovery.xml", "microsoft.extensions.servicediscovery.9.4.1.nupkg.sha512", "microsoft.extensions.servicediscovery.nuspec"]}, "Microsoft.Extensions.ServiceDiscovery.Abstractions/9.4.1": {"sha512": "FLoauVK9R8BWAqFIIUsQF+31TwNqG/h7TSUJR17ZebjmK3Ee1FNrHYYERO853NwLSYG6iEr5V+HZd6b9oj8pIw==", "type": "package", "path": "microsoft.extensions.servicediscovery.abstractions/9.4.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net8.0/Microsoft.Extensions.ServiceDiscovery.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.ServiceDiscovery.Abstractions.xml", "microsoft.extensions.servicediscovery.abstractions.9.4.1.nupkg.sha512", "microsoft.extensions.servicediscovery.abstractions.nuspec"]}, "Microsoft.Extensions.Telemetry/9.8.0": {"sha512": "OAQw8rUlx5UdN0tpT0n0QwtwYb3tI605hoMx0TrXJowuj7xnvvxnnTiBQS8IZUldd9tqb73I0fAKk5yopTYU3w==", "type": "package", "path": "microsoft.extensions.telemetry/9.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "buildTransitive/net462/Microsoft.Extensions.Telemetry.targets", "buildTransitive/net8.0/_._", "lib/net462/Microsoft.Extensions.Telemetry.dll", "lib/net462/Microsoft.Extensions.Telemetry.xml", "lib/net8.0/Microsoft.Extensions.Telemetry.dll", "lib/net8.0/Microsoft.Extensions.Telemetry.xml", "lib/net9.0/Microsoft.Extensions.Telemetry.dll", "lib/net9.0/Microsoft.Extensions.Telemetry.xml", "lib/netstandard2.0/Microsoft.Extensions.Telemetry.dll", "lib/netstandard2.0/Microsoft.Extensions.Telemetry.xml", "microsoft.extensions.telemetry.9.8.0.nupkg.sha512", "microsoft.extensions.telemetry.nuspec"]}, "Microsoft.Extensions.Telemetry.Abstractions/9.8.0": {"sha512": "kO2q2nzlaKZLi3eYa1nRPsbWms56RjQCFQalvT92Zt9j89PPxsyhdT++9s0aLKWDe3eVZpjY1329ZERXvW/1Dg==", "type": "package", "path": "microsoft.extensions.telemetry.abstractions/9.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "analyzers/dotnet/cs/Microsoft.Gen.Logging.dll", "analyzers/dotnet/cs/Microsoft.Gen.Metrics.dll", "buildTransitive/net462/Microsoft.Extensions.Telemetry.Abstractions.targets", "buildTransitive/net6.0/Microsoft.Extensions.Telemetry.Abstractions.props", "buildTransitive/net6.0/Microsoft.Extensions.Telemetry.Abstractions.targets", "buildTransitive/net8.0/Microsoft.Extensions.Telemetry.Abstractions.props", "buildTransitive/net8.0/Microsoft.Extensions.Telemetry.Abstractions.targets", "lib/net462/Microsoft.Extensions.Telemetry.Abstractions.dll", "lib/net462/Microsoft.Extensions.Telemetry.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Telemetry.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Telemetry.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Telemetry.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Telemetry.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Telemetry.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Telemetry.Abstractions.xml", "microsoft.extensions.telemetry.abstractions.9.8.0.nupkg.sha512", "microsoft.extensions.telemetry.abstractions.nuspec"]}, "Microsoft.Identity.Client/4.73.1": {"sha512": "NnDLS8QwYqO5ZZecL2oioi1LUqjh5Ewk4bMLzbgiXJbQmZhDLtKwLxL3DpGMlQAJ2G4KgEnvGPKa+OOgffeJbw==", "type": "package", "path": "microsoft.identity.client/4.73.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.Identity.Client.dll", "lib/net462/Microsoft.Identity.Client.xml", "lib/net472/Microsoft.Identity.Client.dll", "lib/net472/Microsoft.Identity.Client.xml", "lib/net8.0-android34.0/Microsoft.Identity.Client.aar", "lib/net8.0-android34.0/Microsoft.Identity.Client.dll", "lib/net8.0-android34.0/Microsoft.Identity.Client.xml", "lib/net8.0-ios18.0/Microsoft.Identity.Client.dll", "lib/net8.0-ios18.0/Microsoft.Identity.Client.xml", "lib/net8.0/Microsoft.Identity.Client.dll", "lib/net8.0/Microsoft.Identity.Client.xml", "lib/netstandard2.0/Microsoft.Identity.Client.dll", "lib/netstandard2.0/Microsoft.Identity.Client.xml", "microsoft.identity.client.4.73.1.nupkg.sha512", "microsoft.identity.client.nuspec"]}, "Microsoft.Identity.Client.Extensions.Msal/4.73.1": {"sha512": "xDztAiV2F0wI0W8FLKv5cbaBefyLD6JVaAsvgSN7bjWNCzGYzHbcOEIP5s4TJXUpQzMfUyBsFl1mC6Zmgpz0PQ==", "type": "package", "path": "microsoft.identity.client.extensions.msal/4.73.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net8.0/Microsoft.Identity.Client.Extensions.Msal.dll", "lib/net8.0/Microsoft.Identity.Client.Extensions.Msal.xml", "lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.dll", "lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.xml", "microsoft.identity.client.extensions.msal.4.73.1.nupkg.sha512", "microsoft.identity.client.extensions.msal.nuspec"]}, "Microsoft.IdentityModel.Abstractions/8.0.1": {"sha512": "OtlIWcyX01olfdevPKZdIPfBEvbcioDyBiE/Z2lHsopsMD7twcKtlN9kMevHmI5IIPhFpfwCIiR6qHQz1WHUIw==", "type": "package", "path": "microsoft.identitymodel.abstractions/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/Microsoft.IdentityModel.Abstractions.dll", "lib/net462/Microsoft.IdentityModel.Abstractions.xml", "lib/net472/Microsoft.IdentityModel.Abstractions.dll", "lib/net472/Microsoft.IdentityModel.Abstractions.xml", "lib/net6.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net6.0/Microsoft.IdentityModel.Abstractions.xml", "lib/net8.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net8.0/Microsoft.IdentityModel.Abstractions.xml", "lib/net9.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net9.0/Microsoft.IdentityModel.Abstractions.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.xml", "microsoft.identitymodel.abstractions.8.0.1.nupkg.sha512", "microsoft.identitymodel.abstractions.nuspec"]}, "Microsoft.IdentityModel.JsonWebTokens/8.0.1": {"sha512": "s6++gF9x0rQApQzOBbSyp4jUaAlwm+DroKfL8gdOHxs83k8SJfUXhuc46rDB3rNXBQ1MVRxqKUrqFhO/M0E97g==", "type": "package", "path": "microsoft.identitymodel.jsonwebtokens/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net462/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.xml", "microsoft.identitymodel.jsonwebtokens.8.0.1.nupkg.sha512", "microsoft.identitymodel.jsonwebtokens.nuspec"]}, "Microsoft.IdentityModel.Logging/8.0.1": {"sha512": "UCPF2exZqBXe7v/6sGNiM6zCQOUXXQ9+v5VTb9gPB8ZSUPnX53BxlN78v2jsbIvK9Dq4GovQxo23x8JgWvm/Qg==", "type": "package", "path": "microsoft.identitymodel.logging/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/Microsoft.IdentityModel.Logging.dll", "lib/net462/Microsoft.IdentityModel.Logging.xml", "lib/net472/Microsoft.IdentityModel.Logging.dll", "lib/net472/Microsoft.IdentityModel.Logging.xml", "lib/net6.0/Microsoft.IdentityModel.Logging.dll", "lib/net6.0/Microsoft.IdentityModel.Logging.xml", "lib/net8.0/Microsoft.IdentityModel.Logging.dll", "lib/net8.0/Microsoft.IdentityModel.Logging.xml", "lib/net9.0/Microsoft.IdentityModel.Logging.dll", "lib/net9.0/Microsoft.IdentityModel.Logging.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.xml", "microsoft.identitymodel.logging.8.0.1.nupkg.sha512", "microsoft.identitymodel.logging.nuspec"]}, "Microsoft.IdentityModel.Protocols/8.0.1": {"sha512": "uA2vpKqU3I2mBBEaeJAWPTjT9v1TZrGWKdgK6G5qJd03CLx83kdiqO9cmiK8/n1erkHzFBwU/RphP83aAe3i3g==", "type": "package", "path": "microsoft.identitymodel.protocols/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/Microsoft.IdentityModel.Protocols.dll", "lib/net462/Microsoft.IdentityModel.Protocols.xml", "lib/net472/Microsoft.IdentityModel.Protocols.dll", "lib/net472/Microsoft.IdentityModel.Protocols.xml", "lib/net6.0/Microsoft.IdentityModel.Protocols.dll", "lib/net6.0/Microsoft.IdentityModel.Protocols.xml", "lib/net8.0/Microsoft.IdentityModel.Protocols.dll", "lib/net8.0/Microsoft.IdentityModel.Protocols.xml", "lib/net9.0/Microsoft.IdentityModel.Protocols.dll", "lib/net9.0/Microsoft.IdentityModel.Protocols.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.xml", "microsoft.identitymodel.protocols.8.0.1.nupkg.sha512", "microsoft.identitymodel.protocols.nuspec"]}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.0.1": {"sha512": "AQDbfpL+yzuuGhO/mQhKNsp44pm5Jv8/BI4KiFXR7beVGZoSH35zMV3PrmcfvSTsyI6qrcR898NzUauD6SRigg==", "type": "package", "path": "microsoft.identitymodel.protocols.openidconnect/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net462/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net9.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net9.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "microsoft.identitymodel.protocols.openidconnect.8.0.1.nupkg.sha512", "microsoft.identitymodel.protocols.openidconnect.nuspec"]}, "Microsoft.IdentityModel.Tokens/8.0.1": {"sha512": "kDimB6Dkd3nkW2oZPDkMkVHfQt3IDqO5gL0oa8WVy3OP4uE8Ij+8TXnqg9TOd9ufjsY3IDiGz7pCUbnfL18tjg==", "type": "package", "path": "microsoft.identitymodel.tokens/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/Microsoft.IdentityModel.Tokens.dll", "lib/net462/Microsoft.IdentityModel.Tokens.xml", "lib/net472/Microsoft.IdentityModel.Tokens.dll", "lib/net472/Microsoft.IdentityModel.Tokens.xml", "lib/net6.0/Microsoft.IdentityModel.Tokens.dll", "lib/net6.0/Microsoft.IdentityModel.Tokens.xml", "lib/net8.0/Microsoft.IdentityModel.Tokens.dll", "lib/net8.0/Microsoft.IdentityModel.Tokens.xml", "lib/net9.0/Microsoft.IdentityModel.Tokens.dll", "lib/net9.0/Microsoft.IdentityModel.Tokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.xml", "microsoft.identitymodel.tokens.8.0.1.nupkg.sha512", "microsoft.identitymodel.tokens.nuspec"]}, "Microsoft.OpenApi/1.6.24": {"sha512": "fZhncz3BmI2pJk589qHvvR3gGYMol5u98Yk9zRQiDjZac8Uq5NG/dYEFJbW/DBvJf5e8utXt5fN/58XCgjNqyQ==", "type": "package", "path": "microsoft.openapi/1.6.24", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/netstandard2.0/Microsoft.OpenApi.dll", "lib/netstandard2.0/Microsoft.OpenApi.pdb", "lib/netstandard2.0/Microsoft.OpenApi.xml", "microsoft.openapi.1.6.24.nupkg.sha512", "microsoft.openapi.nuspec"]}, "Microsoft.SqlServer.Server/1.0.0": {"sha512": "N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "type": "package", "path": "microsoft.sqlserver.server/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "dotnet.png", "lib/net46/Microsoft.SqlServer.Server.dll", "lib/net46/Microsoft.SqlServer.Server.pdb", "lib/net46/Microsoft.SqlServer.Server.xml", "lib/netstandard2.0/Microsoft.SqlServer.Server.dll", "lib/netstandard2.0/Microsoft.SqlServer.Server.pdb", "lib/netstandard2.0/Microsoft.SqlServer.Server.xml", "microsoft.sqlserver.server.1.0.0.nupkg.sha512", "microsoft.sqlserver.server.nuspec"]}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets/1.22.1": {"sha512": "EfYANhAWqmWKoLwN6bxoiPZSOfJSO9lzX+UrU6GVhLhPub1Hd+5f0zL0/tggIA6mRz6Ebw2xCNcIsM4k+7NPng==", "type": "package", "path": "microsoft.visualstudio.azure.containers.tools.targets/1.22.1", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "EULA.md", "ThirdPartyNotices.txt", "build/Container.props", "build/Container.targets", "build/Microsoft.VisualStudio.Azure.Containers.Tools.Targets.props", "build/Microsoft.VisualStudio.Azure.Containers.Tools.Targets.targets", "build/Rules/GeneralBrowseObject.xaml", "build/Rules/cs-CZ/GeneralBrowseObject.xaml", "build/Rules/de-DE/GeneralBrowseObject.xaml", "build/Rules/es-ES/GeneralBrowseObject.xaml", "build/Rules/fr-FR/GeneralBrowseObject.xaml", "build/Rules/it-IT/GeneralBrowseObject.xaml", "build/Rules/ja-JP/GeneralBrowseObject.xaml", "build/Rules/ko-KR/GeneralBrowseObject.xaml", "build/Rules/pl-PL/GeneralBrowseObject.xaml", "build/Rules/pt-BR/GeneralBrowseObject.xaml", "build/Rules/ru-RU/GeneralBrowseObject.xaml", "build/Rules/tr-TR/GeneralBrowseObject.xaml", "build/Rules/zh-CN/GeneralBrowseObject.xaml", "build/Rules/zh-TW/GeneralBrowseObject.xaml", "build/ToolsTarget.props", "build/ToolsTarget.targets", "icon.png", "microsoft.visualstudio.azure.containers.tools.targets.1.22.1.nupkg.sha512", "microsoft.visualstudio.azure.containers.tools.targets.nuspec", "tools/Microsoft.VisualStudio.Containers.Tools.Common.dll", "tools/Microsoft.VisualStudio.Containers.Tools.Shared.dll", "tools/Microsoft.VisualStudio.Containers.Tools.Tasks.dll", "tools/Newtonsoft.Json.dll", "tools/System.Security.Principal.Windows.dll", "tools/cs/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/cs/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/cs/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/de/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/de/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/de/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/es/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/es/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/es/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/fr/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/fr/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/fr/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/it/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/it/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/it/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/ja/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/ja/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/ja/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/ko/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/ko/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/ko/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/pl/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/pl/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/pl/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/pt-BR/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/pt-BR/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/pt-BR/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/ru/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/ru/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/ru/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/tr/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/tr/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/tr/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/zh-Hans/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/zh-Hans/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/zh-Hans/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/zh-Hant/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/zh-Hant/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/zh-Hant/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll"]}, "MiniProfiler.AspNetCore/4.5.4": {"sha512": "meedJsjpYOeHPhE8H6t+dGQ9zLxcCQVpi4DXzmxmYAXywmTzlo6jv2IASUv5QijTU0CxsROln3FHd8RsTO8Z8A==", "type": "package", "path": "miniprofiler.aspnetcore/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net8.0/MiniProfiler.AspNetCore.dll", "lib/net8.0/MiniProfiler.AspNetCore.xml", "miniprofiler.aspnetcore.4.5.4.nupkg.sha512", "miniprofiler.aspnetcore.nuspec"]}, "MiniProfiler.AspNetCore.Mvc/4.5.4": {"sha512": "+NqXyCy9aNdroPm6leW5+cpngtCnkCdoyOlJzvVN62uucSx+MYkx8jmKbgAt+aCP6aghADfHBExwrTIldHxapg==", "type": "package", "path": "miniprofiler.aspnetcore.mvc/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net8.0/MiniProfiler.AspNetCore.Mvc.dll", "lib/net8.0/MiniProfiler.AspNetCore.Mvc.xml", "miniprofiler.aspnetcore.mvc.4.5.4.nupkg.sha512", "miniprofiler.aspnetcore.mvc.nuspec"]}, "MiniProfiler.Shared/4.5.4": {"sha512": "f8ckFm/xTS8C2Bn4BdVc94dNvg+tRfk0e4XFaETOqRi6r0PUOyn3Z9jTQCVpB3R1pP5WiRsEIrqqxux95BVpTA==", "type": "package", "path": "miniprofiler.shared/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net472/MiniProfiler.Shared.dll", "lib/net472/MiniProfiler.Shared.xml", "lib/net8.0/MiniProfiler.Shared.dll", "lib/net8.0/MiniProfiler.Shared.xml", "lib/netstandard2.0/MiniProfiler.Shared.dll", "lib/netstandard2.0/MiniProfiler.Shared.xml", "miniprofiler.shared.4.5.4.nupkg.sha512", "miniprofiler.shared.nuspec"]}, "MySql.Data/9.1.0": {"sha512": "E4t/IQzcXg4nYGqrGkoGwwSWA1V2L+LKzVddPABAPcj2i6RESP2fcZQ4XFC0Wv+Cq4DlgR3DYhX/fGaZ3VxCPQ==", "type": "package", "path": "mysql.data/9.1.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "README", "README.md", "lib/net462/MySql.Data.dll", "lib/net462/MySql.Data.xml", "lib/net48/MySql.Data.dll", "lib/net48/MySql.Data.xml", "lib/net6.0/MySql.Data.dll", "lib/net6.0/MySql.Data.xml", "lib/net8.0/MySql.Data.dll", "lib/net8.0/MySql.Data.xml", "lib/netstandard2.0/MySql.Data.dll", "lib/netstandard2.0/MySql.Data.xml", "lib/netstandard2.1/MySql.Data.dll", "lib/netstandard2.1/MySql.Data.xml", "logo-mysql-170x115.png", "mysql.data.9.1.0.nupkg.sha512", "mysql.data.nuspec", "runtimes/win-x64/native/comerr64.dll", "runtimes/win-x64/native/gssapi64.dll", "runtimes/win-x64/native/k5sprt64.dll", "runtimes/win-x64/native/krb5_64.dll", "runtimes/win-x64/native/krbcc64.dll"]}, "NetTopologySuite/2.0.0": {"sha512": "3ajBClEI9wx2/DjjGmV52sHW1m52vLg8sdz1pJbTf5ySj1X90qehQs3v1DRwGo0F8UKj/Z2SjNhRN/6LroAkqg==", "type": "package", "path": "nettopologysuite/2.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/NetTopologySuite.dll", "lib/netstandard2.0/NetTopologySuite.xml", "nettopologysuite.2.0.0.nupkg.sha512", "nettopologysuite.nuspec"]}, "NetTopologySuite.IO.PostGis/2.1.0": {"sha512": "3W8XTFz8iP6GQ5jDXK1/LANHiU+988k1kmmuPWNKcJLpmSg6CvFpbTpz+s4+LBzkAp64wHGOldSlkSuzYfrIKA==", "type": "package", "path": "nettopologysuite.io.postgis/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/NetTopologySuite.IO.PostGis.dll", "lib/netstandard2.0/NetTopologySuite.IO.PostGis.xml", "lib/netstandard2.1/NetTopologySuite.IO.PostGis.dll", "lib/netstandard2.1/NetTopologySuite.IO.PostGis.xml", "nettopologysuite.io.postgis.2.1.0.nupkg.sha512", "nettopologysuite.io.postgis.nuspec"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "Npgsql/9.0.3": {"sha512": "tPvY61CxOAWxNsKLEBg+oR646X4Bc8UmyQ/tJszL/7mEmIXQnnBhVJZrZEEUv0Bstu0mEsHZD5At3EO8zQRAYw==", "type": "package", "path": "npgsql/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net6.0/Npgsql.dll", "lib/net6.0/Npgsql.xml", "lib/net8.0/Npgsql.dll", "lib/net8.0/Npgsql.xml", "npgsql.9.0.3.nupkg.sha512", "npgsql.nuspec", "postgresql.png"]}, "Npgsql.LegacyPostgis/5.0.18": {"sha512": "eVWTnaY8SbFBMpugVhQt9nQMFKLGM4JcgNzc3zVDiO+PJMFEa1AYF20V7tiOwduWhH0T4mVP0zPAl2QvUjcDnA==", "type": "package", "path": "npgsql.legacypostgis/5.0.18", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Npgsql.LegacyPostgis.dll", "lib/netstandard2.0/Npgsql.LegacyPostgis.xml", "npgsql.legacypostgis.5.0.18.nupkg.sha512", "npgsql.legacypostgis.nuspec", "postgresql.png"]}, "Npgsql.NetTopologySuite/5.0.18": {"sha512": "tgqYADHGD6uwjibPe6m1CHVE2DJvMFv55hbtdEjwPIIjUKNWP2YUMQUNSeRpZNipjRcUOoAFEZvwAXizNKM8IA==", "type": "package", "path": "npgsql.nettopologysuite/5.0.18", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Npgsql.NetTopologySuite.dll", "lib/netstandard2.0/Npgsql.NetTopologySuite.xml", "npgsql.nettopologysuite.5.0.18.nupkg.sha512", "npgsql.nettopologysuite.nuspec", "postgresql.png"]}, "OpenTelemetry/1.12.0": {"sha512": "aIEu2O3xFOdwIVH0AJsIHPIMH1YuX18nzu7BHyaDNQ6NWSk4Zyrs9Pp6y8SATuSbvdtmvue4mj/QZ3838srbwA==", "type": "package", "path": "opentelemetry/1.12.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net462/OpenTelemetry.dll", "lib/net462/OpenTelemetry.dll-keyless.pem", "lib/net462/OpenTelemetry.dll-keyless.sig", "lib/net462/OpenTelemetry.xml", "lib/net8.0/OpenTelemetry.dll", "lib/net8.0/OpenTelemetry.dll-keyless.pem", "lib/net8.0/OpenTelemetry.dll-keyless.sig", "lib/net8.0/OpenTelemetry.xml", "lib/net9.0/OpenTelemetry.dll", "lib/net9.0/OpenTelemetry.dll-keyless.pem", "lib/net9.0/OpenTelemetry.dll-keyless.sig", "lib/net9.0/OpenTelemetry.xml", "lib/netstandard2.0/OpenTelemetry.dll", "lib/netstandard2.0/OpenTelemetry.dll-keyless.pem", "lib/netstandard2.0/OpenTelemetry.dll-keyless.sig", "lib/netstandard2.0/OpenTelemetry.xml", "lib/netstandard2.1/OpenTelemetry.dll", "lib/netstandard2.1/OpenTelemetry.dll-keyless.pem", "lib/netstandard2.1/OpenTelemetry.dll-keyless.sig", "lib/netstandard2.1/OpenTelemetry.xml", "opentelemetry-icon-color.png", "opentelemetry.1.12.0.nupkg.sha512", "opentelemetry.nuspec"]}, "OpenTelemetry.Api/1.12.0": {"sha512": "Xt0qldi+iE2szGrM3jAqzEMEJd48YBtqI6mge0+ArXTZg3aTpRmyhL6CKKl3bLioaFSSVbBpEbPin8u6Z46Yrw==", "type": "package", "path": "opentelemetry.api/1.12.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net462/OpenTelemetry.Api.dll", "lib/net462/OpenTelemetry.Api.dll-keyless.pem", "lib/net462/OpenTelemetry.Api.dll-keyless.sig", "lib/net462/OpenTelemetry.Api.xml", "lib/net8.0/OpenTelemetry.Api.dll", "lib/net8.0/OpenTelemetry.Api.dll-keyless.pem", "lib/net8.0/OpenTelemetry.Api.dll-keyless.sig", "lib/net8.0/OpenTelemetry.Api.xml", "lib/net9.0/OpenTelemetry.Api.dll", "lib/net9.0/OpenTelemetry.Api.dll-keyless.pem", "lib/net9.0/OpenTelemetry.Api.dll-keyless.sig", "lib/net9.0/OpenTelemetry.Api.xml", "lib/netstandard2.0/OpenTelemetry.Api.dll", "lib/netstandard2.0/OpenTelemetry.Api.dll-keyless.pem", "lib/netstandard2.0/OpenTelemetry.Api.dll-keyless.sig", "lib/netstandard2.0/OpenTelemetry.Api.xml", "opentelemetry-icon-color.png", "opentelemetry.api.1.12.0.nupkg.sha512", "opentelemetry.api.nuspec"]}, "OpenTelemetry.Api.ProviderBuilderExtensions/1.12.0": {"sha512": "t6Vk1143BfiisCWYbRcyzkAuN6Aq5RkYtfOSMoqCIRMvtN9p1e1xzc0nWQ+fccNGOVgHn3aMK5xFn2+iWMcr8A==", "type": "package", "path": "opentelemetry.api.providerbuilderextensions/1.12.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net462/OpenTelemetry.Api.ProviderBuilderExtensions.dll", "lib/net462/OpenTelemetry.Api.ProviderBuilderExtensions.dll-keyless.pem", "lib/net462/OpenTelemetry.Api.ProviderBuilderExtensions.dll-keyless.sig", "lib/net462/OpenTelemetry.Api.ProviderBuilderExtensions.xml", "lib/net8.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll", "lib/net8.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll-keyless.pem", "lib/net8.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll-keyless.sig", "lib/net8.0/OpenTelemetry.Api.ProviderBuilderExtensions.xml", "lib/net9.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll", "lib/net9.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll-keyless.pem", "lib/net9.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll-keyless.sig", "lib/net9.0/OpenTelemetry.Api.ProviderBuilderExtensions.xml", "lib/netstandard2.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll", "lib/netstandard2.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll-keyless.pem", "lib/netstandard2.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll-keyless.sig", "lib/netstandard2.0/OpenTelemetry.Api.ProviderBuilderExtensions.xml", "opentelemetry-icon-color.png", "opentelemetry.api.providerbuilderextensions.1.12.0.nupkg.sha512", "opentelemetry.api.providerbuilderextensions.nuspec"]}, "OpenTelemetry.Exporter.OpenTelemetryProtocol/1.12.0": {"sha512": "7LzQSPhz5pNaL4xZgT3wkZODA1NLrEq3bet8KDHgtaJ9q+VNP7wmiZky8gQfMkB4FXuI/pevT8ZurL4p5997WA==", "type": "package", "path": "opentelemetry.exporter.opentelemetryprotocol/1.12.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net462/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll", "lib/net462/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll-keyless.pem", "lib/net462/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll-keyless.sig", "lib/net462/OpenTelemetry.Exporter.OpenTelemetryProtocol.xml", "lib/net8.0/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll", "lib/net8.0/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll-keyless.pem", "lib/net8.0/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll-keyless.sig", "lib/net8.0/OpenTelemetry.Exporter.OpenTelemetryProtocol.xml", "lib/net9.0/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll", "lib/net9.0/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll-keyless.pem", "lib/net9.0/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll-keyless.sig", "lib/net9.0/OpenTelemetry.Exporter.OpenTelemetryProtocol.xml", "lib/netstandard2.0/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll", "lib/netstandard2.0/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll-keyless.pem", "lib/netstandard2.0/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll-keyless.sig", "lib/netstandard2.0/OpenTelemetry.Exporter.OpenTelemetryProtocol.xml", "lib/netstandard2.1/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll", "lib/netstandard2.1/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll-keyless.pem", "lib/netstandard2.1/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll-keyless.sig", "lib/netstandard2.1/OpenTelemetry.Exporter.OpenTelemetryProtocol.xml", "opentelemetry-icon-color.png", "opentelemetry.exporter.opentelemetryprotocol.1.12.0.nupkg.sha512", "opentelemetry.exporter.opentelemetryprotocol.nuspec"]}, "OpenTelemetry.Exporter.Zipkin/1.12.0": {"sha512": "Mc+k/bxib7ZWe2S6Au/p47yyan1J0sfVplZRIMpUrCwLrDK8J3DSRAqf1CwWdqKN/3tLI03AK4GSujXq5R4Idg==", "type": "package", "path": "opentelemetry.exporter.zipkin/1.12.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net462/OpenTelemetry.Exporter.Zipkin.dll", "lib/net462/OpenTelemetry.Exporter.Zipkin.dll-keyless.pem", "lib/net462/OpenTelemetry.Exporter.Zipkin.dll-keyless.sig", "lib/net462/OpenTelemetry.Exporter.Zipkin.xml", "lib/net8.0/OpenTelemetry.Exporter.Zipkin.dll", "lib/net8.0/OpenTelemetry.Exporter.Zipkin.dll-keyless.pem", "lib/net8.0/OpenTelemetry.Exporter.Zipkin.dll-keyless.sig", "lib/net8.0/OpenTelemetry.Exporter.Zipkin.xml", "lib/net9.0/OpenTelemetry.Exporter.Zipkin.dll", "lib/net9.0/OpenTelemetry.Exporter.Zipkin.dll-keyless.pem", "lib/net9.0/OpenTelemetry.Exporter.Zipkin.dll-keyless.sig", "lib/net9.0/OpenTelemetry.Exporter.Zipkin.xml", "lib/netstandard2.0/OpenTelemetry.Exporter.Zipkin.dll", "lib/netstandard2.0/OpenTelemetry.Exporter.Zipkin.dll-keyless.pem", "lib/netstandard2.0/OpenTelemetry.Exporter.Zipkin.dll-keyless.sig", "lib/netstandard2.0/OpenTelemetry.Exporter.Zipkin.xml", "opentelemetry-icon-color.png", "opentelemetry.exporter.zipkin.1.12.0.nupkg.sha512", "opentelemetry.exporter.zipkin.nuspec"]}, "OpenTelemetry.Extensions.Hosting/1.12.0": {"sha512": "6/8O6rsJRwslg5/Fm3bscBelw4Yh9T9CN24p7cAsuEFkrmmeSO9gkYUCK02Qi+CmPM2KHYTLjKi0lJaCsDMWQA==", "type": "package", "path": "opentelemetry.extensions.hosting/1.12.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net462/OpenTelemetry.Extensions.Hosting.dll", "lib/net462/OpenTelemetry.Extensions.Hosting.dll-keyless.pem", "lib/net462/OpenTelemetry.Extensions.Hosting.dll-keyless.sig", "lib/net462/OpenTelemetry.Extensions.Hosting.xml", "lib/net8.0/OpenTelemetry.Extensions.Hosting.dll", "lib/net8.0/OpenTelemetry.Extensions.Hosting.dll-keyless.pem", "lib/net8.0/OpenTelemetry.Extensions.Hosting.dll-keyless.sig", "lib/net8.0/OpenTelemetry.Extensions.Hosting.xml", "lib/net9.0/OpenTelemetry.Extensions.Hosting.dll", "lib/net9.0/OpenTelemetry.Extensions.Hosting.dll-keyless.pem", "lib/net9.0/OpenTelemetry.Extensions.Hosting.dll-keyless.sig", "lib/net9.0/OpenTelemetry.Extensions.Hosting.xml", "lib/netstandard2.0/OpenTelemetry.Extensions.Hosting.dll", "lib/netstandard2.0/OpenTelemetry.Extensions.Hosting.dll-keyless.pem", "lib/netstandard2.0/OpenTelemetry.Extensions.Hosting.dll-keyless.sig", "lib/netstandard2.0/OpenTelemetry.Extensions.Hosting.xml", "opentelemetry-icon-color.png", "opentelemetry.extensions.hosting.1.12.0.nupkg.sha512", "opentelemetry.extensions.hosting.nuspec"]}, "OpenTelemetry.Instrumentation.AspNetCore/1.12.0": {"sha512": "r+Mzggd2P4N0Y34QIO6kakVPBOKFYSHnLkTrXXM+r37ABp+iaUvVUe+u/uxszsi5f7P5mrG0uYYaJ1QGHvzo3A==", "type": "package", "path": "opentelemetry.instrumentation.aspnetcore/1.12.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "lib/net8.0/OpenTelemetry.Instrumentation.AspNetCore.dll", "lib/net8.0/OpenTelemetry.Instrumentation.AspNetCore.xml", "lib/netstandard2.0/OpenTelemetry.Instrumentation.AspNetCore.dll", "lib/netstandard2.0/OpenTelemetry.Instrumentation.AspNetCore.xml", "opentelemetry-icon-color.png", "opentelemetry.instrumentation.aspnetcore.1.12.0.nupkg.sha512", "opentelemetry.instrumentation.aspnetcore.nuspec"]}, "OpenTelemetry.Instrumentation.GrpcCore/1.0.0-beta.6": {"sha512": "Dy2W1dAI/qXYAkJ2r2lTTRERNhxf88TVid9xcwP5+wl0f8thn2HD7kP5TFmq+2PcJv3bIDi6phKRM6Frih2c8Q==", "type": "package", "path": "opentelemetry.instrumentation.grpccore/1.0.0-beta.6", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/OpenTelemetry.Instrumentation.GrpcCore.dll", "lib/netstandard2.0/OpenTelemetry.Instrumentation.GrpcCore.xml", "opentelemetry-icon-color.png", "opentelemetry.instrumentation.grpccore.1.0.0-beta.6.nupkg.sha512", "opentelemetry.instrumentation.grpccore.nuspec"]}, "OpenTelemetry.Instrumentation.GrpcNetClient/1.12.0-beta.1": {"sha512": "mi3Njei+Y5bn7oJYwIy/XWON+TK/sDUZy1m8UPw4ihtaCVjBTUdPWCjOW1sv3mZoctnJAT6PjNXD9222rqO65w==", "type": "package", "path": "opentelemetry.instrumentation.grpcnetclient/1.12.0-beta.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "lib/net8.0/OpenTelemetry.Instrumentation.GrpcNetClient.dll", "lib/net8.0/OpenTelemetry.Instrumentation.GrpcNetClient.xml", "lib/netstandard2.0/OpenTelemetry.Instrumentation.GrpcNetClient.dll", "lib/netstandard2.0/OpenTelemetry.Instrumentation.GrpcNetClient.xml", "lib/netstandard2.1/OpenTelemetry.Instrumentation.GrpcNetClient.dll", "lib/netstandard2.1/OpenTelemetry.Instrumentation.GrpcNetClient.xml", "opentelemetry-icon-color.png", "opentelemetry.instrumentation.grpcnetclient.1.12.0-beta.1.nupkg.sha512", "opentelemetry.instrumentation.grpcnetclient.nuspec"]}, "OpenTelemetry.Instrumentation.Http/1.12.0": {"sha512": "0rW+MbHgUQAdbvBtRxPYoQBosbNdWegL7cYkRlxq+KQ/VFyU8itt4pWTccmu1/FWmTgqJyT3LaujyDZoRrm8Yg==", "type": "package", "path": "opentelemetry.instrumentation.http/1.12.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "lib/net462/OpenTelemetry.Instrumentation.Http.dll", "lib/net462/OpenTelemetry.Instrumentation.Http.xml", "lib/net8.0/OpenTelemetry.Instrumentation.Http.dll", "lib/net8.0/OpenTelemetry.Instrumentation.Http.xml", "lib/netstandard2.0/OpenTelemetry.Instrumentation.Http.dll", "lib/netstandard2.0/OpenTelemetry.Instrumentation.Http.xml", "opentelemetry-icon-color.png", "opentelemetry.instrumentation.http.1.12.0.nupkg.sha512", "opentelemetry.instrumentation.http.nuspec"]}, "OpenTelemetry.Instrumentation.Process/1.12.0-beta.1": {"sha512": "GrjqeNzH94WOpUpKO7xO13NjBujk0wxvxzGBFuvU+kIAzMB22a4PzOGPrvQivx27RIKaFH+psQP6dTPUmv/8sA==", "type": "package", "path": "opentelemetry.instrumentation.process/1.12.0-beta.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "lib/netstandard2.0/OpenTelemetry.Instrumentation.Process.dll", "lib/netstandard2.0/OpenTelemetry.Instrumentation.Process.xml", "opentelemetry-icon-color.png", "opentelemetry.instrumentation.process.1.12.0-beta.1.nupkg.sha512", "opentelemetry.instrumentation.process.nuspec"]}, "OpenTelemetry.Instrumentation.Runtime/1.12.0": {"sha512": "xmd0TAm2x+T3ztdf5BolIwLPh+Uy6osaBeIQtCXv611PN7h/Pnhsjg5lU2hkAWj7M7ns74U5wtVpS8DXmJ+94w==", "type": "package", "path": "opentelemetry.instrumentation.runtime/1.12.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "lib/net462/OpenTelemetry.Instrumentation.Runtime.dll", "lib/net462/OpenTelemetry.Instrumentation.Runtime.xml", "lib/net8.0/OpenTelemetry.Instrumentation.Runtime.dll", "lib/net8.0/OpenTelemetry.Instrumentation.Runtime.xml", "lib/netstandard2.0/OpenTelemetry.Instrumentation.Runtime.dll", "lib/netstandard2.0/OpenTelemetry.Instrumentation.Runtime.xml", "opentelemetry-icon-color.png", "opentelemetry.instrumentation.runtime.1.12.0.nupkg.sha512", "opentelemetry.instrumentation.runtime.nuspec"]}, "Oracle.ManagedDataAccess.Core/23.6.1": {"sha512": "Oc8AX7xme05xrp4/aCxKBH4+bpWgMCFafXI7LbLO/7OBMJLZRXhMtejDgIb8aYvIVyV7vSdAy3LkCYcJorxn1A==", "type": "package", "path": "oracle.manageddataaccess.core/23.6.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PerfCounters/register_odpc_perfmon_counters.ps1", "PerfCounters/unregister_odpc_perfmon_counters.ps1", "README.md", "info.txt", "lib/net8.0/Oracle.ManagedDataAccess.dll", "lib/netstandard2.1/Oracle.ManagedDataAccess.dll", "oracle.manageddataaccess.core.23.6.1.nupkg.sha512", "oracle.manageddataaccess.core.nuspec", "oracle.png"]}, "Polly.Core/8.4.2": {"sha512": "BpE2I6HBYYA5tF0Vn4eoQOGYTYIK1BlF5EXVgkWGn3mqUUjbXAr13J6fZVbp7Q3epRR8yshacBMlsHMhpOiV3g==", "type": "package", "path": "polly.core/8.4.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/Polly.Core.dll", "lib/net462/Polly.Core.pdb", "lib/net462/Polly.Core.xml", "lib/net472/Polly.Core.dll", "lib/net472/Polly.Core.pdb", "lib/net472/Polly.Core.xml", "lib/net6.0/Polly.Core.dll", "lib/net6.0/Polly.Core.pdb", "lib/net6.0/Polly.Core.xml", "lib/net8.0/Polly.Core.dll", "lib/net8.0/Polly.Core.pdb", "lib/net8.0/Polly.Core.xml", "lib/netstandard2.0/Polly.Core.dll", "lib/netstandard2.0/Polly.Core.pdb", "lib/netstandard2.0/Polly.Core.xml", "package-icon.png", "package-readme.md", "polly.core.8.4.2.nupkg.sha512", "polly.core.nuspec"]}, "Polly.Extensions/8.4.2": {"sha512": "GZ9vRVmR0jV2JtZavt+pGUsQ1O1cuRKG7R7VOZI6ZDy9y6RNPvRvXK1tuS4ffUrv8L0FTea59oEuQzgS0R7zSA==", "type": "package", "path": "polly.extensions/8.4.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/Polly.Extensions.dll", "lib/net462/Polly.Extensions.pdb", "lib/net462/Polly.Extensions.xml", "lib/net472/Polly.Extensions.dll", "lib/net472/Polly.Extensions.pdb", "lib/net472/Polly.Extensions.xml", "lib/net6.0/Polly.Extensions.dll", "lib/net6.0/Polly.Extensions.pdb", "lib/net6.0/Polly.Extensions.xml", "lib/net8.0/Polly.Extensions.dll", "lib/net8.0/Polly.Extensions.pdb", "lib/net8.0/Polly.Extensions.xml", "lib/netstandard2.0/Polly.Extensions.dll", "lib/netstandard2.0/Polly.Extensions.pdb", "lib/netstandard2.0/Polly.Extensions.xml", "package-icon.png", "package-readme.md", "polly.extensions.8.4.2.nupkg.sha512", "polly.extensions.nuspec"]}, "Polly.RateLimiting/8.4.2": {"sha512": "ehTImQ/eUyO07VYW2WvwSmU9rRH200SKJ/3jku9rOkyWE0A2JxNFmAVms8dSn49QLSjmjFRRSgfNyOgr/2PSmA==", "type": "package", "path": "polly.ratelimiting/8.4.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/Polly.RateLimiting.dll", "lib/net462/Polly.RateLimiting.pdb", "lib/net462/Polly.RateLimiting.xml", "lib/net472/Polly.RateLimiting.dll", "lib/net472/Polly.RateLimiting.pdb", "lib/net472/Polly.RateLimiting.xml", "lib/net6.0/Polly.RateLimiting.dll", "lib/net6.0/Polly.RateLimiting.pdb", "lib/net6.0/Polly.RateLimiting.xml", "lib/net8.0/Polly.RateLimiting.dll", "lib/net8.0/Polly.RateLimiting.pdb", "lib/net8.0/Polly.RateLimiting.xml", "lib/netstandard2.0/Polly.RateLimiting.dll", "lib/netstandard2.0/Polly.RateLimiting.pdb", "lib/netstandard2.0/Polly.RateLimiting.xml", "package-icon.png", "package-readme.md", "polly.ratelimiting.8.4.2.nupkg.sha512", "polly.ratelimiting.nuspec"]}, "QRCoder/1.6.0": {"sha512": "XmPA81eo+oRxBuyVdswsSkTGTE1d3thfF11Z1PdD7oB56A6HU4G4AAOdySmGRMb/cljwlFTMWUtosGEnwpS6GA==", "type": "package", "path": "qrcoder/1.6.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net35/QRCoder.dll", "lib/net40/QRCoder.dll", "lib/net5.0-windows7.0/QRCoder.dll", "lib/net5.0/QRCoder.dll", "lib/net6.0-windows7.0/QRCoder.dll", "lib/net6.0/QRCoder.dll", "lib/netstandard1.3/QRCoder.dll", "lib/netstandard2.0/QRCoder.dll", "nuget-icon.png", "nuget-readme.md", "qrcoder.1.6.0.nupkg.sha512", "qrcoder.nuspec"]}, "Scalar.AspNetCore/2.6.9": {"sha512": "wIejm5++cWnu2jrkNiku+AvIClEEgp8c7GMQmTuT5LZeamEhHS1tvGEDJhfnPCZ4DPIMVX6e9b4tpzRJ2Qqy/w==", "type": "package", "path": "scalar.aspnetcore/2.6.9", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net8.0/Scalar.AspNetCore.dll", "lib/net8.0/Scalar.AspNetCore.xml", "lib/net9.0/Scalar.AspNetCore.dll", "lib/net9.0/Scalar.AspNetCore.xml", "scalar.aspnetcore.2.6.9.nupkg.sha512", "scalar.aspnetcore.nuspec"]}, "Serilog/4.3.1-dev-02373": {"sha512": "f0HNPaleOox5++8zqxAdi9afnUUkoznLOmd0ur/UnLbzl8bIaPyyBGOpbIJmC1kz2vHfkQ0fXh6KXMdlz7bcLQ==", "type": "package", "path": "serilog/4.3.1-dev-02373", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "build/Serilog.targets", "icon.png", "lib/net462/Serilog.dll", "lib/net462/Serilog.xml", "lib/net471/Serilog.dll", "lib/net471/Serilog.xml", "lib/net6.0/Serilog.dll", "lib/net6.0/Serilog.xml", "lib/net8.0/Serilog.dll", "lib/net8.0/Serilog.xml", "lib/net9.0/Serilog.dll", "lib/net9.0/Serilog.xml", "lib/netstandard2.0/Serilog.dll", "lib/netstandard2.0/Serilog.xml", "serilog.4.3.1-dev-02373.nupkg.sha512", "serilog.nuspec"]}, "Serilog.AspNetCore/9.0.0": {"sha512": "JslDajPlBsn3Pww1554flJFTqROvK9zz9jONNQgn0D8Lx2Trw8L0A8/n6zEQK1DAZWXrJwiVLw8cnTR3YFuYsg==", "type": "package", "path": "serilog.aspnetcore/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.AspNetCore.dll", "lib/net462/Serilog.AspNetCore.xml", "lib/net8.0/Serilog.AspNetCore.dll", "lib/net8.0/Serilog.AspNetCore.xml", "lib/net9.0/Serilog.AspNetCore.dll", "lib/net9.0/Serilog.AspNetCore.xml", "lib/netstandard2.0/Serilog.AspNetCore.dll", "lib/netstandard2.0/Serilog.AspNetCore.xml", "lib/netstandard2.1/Serilog.AspNetCore.dll", "lib/netstandard2.1/Serilog.AspNetCore.xml", "serilog.aspnetcore.9.0.0.nupkg.sha512", "serilog.aspnetcore.nuspec"]}, "Serilog.Enrichers.Environment/3.0.1": {"sha512": "9BqCE4C9FF+/rJb/CsQwe7oVf44xqkOvMwX//CUxvUR25lFL4tSS6iuxE5eW07quby1BAyAEP+vM6TWsnT3iqw==", "type": "package", "path": "serilog.enrichers.environment/3.0.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Serilog.Enrichers.Environment.dll", "lib/net462/Serilog.Enrichers.Environment.xml", "lib/net471/Serilog.Enrichers.Environment.dll", "lib/net471/Serilog.Enrichers.Environment.xml", "lib/net6.0/Serilog.Enrichers.Environment.dll", "lib/net6.0/Serilog.Enrichers.Environment.xml", "lib/net8.0/Serilog.Enrichers.Environment.dll", "lib/net8.0/Serilog.Enrichers.Environment.xml", "lib/netstandard2.0/Serilog.Enrichers.Environment.dll", "lib/netstandard2.0/Serilog.Enrichers.Environment.xml", "serilog-enricher-nuget.png", "serilog.enrichers.environment.3.0.1.nupkg.sha512", "serilog.enrichers.environment.nuspec"]}, "Serilog.Enrichers.Thread/4.0.0": {"sha512": "C7BK25a1rhUyr+Tp+1BYcVlBJq7M2VCHlIgnwoIUVJcicM9jYcvQK18+OeHiXw7uLPSjqWxJIp1EfaZ/RGmEwA==", "type": "package", "path": "serilog.enrichers.thread/4.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Serilog.Enrichers.Thread.dll", "lib/net462/Serilog.Enrichers.Thread.xml", "lib/net471/Serilog.Enrichers.Thread.dll", "lib/net471/Serilog.Enrichers.Thread.xml", "lib/net6.0/Serilog.Enrichers.Thread.dll", "lib/net6.0/Serilog.Enrichers.Thread.xml", "lib/net8.0/Serilog.Enrichers.Thread.dll", "lib/net8.0/Serilog.Enrichers.Thread.xml", "lib/netstandard2.0/Serilog.Enrichers.Thread.dll", "lib/netstandard2.0/Serilog.Enrichers.Thread.xml", "serilog-enricher-nuget.png", "serilog.enrichers.thread.4.0.0.nupkg.sha512", "serilog.enrichers.thread.nuspec"]}, "Serilog.Expressions/5.1.0-dev-02301": {"sha512": "hW7OKCr8m/5h84oTFeXSnnfqFozn05h9BfU+1kMLLFvSmJPjRhaxqVqR/zs2+g9kD8s0d9CSRCwhtZAshbyvUg==", "type": "package", "path": "serilog.expressions/5.1.0-dev-02301", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.Expressions.dll", "lib/net462/Serilog.Expressions.xml", "lib/net471/Serilog.Expressions.dll", "lib/net471/Serilog.Expressions.xml", "lib/net6.0/Serilog.Expressions.dll", "lib/net6.0/Serilog.Expressions.xml", "lib/net8.0/Serilog.Expressions.dll", "lib/net8.0/Serilog.Expressions.xml", "lib/net9.0/Serilog.Expressions.dll", "lib/net9.0/Serilog.Expressions.xml", "lib/netstandard2.0/Serilog.Expressions.dll", "lib/netstandard2.0/Serilog.Expressions.xml", "serilog.expressions.5.1.0-dev-02301.nupkg.sha512", "serilog.expressions.nuspec"]}, "Serilog.Extensions.Hosting/9.0.1-dev-02307": {"sha512": "bBx2sEozyzXTv+nysstxgUxHOWbAx/viJ/SmM4ELLhjEHjj+KfGOpn2c0hn0Wb6x+9OZ3bVESsZmhtPLr4gLKw==", "type": "package", "path": "serilog.extensions.hosting/9.0.1-dev-02307", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.Extensions.Hosting.dll", "lib/net462/Serilog.Extensions.Hosting.xml", "lib/net8.0/Serilog.Extensions.Hosting.dll", "lib/net8.0/Serilog.Extensions.Hosting.xml", "lib/net9.0/Serilog.Extensions.Hosting.dll", "lib/net9.0/Serilog.Extensions.Hosting.xml", "lib/netstandard2.0/Serilog.Extensions.Hosting.dll", "lib/netstandard2.0/Serilog.Extensions.Hosting.xml", "lib/netstandard2.1/Serilog.Extensions.Hosting.dll", "lib/netstandard2.1/Serilog.Extensions.Hosting.xml", "serilog.extensions.hosting.9.0.1-dev-02307.nupkg.sha512", "serilog.extensions.hosting.nuspec"]}, "Serilog.Extensions.Logging/9.0.3-dev-02320": {"sha512": "cfARu+vsHJHNyTVmo1U+AQY00q1W7w3onNSHqUHyd/GtoCSkH2VFFpy7C2z+ATVCcDk7e6EUU49dn6svvW3uMQ==", "type": "package", "path": "serilog.extensions.logging/9.0.3-dev-02320", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Serilog.Extensions.Logging.dll", "lib/net462/Serilog.Extensions.Logging.xml", "lib/net8.0/Serilog.Extensions.Logging.dll", "lib/net8.0/Serilog.Extensions.Logging.xml", "lib/net9.0/Serilog.Extensions.Logging.dll", "lib/net9.0/Serilog.Extensions.Logging.xml", "lib/netstandard2.0/Serilog.Extensions.Logging.dll", "lib/netstandard2.0/Serilog.Extensions.Logging.xml", "lib/netstandard2.1/Serilog.Extensions.Logging.dll", "lib/netstandard2.1/Serilog.Extensions.Logging.xml", "serilog-extension-nuget.png", "serilog.extensions.logging.9.0.3-dev-02320.nupkg.sha512", "serilog.extensions.logging.nuspec"]}, "Serilog.Formatting.Compact/3.0.0": {"sha512": "wQsv14w9cqlfB5FX2MZpNsTawckN4a8dryuNGbebB/3Nh1pXnROHZov3swtu3Nj5oNG7Ba+xdu7Et/ulAUPanQ==", "type": "package", "path": "serilog.formatting.compact/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Serilog.Formatting.Compact.dll", "lib/net462/Serilog.Formatting.Compact.xml", "lib/net471/Serilog.Formatting.Compact.dll", "lib/net471/Serilog.Formatting.Compact.xml", "lib/net6.0/Serilog.Formatting.Compact.dll", "lib/net6.0/Serilog.Formatting.Compact.xml", "lib/net8.0/Serilog.Formatting.Compact.dll", "lib/net8.0/Serilog.Formatting.Compact.xml", "lib/netstandard2.0/Serilog.Formatting.Compact.dll", "lib/netstandard2.0/Serilog.Formatting.Compact.xml", "serilog-extension-nuget.png", "serilog.formatting.compact.3.0.0.nupkg.sha512", "serilog.formatting.compact.nuspec"]}, "Serilog.Settings.Configuration/9.0.1-dev-02317": {"sha512": "1/PPRG1VvYCuFJL8Dc7lkpHNFRZq6n0cwy976CgK21qRwmAIR2GgEkzIc9LZw8TVlvSmoUhZRyeBoU7bB9TjIw==", "type": "package", "path": "serilog.settings.configuration/9.0.1-dev-02317", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.Settings.Configuration.dll", "lib/net462/Serilog.Settings.Configuration.xml", "lib/net8.0/Serilog.Settings.Configuration.dll", "lib/net8.0/Serilog.Settings.Configuration.xml", "lib/net9.0/Serilog.Settings.Configuration.dll", "lib/net9.0/Serilog.Settings.Configuration.xml", "lib/netstandard2.0/Serilog.Settings.Configuration.dll", "lib/netstandard2.0/Serilog.Settings.Configuration.xml", "serilog.settings.configuration.9.0.1-dev-02317.nupkg.sha512", "serilog.settings.configuration.nuspec"]}, "Serilog.Sinks.Console/6.0.1-dev-00953": {"sha512": "Goi2B0Je0X0NvWYUi0SiU9MJNF2957Kfjmc6VPZ2hNl6Lmj9he6laxmDuQU/c0fBdAFnNiEUPPcHd/NJVyfbkA==", "type": "package", "path": "serilog.sinks.console/6.0.1-dev-00953", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.Sinks.Console.dll", "lib/net462/Serilog.Sinks.Console.xml", "lib/net471/Serilog.Sinks.Console.dll", "lib/net471/Serilog.Sinks.Console.xml", "lib/net6.0/Serilog.Sinks.Console.dll", "lib/net6.0/Serilog.Sinks.Console.xml", "lib/net8.0/Serilog.Sinks.Console.dll", "lib/net8.0/Serilog.Sinks.Console.xml", "lib/netstandard2.0/Serilog.Sinks.Console.dll", "lib/netstandard2.0/Serilog.Sinks.Console.xml", "serilog.sinks.console.6.0.1-dev-00953.nupkg.sha512", "serilog.sinks.console.nuspec"]}, "Serilog.Sinks.Debug/3.0.0": {"sha512": "4BzXcdrgRX7wde9PmHuYd9U6YqycCC28hhpKonK7hx0wb19eiuRj16fPcPSVp0o/Y1ipJuNLYQ00R3q2Zs8FDA==", "type": "package", "path": "serilog.sinks.debug/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.Sinks.Debug.dll", "lib/net462/Serilog.Sinks.Debug.xml", "lib/net471/Serilog.Sinks.Debug.dll", "lib/net471/Serilog.Sinks.Debug.xml", "lib/net6.0/Serilog.Sinks.Debug.dll", "lib/net6.0/Serilog.Sinks.Debug.xml", "lib/net8.0/Serilog.Sinks.Debug.dll", "lib/net8.0/Serilog.Sinks.Debug.xml", "lib/netstandard2.0/Serilog.Sinks.Debug.dll", "lib/netstandard2.0/Serilog.Sinks.Debug.xml", "serilog.sinks.debug.3.0.0.nupkg.sha512", "serilog.sinks.debug.nuspec"]}, "Serilog.Sinks.File/6.0.0": {"sha512": "lxjg89Y8gJMmFxVkbZ+qDgjl+T4yC5F7WSLTvA+5q0R04tfKVLRL/EHpYoJ/MEQd2EeCKDuylBIVnAYMotmh2A==", "type": "package", "path": "serilog.sinks.file/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Serilog.Sinks.File.dll", "lib/net462/Serilog.Sinks.File.xml", "lib/net471/Serilog.Sinks.File.dll", "lib/net471/Serilog.Sinks.File.xml", "lib/net6.0/Serilog.Sinks.File.dll", "lib/net6.0/Serilog.Sinks.File.xml", "lib/net8.0/Serilog.Sinks.File.dll", "lib/net8.0/Serilog.Sinks.File.xml", "lib/netstandard2.0/Serilog.Sinks.File.dll", "lib/netstandard2.0/Serilog.Sinks.File.xml", "serilog-sink-nuget.png", "serilog.sinks.file.6.0.0.nupkg.sha512", "serilog.sinks.file.nuspec"]}, "Serilog.Sinks.OpenTelemetry/4.2.1-dev-02306": {"sha512": "9SeG+JzjsMSDF7S1LJtGicZKFGfOWp2hLPZVtyvNh7uXRQqHGrcgxC9k1WPZYXrc7AcFJDbYbfgE0/7SSekH5Q==", "type": "package", "path": "serilog.sinks.opentelemetry/4.2.1-dev-02306", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Serilog.Sinks.OpenTelemetry.dll", "lib/net462/Serilog.Sinks.OpenTelemetry.xml", "lib/net471/Serilog.Sinks.OpenTelemetry.dll", "lib/net471/Serilog.Sinks.OpenTelemetry.xml", "lib/net6.0/Serilog.Sinks.OpenTelemetry.dll", "lib/net6.0/Serilog.Sinks.OpenTelemetry.xml", "lib/net8.0/Serilog.Sinks.OpenTelemetry.dll", "lib/net8.0/Serilog.Sinks.OpenTelemetry.xml", "lib/net9.0/Serilog.Sinks.OpenTelemetry.dll", "lib/net9.0/Serilog.Sinks.OpenTelemetry.xml", "lib/netstandard2.0/Serilog.Sinks.OpenTelemetry.dll", "lib/netstandard2.0/Serilog.Sinks.OpenTelemetry.xml", "serilog-sink-nuget.png", "serilog.sinks.opentelemetry.4.2.1-dev-02306.nupkg.sha512", "serilog.sinks.opentelemetry.nuspec"]}, "Serilog.Sinks.Seq/9.0.0": {"sha512": "aNU8A0K322q7+voPNmp1/qNPH+9QK8xvM1p72sMmCG0wGlshFzmtDW9QnVSoSYCj0MgQKcMOlgooovtBhRlNHw==", "type": "package", "path": "serilog.sinks.seq/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net6.0/Serilog.Sinks.Seq.dll", "lib/net6.0/Serilog.Sinks.Seq.xml", "lib/netstandard2.0/Serilog.Sinks.Seq.dll", "lib/netstandard2.0/Serilog.Sinks.Seq.xml", "serilog.sinks.seq.9.0.0.nupkg.sha512", "serilog.sinks.seq.nuspec"]}, "SharpCompress/0.40.0": {"sha512": "yP/aFX1jqGikVF7u2f05VEaWN4aCaKNLxSas82UgA2GGVECxq/BcqZx3STHCJ78qilo1azEOk1XpBglIuGMb7w==", "type": "package", "path": "sharpcompress/0.40.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net48/SharpCompress.dll", "lib/net48/SharpCompress.pdb", "lib/net481/SharpCompress.dll", "lib/net481/SharpCompress.pdb", "lib/net6.0/SharpCompress.dll", "lib/net6.0/SharpCompress.pdb", "lib/net8.0/SharpCompress.dll", "lib/net8.0/SharpCompress.pdb", "lib/netstandard2.0/SharpCompress.dll", "lib/netstandard2.0/SharpCompress.pdb", "sharpcompress.0.40.0.nupkg.sha512", "sharpcompress.nuspec"]}, "SixLabors.Fonts/2.1.3": {"sha512": "ORWbZ5BHrC/LZvo+Y09MnoJq5VUKD85LsYALk+YI7CHFra+m5arCkz00IntDM6SrAiB22bvSdKtKmuCyHOKlqg==", "type": "package", "path": "sixlabors.fonts/2.1.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "lib/net6.0/SixLabors.Fonts.dll", "lib/net6.0/SixLabors.Fonts.xml", "sixlabors.fonts.128.png", "sixlabors.fonts.2.1.3.nupkg.sha512", "sixlabors.fonts.nuspec"]}, "SixLabors.ImageSharp/3.1.11": {"sha512": "JfPLyigLthuE50yi6tMt7Amrenr/fA31t2CvJyhy/kQmfulIBAqo5T/YFUSRHtuYPXRSaUHygFeh6Qd933EoSw==", "type": "package", "path": "sixlabors.imagesharp/3.1.11", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "build/SixLabors.ImageSharp.props", "lib/net6.0/SixLabors.ImageSharp.dll", "lib/net6.0/SixLabors.ImageSharp.xml", "sixlabors.imagesharp.128.png", "sixlabors.imagesharp.3.1.11.nupkg.sha512", "sixlabors.imagesharp.nuspec"]}, "SixLabors.ImageSharp.Drawing/2.1.7": {"sha512": "9KwCo9Fa350cx6ckpsy8NqXQZKwir4RQ8Kj0sdCmJA7wsK9FMyfgC527Sn4l/D6bj2ditSHlhS7dGzcgGszvSQ==", "type": "package", "path": "sixlabors.imagesharp.drawing/2.1.7", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "lib/net6.0/SixLabors.ImageSharp.Drawing.dll", "lib/net6.0/SixLabors.ImageSharp.Drawing.xml", "sixlabors.imagesharp.drawing.128.png", "sixlabors.imagesharp.drawing.2.1.7.nupkg.sha512", "sixlabors.imagesharp.drawing.nuspec"]}, "Stub.System.Data.SQLite.Core.NetStandard/1.0.119": {"sha512": "dI7ngiCNgdm+n00nQvFTa+LbHvE9MIQXwMSLRzJI/KAJ7G1WmCachsvfE1CD6xvb3OXJvYYEfv3+S/LHyhN0Rg==", "type": "package", "path": "stub.system.data.sqlite.core.netstandard/1.0.119", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/System.Data.SQLite.dll", "lib/netstandard2.0/System.Data.SQLite.dll.altconfig", "lib/netstandard2.0/System.Data.SQLite.xml", "lib/netstandard2.1/System.Data.SQLite.dll", "lib/netstandard2.1/System.Data.SQLite.dll.altconfig", "lib/netstandard2.1/System.Data.SQLite.xml", "runtimes/linux-x64/native/SQLite.Interop.dll", "runtimes/osx-x64/native/SQLite.Interop.dll", "runtimes/win-x64/native/SQLite.Interop.dll", "runtimes/win-x86/native/SQLite.Interop.dll", "stub.system.data.sqlite.core.netstandard.1.0.119.nupkg.sha512", "stub.system.data.sqlite.core.netstandard.nuspec"]}, "Swashbuckle.AspNetCore/9.0.3": {"sha512": "Akk4oFgy0ST8Q8pZTfPbrt045tWNyMMiKhlbYjG3qnjQZLz645IL5vhQm7NLicc2sAAQ+vftArIlsYWFevmb2g==", "type": "package", "path": "swashbuckle.aspnetcore/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "build/Swashbuckle.AspNetCore.props", "buildMultiTargeting/Swashbuckle.AspNetCore.props", "docs/package-readme.md", "swashbuckle.aspnetcore.9.0.3.nupkg.sha512", "swashbuckle.aspnetcore.nuspec"]}, "Swashbuckle.AspNetCore.Swagger/9.0.3": {"sha512": "CGpkZDWj1g/yH/0wYkxUtBhiFo5TY/Esq2fS0vlBvLOs1UL2Jzef9tdtYmTdd3zBPtnMyXQcsXjMt9yCxz4VaA==", "type": "package", "path": "swashbuckle.aspnetcore.swagger/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net8.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/net8.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/net8.0/Swashbuckle.AspNetCore.Swagger.xml", "lib/net9.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/net9.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/net9.0/Swashbuckle.AspNetCore.Swagger.xml", "package-readme.md", "swashbuckle.aspnetcore.swagger.9.0.3.nupkg.sha512", "swashbuckle.aspnetcore.swagger.nuspec"]}, "Swashbuckle.AspNetCore.SwaggerGen/9.0.3": {"sha512": "STqjhw1TZiEGmIRgE6jcJUOcgU/Fjquc6dP4GqbuwBzqWZAWr/9T7FZOGWYEwKnmkMplzlUNepGHwnUrfTP0fw==", "type": "package", "path": "swashbuckle.aspnetcore.swaggergen/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net8.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/net8.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/net8.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "lib/net9.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/net9.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/net9.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "package-readme.md", "swashbuckle.aspnetcore.swaggergen.9.0.3.nupkg.sha512", "swashbuckle.aspnetcore.swaggergen.nuspec"]}, "Swashbuckle.AspNetCore.SwaggerUI/9.0.3": {"sha512": "DgJKJASz5OAygeKv2+N0FCZVhQylESqLXrtrRAqIT0vKpX7t5ImJ1FL6+6OqxKiamGkL0jchRXR8OgpMSsMh8w==", "type": "package", "path": "swashbuckle.aspnetcore.swaggerui/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net8.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/net8.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/net8.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "lib/net9.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/net9.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/net9.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "package-readme.md", "swashbuckle.aspnetcore.swaggerui.9.0.3.nupkg.sha512", "swashbuckle.aspnetcore.swaggerui.nuspec"]}, "System.ClientModel/1.5.1": {"sha512": "k2jKSO0X45IqhVOT9iQB4xralNN9foRQsRvXBTyRpAVxyzCJlG895T9qYrQWbcJ6OQXxOouJQ37x5nZH5XKK+A==", "type": "package", "path": "system.clientmodel/1.5.1", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "DotNetPackageIcon.png", "README.md", "analyzers/dotnet/cs/System.ClientModel.SourceGeneration.dll", "lib/net8.0/System.ClientModel.dll", "lib/net8.0/System.ClientModel.xml", "lib/netstandard2.0/System.ClientModel.dll", "lib/netstandard2.0/System.ClientModel.xml", "system.clientmodel.1.5.1.nupkg.sha512", "system.clientmodel.nuspec"]}, "System.CodeDom/9.0.8": {"sha512": "kxg1TE/BMUAaMve/lPH9Bds+CFF4JPrN9WkQqHejc7UP6PO4KDAjZNF0uhvpZmoLR+EvHdRt232J++ZDI7aGqA==", "type": "package", "path": "system.codedom/9.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.CodeDom.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.CodeDom.targets", "lib/net462/System.CodeDom.dll", "lib/net462/System.CodeDom.xml", "lib/net8.0/System.CodeDom.dll", "lib/net8.0/System.CodeDom.xml", "lib/net9.0/System.CodeDom.dll", "lib/net9.0/System.CodeDom.xml", "lib/netstandard2.0/System.CodeDom.dll", "lib/netstandard2.0/System.CodeDom.xml", "system.codedom.9.0.8.nupkg.sha512", "system.codedom.nuspec", "useSharedDesignerContext.txt"]}, "System.Configuration.ConfigurationManager/9.0.8": {"sha512": "BR70HYY5jeOa/jBrd/wyydpuqhFyla2ybQRL/UPBIiSvctVqi18iQoM44Gx41jy7t6wuAdKuTnie6io4j8aq3w==", "type": "package", "path": "system.configuration.configurationmanager/9.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Configuration.ConfigurationManager.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Configuration.ConfigurationManager.targets", "lib/net462/System.Configuration.ConfigurationManager.dll", "lib/net462/System.Configuration.ConfigurationManager.xml", "lib/net8.0/System.Configuration.ConfigurationManager.dll", "lib/net8.0/System.Configuration.ConfigurationManager.xml", "lib/net9.0/System.Configuration.ConfigurationManager.dll", "lib/net9.0/System.Configuration.ConfigurationManager.xml", "lib/netstandard2.0/System.Configuration.ConfigurationManager.dll", "lib/netstandard2.0/System.Configuration.ConfigurationManager.xml", "system.configuration.configurationmanager.9.0.8.nupkg.sha512", "system.configuration.configurationmanager.nuspec", "useSharedDesignerContext.txt"]}, "System.Data.Odbc/8.0.0": {"sha512": "c+GfnZt2/HyU+voKw2fctLZClcNjPZPWS+mnIhGvDknRMqL/fwWlREWPgA4csbp9ZkQIgB4qkufgdh/oh5Ubow==", "type": "package", "path": "system.data.odbc/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Data.Odbc.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Data.Odbc.targets", "lib/net462/System.Data.Odbc.dll", "lib/net462/System.Data.Odbc.xml", "lib/net6.0/System.Data.Odbc.dll", "lib/net6.0/System.Data.Odbc.xml", "lib/net7.0/System.Data.Odbc.dll", "lib/net7.0/System.Data.Odbc.xml", "lib/net8.0/System.Data.Odbc.dll", "lib/net8.0/System.Data.Odbc.xml", "lib/netstandard2.0/System.Data.Odbc.dll", "lib/netstandard2.0/System.Data.Odbc.xml", "runtimes/freebsd/lib/net6.0/System.Data.Odbc.dll", "runtimes/freebsd/lib/net6.0/System.Data.Odbc.xml", "runtimes/freebsd/lib/net7.0/System.Data.Odbc.dll", "runtimes/freebsd/lib/net7.0/System.Data.Odbc.xml", "runtimes/freebsd/lib/net8.0/System.Data.Odbc.dll", "runtimes/freebsd/lib/net8.0/System.Data.Odbc.xml", "runtimes/illumos/lib/net7.0/System.Data.Odbc.dll", "runtimes/illumos/lib/net7.0/System.Data.Odbc.xml", "runtimes/illumos/lib/net8.0/System.Data.Odbc.dll", "runtimes/illumos/lib/net8.0/System.Data.Odbc.xml", "runtimes/ios/lib/net7.0/System.Data.Odbc.dll", "runtimes/ios/lib/net7.0/System.Data.Odbc.xml", "runtimes/ios/lib/net8.0/System.Data.Odbc.dll", "runtimes/ios/lib/net8.0/System.Data.Odbc.xml", "runtimes/linux/lib/net6.0/System.Data.Odbc.dll", "runtimes/linux/lib/net6.0/System.Data.Odbc.xml", "runtimes/linux/lib/net7.0/System.Data.Odbc.dll", "runtimes/linux/lib/net7.0/System.Data.Odbc.xml", "runtimes/linux/lib/net8.0/System.Data.Odbc.dll", "runtimes/linux/lib/net8.0/System.Data.Odbc.xml", "runtimes/osx/lib/net6.0/System.Data.Odbc.dll", "runtimes/osx/lib/net6.0/System.Data.Odbc.xml", "runtimes/osx/lib/net7.0/System.Data.Odbc.dll", "runtimes/osx/lib/net7.0/System.Data.Odbc.xml", "runtimes/osx/lib/net8.0/System.Data.Odbc.dll", "runtimes/osx/lib/net8.0/System.Data.Odbc.xml", "runtimes/solaris/lib/net7.0/System.Data.Odbc.dll", "runtimes/solaris/lib/net7.0/System.Data.Odbc.xml", "runtimes/solaris/lib/net8.0/System.Data.Odbc.dll", "runtimes/solaris/lib/net8.0/System.Data.Odbc.xml", "runtimes/tvos/lib/net7.0/System.Data.Odbc.dll", "runtimes/tvos/lib/net7.0/System.Data.Odbc.xml", "runtimes/tvos/lib/net8.0/System.Data.Odbc.dll", "runtimes/tvos/lib/net8.0/System.Data.Odbc.xml", "runtimes/win/lib/net6.0/System.Data.Odbc.dll", "runtimes/win/lib/net6.0/System.Data.Odbc.xml", "runtimes/win/lib/net7.0/System.Data.Odbc.dll", "runtimes/win/lib/net7.0/System.Data.Odbc.xml", "runtimes/win/lib/net8.0/System.Data.Odbc.dll", "runtimes/win/lib/net8.0/System.Data.Odbc.xml", "system.data.odbc.8.0.0.nupkg.sha512", "system.data.odbc.nuspec", "useSharedDesignerContext.txt"]}, "System.Data.OleDb/6.0.0": {"sha512": "LQ8PjTIF1LtrrlGiyiTVjAkQtTWKm9GSNnygIlWjhN9y88s7xhy6DUNDDkmQQ9f6ex7mA4k0Tl97lz/CklaiLg==", "type": "package", "path": "system.data.oledb/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Data.OleDb.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Data.OleDb.dll", "lib/net461/System.Data.OleDb.xml", "lib/net6.0/System.Data.OleDb.dll", "lib/net6.0/System.Data.OleDb.xml", "lib/netstandard2.0/System.Data.OleDb.dll", "lib/netstandard2.0/System.Data.OleDb.xml", "runtimes/win/lib/net461/System.Data.OleDb.dll", "runtimes/win/lib/net461/System.Data.OleDb.xml", "runtimes/win/lib/net6.0/System.Data.OleDb.dll", "runtimes/win/lib/net6.0/System.Data.OleDb.xml", "runtimes/win/lib/netstandard2.0/System.Data.OleDb.dll", "runtimes/win/lib/netstandard2.0/System.Data.OleDb.xml", "system.data.oledb.6.0.0.nupkg.sha512", "system.data.oledb.nuspec", "useSharedDesignerContext.txt"]}, "System.Data.SQLite.Core/1.0.119": {"sha512": "bhQB8HVtRA+OOYw8UTD1F1kU+nGJ0/OZvH1JmlVUI4bGvgVEWeX1NcHjA765NvUoRVuCPlt8PrEpZ1thSsk1jg==", "type": "package", "path": "system.data.sqlite.core/1.0.119", "files": [".nupkg.metadata", ".signature.p7s", "system.data.sqlite.core.1.0.119.nupkg.sha512", "system.data.sqlite.core.nuspec"]}, "System.Diagnostics.EventLog/10.0.0-preview.7.25380.108": {"sha512": "kKFLata6tLJ/1+jJTbsE9YZu/zeOGO+9ZeukQ3uni5flEjcPpjqGxtmqOx/IFUecMQjNGheNEVyC1KaMl+aZMg==", "type": "package", "path": "system.diagnostics.eventlog/10.0.0-preview.7.25380.108", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.EventLog.targets", "lib/net10.0/System.Diagnostics.EventLog.dll", "lib/net10.0/System.Diagnostics.EventLog.xml", "lib/net462/System.Diagnostics.EventLog.dll", "lib/net462/System.Diagnostics.EventLog.xml", "lib/net8.0/System.Diagnostics.EventLog.dll", "lib/net8.0/System.Diagnostics.EventLog.xml", "lib/net9.0/System.Diagnostics.EventLog.dll", "lib/net9.0/System.Diagnostics.EventLog.xml", "lib/netstandard2.0/System.Diagnostics.EventLog.dll", "lib/netstandard2.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net10.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net10.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net10.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.xml", "system.diagnostics.eventlog.10.0.0-preview.7.25380.108.nupkg.sha512", "system.diagnostics.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.PerformanceCounter/9.0.8": {"sha512": "9jQDqjiJ9VF9M2eVdT09gfU8yg5mhqEkfiZeB3hMjlYApNVJK6vXt9u5T8Oj5Hc+H9zNmrRzTjkXtEF5HcbAfg==", "type": "package", "path": "system.diagnostics.performancecounter/9.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.PerformanceCounter.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.PerformanceCounter.targets", "lib/net462/System.Diagnostics.PerformanceCounter.dll", "lib/net462/System.Diagnostics.PerformanceCounter.xml", "lib/net8.0/System.Diagnostics.PerformanceCounter.dll", "lib/net8.0/System.Diagnostics.PerformanceCounter.xml", "lib/net9.0/System.Diagnostics.PerformanceCounter.dll", "lib/net9.0/System.Diagnostics.PerformanceCounter.xml", "lib/netstandard2.0/System.Diagnostics.PerformanceCounter.dll", "lib/netstandard2.0/System.Diagnostics.PerformanceCounter.xml", "runtimes/win/lib/net8.0/System.Diagnostics.PerformanceCounter.dll", "runtimes/win/lib/net8.0/System.Diagnostics.PerformanceCounter.xml", "runtimes/win/lib/net9.0/System.Diagnostics.PerformanceCounter.dll", "runtimes/win/lib/net9.0/System.Diagnostics.PerformanceCounter.xml", "system.diagnostics.performancecounter.9.0.8.nupkg.sha512", "system.diagnostics.performancecounter.nuspec", "useSharedDesignerContext.txt"]}, "System.DirectoryServices.Protocols/8.0.0": {"sha512": "puwJxURHDrYLGTQdsHyeMS72ClTqYa4lDYz6LHSbkZEk5hq8H8JfsO4MyYhB5BMMxg93jsQzLUwrnCumj11UIg==", "type": "package", "path": "system.directoryservices.protocols/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.DirectoryServices.Protocols.targets", "lib/net462/_._", "lib/net6.0/System.DirectoryServices.Protocols.dll", "lib/net6.0/System.DirectoryServices.Protocols.xml", "lib/net7.0/System.DirectoryServices.Protocols.dll", "lib/net7.0/System.DirectoryServices.Protocols.xml", "lib/net8.0/System.DirectoryServices.Protocols.dll", "lib/net8.0/System.DirectoryServices.Protocols.xml", "lib/netstandard2.0/System.DirectoryServices.Protocols.dll", "lib/netstandard2.0/System.DirectoryServices.Protocols.xml", "runtimes/linux/lib/net6.0/System.DirectoryServices.Protocols.dll", "runtimes/linux/lib/net6.0/System.DirectoryServices.Protocols.xml", "runtimes/linux/lib/net7.0/System.DirectoryServices.Protocols.dll", "runtimes/linux/lib/net7.0/System.DirectoryServices.Protocols.xml", "runtimes/linux/lib/net8.0/System.DirectoryServices.Protocols.dll", "runtimes/linux/lib/net8.0/System.DirectoryServices.Protocols.xml", "runtimes/osx/lib/net6.0/System.DirectoryServices.Protocols.dll", "runtimes/osx/lib/net6.0/System.DirectoryServices.Protocols.xml", "runtimes/osx/lib/net7.0/System.DirectoryServices.Protocols.dll", "runtimes/osx/lib/net7.0/System.DirectoryServices.Protocols.xml", "runtimes/osx/lib/net8.0/System.DirectoryServices.Protocols.dll", "runtimes/osx/lib/net8.0/System.DirectoryServices.Protocols.xml", "runtimes/win/lib/net6.0/System.DirectoryServices.Protocols.dll", "runtimes/win/lib/net6.0/System.DirectoryServices.Protocols.xml", "runtimes/win/lib/net7.0/System.DirectoryServices.Protocols.dll", "runtimes/win/lib/net7.0/System.DirectoryServices.Protocols.xml", "runtimes/win/lib/net8.0/System.DirectoryServices.Protocols.dll", "runtimes/win/lib/net8.0/System.DirectoryServices.Protocols.xml", "system.directoryservices.protocols.8.0.0.nupkg.sha512", "system.directoryservices.protocols.nuspec", "useSharedDesignerContext.txt"]}, "System.IdentityModel.Tokens.Jwt/8.0.1": {"sha512": "GJw3bYkWpOgvN3tJo5X4lYUeIFA2HD293FPUhKmp7qxS+g5ywAb34Dnd3cDAFLkcMohy5XTpoaZ4uAHuw0uSPQ==", "type": "package", "path": "system.identitymodel.tokens.jwt/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/System.IdentityModel.Tokens.Jwt.dll", "lib/net462/System.IdentityModel.Tokens.Jwt.xml", "lib/net472/System.IdentityModel.Tokens.Jwt.dll", "lib/net472/System.IdentityModel.Tokens.Jwt.xml", "lib/net6.0/System.IdentityModel.Tokens.Jwt.dll", "lib/net6.0/System.IdentityModel.Tokens.Jwt.xml", "lib/net8.0/System.IdentityModel.Tokens.Jwt.dll", "lib/net8.0/System.IdentityModel.Tokens.Jwt.xml", "lib/net9.0/System.IdentityModel.Tokens.Jwt.dll", "lib/net9.0/System.IdentityModel.Tokens.Jwt.xml", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.xml", "system.identitymodel.tokens.jwt.8.0.1.nupkg.sha512", "system.identitymodel.tokens.jwt.nuspec"]}, "System.Management/9.0.8": {"sha512": "D7icHff0OpEEIOLsTgnPL+Ff1VmJ3tzA8y20LNKgv4hx1MFMA3XGAVlWMCkLQfBkyvEwib6rMG6nP27lf98KRA==", "type": "package", "path": "system.management/9.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Management.targets", "lib/net462/_._", "lib/net8.0/System.Management.dll", "lib/net8.0/System.Management.xml", "lib/net9.0/System.Management.dll", "lib/net9.0/System.Management.xml", "lib/netstandard2.0/System.Management.dll", "lib/netstandard2.0/System.Management.xml", "runtimes/win/lib/net8.0/System.Management.dll", "runtimes/win/lib/net8.0/System.Management.xml", "runtimes/win/lib/net9.0/System.Management.dll", "runtimes/win/lib/net9.0/System.Management.xml", "system.management.9.0.8.nupkg.sha512", "system.management.nuspec", "useSharedDesignerContext.txt"]}, "System.Memory.Data/8.0.1": {"sha512": "BVYuec3jV23EMRDeR7Dr1/qhx7369dZzJ9IWy2xylvb4YfXsrUxspWc4UWYid/tj4zZK58uGZqn2WQiaDMhmAg==", "type": "package", "path": "system.memory.data/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Memory.Data.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Memory.Data.targets", "lib/net462/System.Memory.Data.dll", "lib/net462/System.Memory.Data.xml", "lib/net6.0/System.Memory.Data.dll", "lib/net6.0/System.Memory.Data.xml", "lib/net7.0/System.Memory.Data.dll", "lib/net7.0/System.Memory.Data.xml", "lib/net8.0/System.Memory.Data.dll", "lib/net8.0/System.Memory.Data.xml", "lib/netstandard2.0/System.Memory.Data.dll", "lib/netstandard2.0/System.Memory.Data.xml", "system.memory.data.8.0.1.nupkg.sha512", "system.memory.data.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.Pkcs/9.0.4": {"sha512": "cUFTcMlz/Qw9s90b2wnWSCvHdjv51Bau9FQqhsr4TlwSe1OX+7SoXUqphis5G74MLOvMOCghxPPlEqOdCrVVGA==", "type": "package", "path": "system.security.cryptography.pkcs/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.Pkcs.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Pkcs.targets", "lib/net462/System.Security.Cryptography.Pkcs.dll", "lib/net462/System.Security.Cryptography.Pkcs.xml", "lib/net8.0/System.Security.Cryptography.Pkcs.dll", "lib/net8.0/System.Security.Cryptography.Pkcs.xml", "lib/net9.0/System.Security.Cryptography.Pkcs.dll", "lib/net9.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net9.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net9.0/System.Security.Cryptography.Pkcs.xml", "system.security.cryptography.pkcs.9.0.4.nupkg.sha512", "system.security.cryptography.pkcs.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.ProtectedData/9.0.8": {"sha512": "w7+KCnqmtDboV8dxTLxlUltasP7AgzNFdTLq1D/ey50ykgXW+CJBIQkzYZjgPzmjKB+/PGGUKYrH7TSbwrDtRw==", "type": "package", "path": "system.security.cryptography.protecteddata/9.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.ProtectedData.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.ProtectedData.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Security.Cryptography.ProtectedData.dll", "lib/net462/System.Security.Cryptography.ProtectedData.xml", "lib/net8.0/System.Security.Cryptography.ProtectedData.dll", "lib/net8.0/System.Security.Cryptography.ProtectedData.xml", "lib/net9.0/System.Security.Cryptography.ProtectedData.dll", "lib/net9.0/System.Security.Cryptography.ProtectedData.xml", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "system.security.cryptography.protecteddata.9.0.8.nupkg.sha512", "system.security.cryptography.protecteddata.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Permissions/8.0.0": {"sha512": "v/BBylw7XevuAsHXoX9dDUUfmBIcUf7Lkz8K3ZXIKz3YRKpw8YftpSir4n4e/jDTKFoaK37AsC3xnk+GNFI1Ow==", "type": "package", "path": "system.security.permissions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Permissions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Permissions.targets", "lib/net462/System.Security.Permissions.dll", "lib/net462/System.Security.Permissions.xml", "lib/net6.0/System.Security.Permissions.dll", "lib/net6.0/System.Security.Permissions.xml", "lib/net7.0/System.Security.Permissions.dll", "lib/net7.0/System.Security.Permissions.xml", "lib/net8.0/System.Security.Permissions.dll", "lib/net8.0/System.Security.Permissions.xml", "lib/netstandard2.0/System.Security.Permissions.dll", "lib/netstandard2.0/System.Security.Permissions.xml", "system.security.permissions.8.0.0.nupkg.sha512", "system.security.permissions.nuspec", "useSharedDesignerContext.txt"]}, "System.Windows.Extensions/8.0.0": {"sha512": "Obg3a90MkOw9mYKxrardLpY2u0axDMrSmy4JCdq2cYbelM2cUwmUir5Bomvd1yxmPL9h5LVHU1tuKBZpUjfASg==", "type": "package", "path": "system.windows.extensions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net6.0/System.Windows.Extensions.dll", "lib/net6.0/System.Windows.Extensions.xml", "lib/net7.0/System.Windows.Extensions.dll", "lib/net7.0/System.Windows.Extensions.xml", "lib/net8.0/System.Windows.Extensions.dll", "lib/net8.0/System.Windows.Extensions.xml", "runtimes/win/lib/net6.0/System.Windows.Extensions.dll", "runtimes/win/lib/net6.0/System.Windows.Extensions.xml", "runtimes/win/lib/net7.0/System.Windows.Extensions.dll", "runtimes/win/lib/net7.0/System.Windows.Extensions.xml", "runtimes/win/lib/net8.0/System.Windows.Extensions.dll", "runtimes/win/lib/net8.0/System.Windows.Extensions.xml", "system.windows.extensions.8.0.0.nupkg.sha512", "system.windows.extensions.nuspec", "useSharedDesignerContext.txt"]}, "WorkQueue/1.3.0": {"sha512": "Gs01Z2+uwtU23WJN67uMdo1819YcFdT1LGeRnONXb0BV79lxbMwEdsieTQbTFpGDvRSXDB7SBMDO68zTnB9mMQ==", "type": "package", "path": "workqueue/1.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net40/WorkQueue.dll", "lib/net40/WorkQueue.xml", "lib/net45/WorkQueue.dll", "lib/net45/WorkQueue.xml", "lib/netstandard2.0/WorkQueue.dll", "lib/netstandard2.0/WorkQueue.xml", "workqueue.1.3.0.nupkg.sha512", "workqueue.nuspec"]}, "ZstdSharp.Port/0.8.5": {"sha512": "TR4j17WeVSEb3ncgL2NqlXEqcy04I+Kk9CaebNDplUeL8XOgjkZ7fP4Wg4grBdPLIqsV86p2QaXTkZoRMVOsew==", "type": "package", "path": "zstdsharp.port/0.8.5", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/ZstdSharp.dll", "lib/net5.0/ZstdSharp.dll", "lib/net6.0/ZstdSharp.dll", "lib/net7.0/ZstdSharp.dll", "lib/net8.0/ZstdSharp.dll", "lib/net9.0/ZstdSharp.dll", "lib/netcoreapp3.1/ZstdSharp.dll", "lib/netstandard2.0/ZstdSharp.dll", "lib/netstandard2.1/ZstdSharp.dll", "zstdsharp.port.0.8.5.nupkg.sha512", "zstdsharp.port.nuspec"]}, "SSIC.Entity/1.0.0": {"type": "project", "path": "../SSIC.Entity/SSIC.Entity.csproj", "msbuildProject": "../SSIC.Entity/SSIC.Entity.csproj"}, "SSIC.Infrastructure/1.0.0": {"type": "project", "path": "../SSIC.Infrastructure/SSIC.Infrastructure.csproj", "msbuildProject": "../SSIC.Infrastructure/SSIC.Infrastructure.csproj"}, "SSIC.Utilities/1.0.0": {"type": "project", "path": "../SSIC.Utilities/SSIC.Utilities.csproj", "msbuildProject": "../SSIC.Utilities/SSIC.Utilities.csproj"}}, "projectFileDependencyGroups": {"net10.0": ["Microsoft.DependencyValidation.Analyzers >= 0.11.0", "Microsoft.VisualStudio.Azure.Containers.Tools.Targets >= 1.22.1", "SSIC.Infrastructure >= 1.0.0", "SSIC.Utilities >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\SSIC\\Host\\SSIC.DevTools\\SSIC.DevTools.csproj", "projectName": "SSIC.DevTools", "projectPath": "F:\\SSIC\\Host\\SSIC.DevTools\\SSIC.DevTools.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\SSIC\\Host\\SSIC.DevTools\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net10.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "http://mes:10881/repository/MESCORE": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net10.0": {"targetAlias": "net10.0", "projectReferences": {"F:\\SSIC\\Host\\SSIC.Infrastructure\\SSIC.Infrastructure.csproj": {"projectPath": "F:\\SSIC\\Host\\SSIC.Infrastructure\\SSIC.Infrastructure.csproj"}, "F:\\SSIC\\Host\\SSIC.Utilities\\SSIC.Utilities.csproj": {"projectPath": "F:\\SSIC\\Host\\SSIC.Utilities\\SSIC.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net10.0": {"targetAlias": "net10.0", "dependencies": {"Microsoft.DependencyValidation.Analyzers": {"target": "Package", "version": "[0.11.0, )"}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": {"target": "Package", "version": "[1.22.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.6.25358.103/PortableRuntimeIdentifierGraph.json", "packagesToPrune": {"Microsoft.AspNetCore": "(, 10.0.32767]", "Microsoft.AspNetCore.Antiforgery": "(, 10.0.32767]", "Microsoft.AspNetCore.App": "(, 10.0.32767]", "Microsoft.AspNetCore.Authentication": "(, 10.0.32767]", "Microsoft.AspNetCore.Authentication.Abstractions": "(, 10.0.32767]", "Microsoft.AspNetCore.Authentication.BearerToken": "(, 10.0.32767]", "Microsoft.AspNetCore.Authentication.Cookies": "(, 10.0.32767]", "Microsoft.AspNetCore.Authentication.Core": "(, 10.0.32767]", "Microsoft.AspNetCore.Authentication.OAuth": "(, 10.0.32767]", "Microsoft.AspNetCore.Authorization": "(, 10.0.32767]", "Microsoft.AspNetCore.Authorization.Policy": "(, 10.0.32767]", "Microsoft.AspNetCore.Components": "(, 10.0.32767]", "Microsoft.AspNetCore.Components.Authorization": "(, 10.0.32767]", "Microsoft.AspNetCore.Components.Endpoints": "(, 10.0.32767]", "Microsoft.AspNetCore.Components.Forms": "(, 10.0.32767]", "Microsoft.AspNetCore.Components.Server": "(, 10.0.32767]", "Microsoft.AspNetCore.Components.Web": "(, 10.0.32767]", "Microsoft.AspNetCore.Connections.Abstractions": "(, 10.0.32767]", "Microsoft.AspNetCore.CookiePolicy": "(, 10.0.32767]", "Microsoft.AspNetCore.Cors": "(, 10.0.32767]", "Microsoft.AspNetCore.Cryptography.Internal": "(, 10.0.32767]", "Microsoft.AspNetCore.Cryptography.KeyDerivation": "(, 10.0.32767]", "Microsoft.AspNetCore.DataProtection": "(, 10.0.32767]", "Microsoft.AspNetCore.DataProtection.Abstractions": "(, 10.0.32767]", "Microsoft.AspNetCore.DataProtection.Extensions": "(, 10.0.32767]", "Microsoft.AspNetCore.Diagnostics": "(, 10.0.32767]", "Microsoft.AspNetCore.Diagnostics.Abstractions": "(, 10.0.32767]", "Microsoft.AspNetCore.Diagnostics.HealthChecks": "(, 10.0.32767]", "Microsoft.AspNetCore.HostFiltering": "(, 10.0.32767]", "Microsoft.AspNetCore.Hosting": "(, 10.0.32767]", "Microsoft.AspNetCore.Hosting.Abstractions": "(, 10.0.32767]", "Microsoft.AspNetCore.Hosting.Server.Abstractions": "(, 10.0.32767]", "Microsoft.AspNetCore.Html.Abstractions": "(, 10.0.32767]", "Microsoft.AspNetCore.Http": "(, 10.0.32767]", "Microsoft.AspNetCore.Http.Abstractions": "(, 10.0.32767]", "Microsoft.AspNetCore.Http.Connections": "(, 10.0.32767]", "Microsoft.AspNetCore.Http.Connections.Common": "(, 10.0.32767]", "Microsoft.AspNetCore.Http.Extensions": "(, 10.0.32767]", "Microsoft.AspNetCore.Http.Features": "(, 10.0.32767]", "Microsoft.AspNetCore.Http.Results": "(, 10.0.32767]", "Microsoft.AspNetCore.HttpLogging": "(, 10.0.32767]", "Microsoft.AspNetCore.HttpOverrides": "(, 10.0.32767]", "Microsoft.AspNetCore.HttpsPolicy": "(, 10.0.32767]", "Microsoft.AspNetCore.Identity": "(, 10.0.32767]", "Microsoft.AspNetCore.Localization": "(, 10.0.32767]", "Microsoft.AspNetCore.Localization.Routing": "(, 10.0.32767]", "Microsoft.AspNetCore.Metadata": "(, 10.0.32767]", "Microsoft.AspNetCore.Mvc": "(, 10.0.32767]", "Microsoft.AspNetCore.Mvc.Abstractions": "(, 10.0.32767]", "Microsoft.AspNetCore.Mvc.ApiExplorer": "(, 10.0.32767]", "Microsoft.AspNetCore.Mvc.Core": "(, 10.0.32767]", "Microsoft.AspNetCore.Mvc.Cors": "(, 10.0.32767]", "Microsoft.AspNetCore.Mvc.DataAnnotations": "(, 10.0.32767]", "Microsoft.AspNetCore.Mvc.Formatters.Json": "(, 10.0.32767]", "Microsoft.AspNetCore.Mvc.Formatters.Xml": "(, 10.0.32767]", "Microsoft.AspNetCore.Mvc.Localization": "(, 10.0.32767]", "Microsoft.AspNetCore.Mvc.Razor": "(, 10.0.32767]", "Microsoft.AspNetCore.Mvc.RazorPages": "(, 10.0.32767]", "Microsoft.AspNetCore.Mvc.TagHelpers": "(, 10.0.32767]", "Microsoft.AspNetCore.Mvc.ViewFeatures": "(, 10.0.32767]", "Microsoft.AspNetCore.OutputCaching": "(, 10.0.32767]", "Microsoft.AspNetCore.RateLimiting": "(, 10.0.32767]", "Microsoft.AspNetCore.Razor": "(, 10.0.32767]", "Microsoft.AspNetCore.Razor.Runtime": "(, 10.0.32767]", "Microsoft.AspNetCore.RequestDecompression": "(, 10.0.32767]", "Microsoft.AspNetCore.ResponseCaching": "(, 10.0.32767]", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "(, 10.0.32767]", "Microsoft.AspNetCore.ResponseCompression": "(, 10.0.32767]", "Microsoft.AspNetCore.Rewrite": "(, 10.0.32767]", "Microsoft.AspNetCore.Routing": "(, 10.0.32767]", "Microsoft.AspNetCore.Routing.Abstractions": "(, 10.0.32767]", "Microsoft.AspNetCore.Server.HttpSys": "(, 10.0.32767]", "Microsoft.AspNetCore.Server.IIS": "(, 10.0.32767]", "Microsoft.AspNetCore.Server.IISIntegration": "(, 10.0.32767]", "Microsoft.AspNetCore.Server.Kestrel": "(, 10.0.32767]", "Microsoft.AspNetCore.Server.Kestrel.Core": "(, 10.0.32767]", "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes": "(, 10.0.32767]", "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic": "(, 10.0.32767]", "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets": "(, 10.0.32767]", "Microsoft.AspNetCore.Session": "(, 10.0.32767]", "Microsoft.AspNetCore.SignalR": "(, 10.0.32767]", "Microsoft.AspNetCore.SignalR.Common": "(, 10.0.32767]", "Microsoft.AspNetCore.SignalR.Core": "(, 10.0.32767]", "Microsoft.AspNetCore.SignalR.Protocols.Json": "(, 10.0.32767]", "Microsoft.AspNetCore.StaticAssets": "(, 10.0.32767]", "Microsoft.AspNetCore.StaticFiles": "(, 10.0.32767]", "Microsoft.AspNetCore.WebSockets": "(, 10.0.32767]", "Microsoft.AspNetCore.WebUtilities": "(, 10.0.32767]", "Microsoft.CSharp": "(, 4.7.32767]", "Microsoft.Extensions.Caching.Abstractions": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Caching.Memory": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Configuration": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Configuration.Abstractions": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Configuration.Binder": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Configuration.CommandLine": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Configuration.EnvironmentVariables": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Configuration.FileExtensions": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Configuration.Ini": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Configuration.Json": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Configuration.KeyPerFile": "(, 10.0.32767]", "Microsoft.Extensions.Configuration.UserSecrets": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Configuration.Xml": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.DependencyInjection": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.DependencyInjection.Abstractions": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Diagnostics": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Diagnostics.Abstractions": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Diagnostics.HealthChecks": "(, 10.0.32767]", "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "(, 10.0.32767]", "Microsoft.Extensions.Features": "(, 10.0.32767]", "Microsoft.Extensions.FileProviders.Abstractions": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.FileProviders.Composite": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.FileProviders.Embedded": "(, 10.0.32767]", "Microsoft.Extensions.FileProviders.Physical": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.FileSystemGlobbing": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Hosting": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Hosting.Abstractions": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Http": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Identity.Core": "(, 10.0.32767]", "Microsoft.Extensions.Identity.Stores": "(, 10.0.32767]", "Microsoft.Extensions.Localization": "(, 10.0.32767]", "Microsoft.Extensions.Localization.Abstractions": "(, 10.0.32767]", "Microsoft.Extensions.Logging": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Logging.Abstractions": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Logging.Configuration": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Logging.Console": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Logging.Debug": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Logging.EventLog": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Logging.EventSource": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Logging.TraceSource": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.ObjectPool": "(, 10.0.32767]", "Microsoft.Extensions.Options": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Options.ConfigurationExtensions": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Options.DataAnnotations": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Primitives": "(, 10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Validation": "(, 10.0.32767]", "Microsoft.Extensions.WebEncoders": "(, 10.0.32767]", "Microsoft.JSInterop": "(, 10.0.32767]", "Microsoft.Net.Http.Headers": "(, 10.0.32767]", "Microsoft.VisualBasic": "(, 10.4.32767]", "Microsoft.Win32.Primitives": "(, 4.3.32767]", "Microsoft.Win32.Registry": "(, 5.0.32767]", "runtime.any.System.Collections": "(, 4.3.32767]", "runtime.any.System.Diagnostics.Tools": "(, 4.3.32767]", "runtime.any.System.Diagnostics.Tracing": "(, 4.3.32767]", "runtime.any.System.Globalization": "(, 4.3.32767]", "runtime.any.System.Globalization.Calendars": "(, 4.3.32767]", "runtime.any.System.IO": "(, 4.3.32767]", "runtime.any.System.Reflection": "(, 4.3.32767]", "runtime.any.System.Reflection.Extensions": "(, 4.3.32767]", "runtime.any.System.Reflection.Primitives": "(, 4.3.32767]", "runtime.any.System.Resources.ResourceManager": "(, 4.3.32767]", "runtime.any.System.Runtime": "(, 4.3.32767]", "runtime.any.System.Runtime.Handles": "(, 4.3.32767]", "runtime.any.System.Runtime.InteropServices": "(, 4.3.32767]", "runtime.any.System.Text.Encoding": "(, 4.3.32767]", "runtime.any.System.Text.Encoding.Extensions": "(, 4.3.32767]", "runtime.any.System.Threading.Tasks": "(, 4.3.32767]", "runtime.any.System.Threading.Timer": "(, 4.3.32767]", "runtime.aot.System.Collections": "(, 4.3.32767]", "runtime.aot.System.Diagnostics.Tools": "(, 4.3.32767]", "runtime.aot.System.Diagnostics.Tracing": "(, 4.3.32767]", "runtime.aot.System.Globalization": "(, 4.3.32767]", "runtime.aot.System.Globalization.Calendars": "(, 4.3.32767]", "runtime.aot.System.IO": "(, 4.3.32767]", "runtime.aot.System.Reflection": "(, 4.3.32767]", "runtime.aot.System.Reflection.Extensions": "(, 4.3.32767]", "runtime.aot.System.Reflection.Primitives": "(, 4.3.32767]", "runtime.aot.System.Resources.ResourceManager": "(, 4.3.32767]", "runtime.aot.System.Runtime": "(, 4.3.32767]", "runtime.aot.System.Runtime.Handles": "(, 4.3.32767]", "runtime.aot.System.Runtime.InteropServices": "(, 4.3.32767]", "runtime.aot.System.Text.Encoding": "(, 4.3.32767]", "runtime.aot.System.Text.Encoding.Extensions": "(, 4.3.32767]", "runtime.aot.System.Threading.Tasks": "(, 4.3.32767]", "runtime.aot.System.Threading.Timer": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.unix.Microsoft.Win32.Primitives": "(, 4.3.32767]", "runtime.unix.System.Console": "(, 4.3.32767]", "runtime.unix.System.Diagnostics.Debug": "(, 4.3.32767]", "runtime.unix.System.IO.FileSystem": "(, 4.3.32767]", "runtime.unix.System.Net.Primitives": "(, 4.3.32767]", "runtime.unix.System.Net.Sockets": "(, 4.3.32767]", "runtime.unix.System.Private.Uri": "(, 4.3.32767]", "runtime.unix.System.Runtime.Extensions": "(, 4.3.32767]", "runtime.win.Microsoft.Win32.Primitives": "(, 4.3.32767]", "runtime.win.System.Console": "(, 4.3.32767]", "runtime.win.System.Diagnostics.Debug": "(, 4.3.32767]", "runtime.win.System.IO.FileSystem": "(, 4.3.32767]", "runtime.win.System.Net.Primitives": "(, 4.3.32767]", "runtime.win.System.Net.Sockets": "(, 4.3.32767]", "runtime.win.System.Runtime.Extensions": "(, 4.3.32767]", "runtime.win10-arm-aot.runtime.native.System.IO.Compression": "(, 4.0.32767]", "runtime.win10-arm64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.win10-x64-aot.runtime.native.System.IO.Compression": "(, 4.0.32767]", "runtime.win10-x86-aot.runtime.native.System.IO.Compression": "(, 4.0.32767]", "runtime.win7-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.win7-x86.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.win7.System.Private.Uri": "(, 4.3.32767]", "runtime.win8-arm.runtime.native.System.IO.Compression": "(, 4.3.32767]", "System.AppContext": "(, 4.3.32767]", "System.Buffers": "(, 5.0.32767]", "System.Collections": "(, 4.3.32767]", "System.Collections.Concurrent": "(, 4.3.32767]", "System.Collections.Immutable": "(, 10.0.0-preview.6.25358.103]", "System.Collections.NonGeneric": "(, 4.3.32767]", "System.Collections.Specialized": "(, 4.3.32767]", "System.ComponentModel": "(, 4.3.32767]", "System.ComponentModel.Annotations": "(, 4.3.32767]", "System.ComponentModel.EventBasedAsync": "(, 4.3.32767]", "System.ComponentModel.Primitives": "(, 4.3.32767]", "System.ComponentModel.TypeConverter": "(, 4.3.32767]", "System.Console": "(, 4.3.32767]", "System.Data.Common": "(, 4.3.32767]", "System.Data.DataSetExtensions": "(, 4.4.32767]", "System.Diagnostics.Contracts": "(, 4.3.32767]", "System.Diagnostics.Debug": "(, 4.3.32767]", "System.Diagnostics.DiagnosticSource": "(, 10.0.0-preview.6.25358.103]", "System.Diagnostics.EventLog": "(, 10.0.0-preview.6.25358.103]", "System.Diagnostics.FileVersionInfo": "(, 4.3.32767]", "System.Diagnostics.Process": "(, 4.3.32767]", "System.Diagnostics.StackTrace": "(, 4.3.32767]", "System.Diagnostics.TextWriterTraceListener": "(, 4.3.32767]", "System.Diagnostics.Tools": "(, 4.3.32767]", "System.Diagnostics.TraceSource": "(, 4.3.32767]", "System.Diagnostics.Tracing": "(, 4.3.32767]", "System.Drawing.Primitives": "(, 4.3.32767]", "System.Dynamic.Runtime": "(, 4.3.32767]", "System.Formats.Asn1": "(, 10.0.0-preview.6.25358.103]", "System.Formats.Cbor": "(, 10.0.0-preview.6.25358.103]", "System.Formats.Tar": "(, 10.0.0-preview.6.25358.103]", "System.Globalization": "(, 4.3.32767]", "System.Globalization.Calendars": "(, 4.3.32767]", "System.Globalization.Extensions": "(, 4.3.32767]", "System.IO": "(, 4.3.32767]", "System.IO.Compression": "(, 4.3.32767]", "System.IO.Compression.ZipFile": "(, 4.3.32767]", "System.IO.FileSystem": "(, 4.3.32767]", "System.IO.FileSystem.AccessControl": "(, 4.4.32767]", "System.IO.FileSystem.DriveInfo": "(, 4.3.32767]", "System.IO.FileSystem.Primitives": "(, 4.3.32767]", "System.IO.FileSystem.Watcher": "(, 4.3.32767]", "System.IO.IsolatedStorage": "(, 4.3.32767]", "System.IO.MemoryMappedFiles": "(, 4.3.32767]", "System.IO.Pipelines": "(, 10.0.0-preview.6.25358.103]", "System.IO.Pipes": "(, 4.3.32767]", "System.IO.Pipes.AccessControl": "(, 5.0.32767]", "System.IO.UnmanagedMemoryStream": "(, 4.3.32767]", "System.Linq": "(, 4.3.32767]", "System.Linq.AsyncEnumerable": "(, 10.0.0-preview.6.25358.103]", "System.Linq.Expressions": "(, 4.3.32767]", "System.Linq.Parallel": "(, 4.3.32767]", "System.Linq.Queryable": "(, 4.3.32767]", "System.Memory": "(, 5.0.32767]", "System.Net.Http": "(, 4.3.32767]", "System.Net.Http.Json": "(, 10.0.0-preview.6.25358.103]", "System.Net.NameResolution": "(, 4.3.32767]", "System.Net.NetworkInformation": "(, 4.3.32767]", "System.Net.Ping": "(, 4.3.32767]", "System.Net.Primitives": "(, 4.3.32767]", "System.Net.Requests": "(, 4.3.32767]", "System.Net.Security": "(, 4.3.32767]", "System.Net.ServerSentEvents": "(, 10.0.0-preview.6.25358.103]", "System.Net.Sockets": "(, 4.3.32767]", "System.Net.WebHeaderCollection": "(, 4.3.32767]", "System.Net.WebSockets": "(, 4.3.32767]", "System.Net.WebSockets.Client": "(, 4.3.32767]", "System.Numerics.Vectors": "(, 5.0.32767]", "System.ObjectModel": "(, 4.3.32767]", "System.Private.DataContractSerialization": "(, 4.3.32767]", "System.Private.Uri": "(, 4.3.32767]", "System.Reflection": "(, 4.3.32767]", "System.Reflection.DispatchProxy": "(, 6.0.32767]", "System.Reflection.Emit": "(, 4.7.32767]", "System.Reflection.Emit.ILGeneration": "(, 4.7.32767]", "System.Reflection.Emit.Lightweight": "(, 4.7.32767]", "System.Reflection.Extensions": "(, 4.3.32767]", "System.Reflection.Metadata": "(, 10.0.0-preview.6.25358.103]", "System.Reflection.Primitives": "(, 4.3.32767]", "System.Reflection.TypeExtensions": "(, 4.3.32767]", "System.Resources.Reader": "(, 4.3.32767]", "System.Resources.ResourceManager": "(, 4.3.32767]", "System.Resources.Writer": "(, 4.3.32767]", "System.Runtime": "(, 4.3.32767]", "System.Runtime.CompilerServices.Unsafe": "(, 7.0.32767]", "System.Runtime.CompilerServices.VisualC": "(, 4.3.32767]", "System.Runtime.Extensions": "(, 4.3.32767]", "System.Runtime.Handles": "(, 4.3.32767]", "System.Runtime.InteropServices": "(, 4.3.32767]", "System.Runtime.InteropServices.RuntimeInformation": "(, 4.3.32767]", "System.Runtime.Loader": "(, 4.3.32767]", "System.Runtime.Numerics": "(, 4.3.32767]", "System.Runtime.Serialization.Formatters": "(, 4.3.32767]", "System.Runtime.Serialization.Json": "(, 4.3.32767]", "System.Runtime.Serialization.Primitives": "(, 4.3.32767]", "System.Runtime.Serialization.Xml": "(, 4.3.32767]", "System.Security.AccessControl": "(, 6.0.32767]", "System.Security.Claims": "(, 4.3.32767]", "System.Security.Cryptography.Algorithms": "(, 4.3.32767]", "System.Security.Cryptography.Cng": "(, 5.0.32767]", "System.Security.Cryptography.Csp": "(, 4.3.32767]", "System.Security.Cryptography.Encoding": "(, 4.3.32767]", "System.Security.Cryptography.OpenSsl": "(, 5.0.32767]", "System.Security.Cryptography.Primitives": "(, 4.3.32767]", "System.Security.Cryptography.X509Certificates": "(, 4.3.32767]", "System.Security.Cryptography.Xml": "(, 10.0.0-preview.6.25358.103]", "System.Security.Principal": "(, 4.3.32767]", "System.Security.Principal.Windows": "(, 5.0.32767]", "System.Security.SecureString": "(, 4.3.32767]", "System.Text.Encoding": "(, 4.3.32767]", "System.Text.Encoding.CodePages": "(, 10.0.0-preview.6.25358.103]", "System.Text.Encoding.Extensions": "(, 4.3.32767]", "System.Text.Encodings.Web": "(, 10.0.0-preview.6.25358.103]", "System.Text.Json": "(, 10.0.0-preview.6.25358.103]", "System.Text.RegularExpressions": "(, 4.3.32767]", "System.Threading": "(, 4.3.32767]", "System.Threading.AccessControl": "(, 10.0.0-preview.6.25358.103]", "System.Threading.Channels": "(, 10.0.0-preview.6.25358.103]", "System.Threading.Overlapped": "(, 4.3.32767]", "System.Threading.RateLimiting": "(, 10.0.0-preview.6.25358.103]", "System.Threading.Tasks": "(, 4.3.32767]", "System.Threading.Tasks.Dataflow": "(, 10.0.0-preview.6.25358.103]", "System.Threading.Tasks.Extensions": "(, 5.0.32767]", "System.Threading.Tasks.Parallel": "(, 4.3.32767]", "System.Threading.Thread": "(, 4.3.32767]", "System.Threading.ThreadPool": "(, 4.3.32767]", "System.Threading.Timer": "(, 4.3.32767]", "System.ValueTuple": "(, 4.5.32767]", "System.Xml.ReaderWriter": "(, 4.3.32767]", "System.Xml.XDocument": "(, 4.3.32767]", "System.Xml.XmlDocument": "(, 4.3.32767]", "System.Xml.XmlSerializer": "(, 4.3.32767]", "System.Xml.XPath": "(, 4.3.32767]", "System.Xml.XPath.XDocument": "(, 5.0.32767]"}}}}}