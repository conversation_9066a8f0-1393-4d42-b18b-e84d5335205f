<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSIC 代码生成器</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background-color: #fafafa;
        }
        .section h2 {
            color: #555;
            margin-top: 0;
        }
        .button-group {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }
        button {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #545b62;
        }
        .btn-info {
            background-color: #17a2b8;
            color: white;
        }
        .btn-info:hover {
            background-color: #117a8b;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 0 auto 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .entity-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background: white;
            border-radius: 4px;
        }
        .module-item {
            margin-bottom: 15px;
            padding: 10px;
            border: 1px solid #eee;
            border-radius: 4px;
            background: #f9f9f9;
        }
        .module-name {
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        .entity-item {
            display: inline-block;
            margin: 2px 5px;
            padding: 2px 8px;
            background: #e9ecef;
            border-radius: 3px;
            font-size: 12px;
        }
        .config-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
        }
        .config-section h3 {
            margin-top: 0;
            margin-bottom: 15px;
            color: #495057;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #495057;
        }
        .form-group input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .form-group input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }
        .form-description {
            color: #6c757d;
            font-size: 12px;
            margin-top: 5px;
            padding: 8px 12px;
            background-color: #f8f9fa;
            border-left: 3px solid #17a2b8;
            border-radius: 0 4px 4px 0;
            line-height: 1.4;
        }
        .entity-description {
            color: #28a745;
            font-size: 13px;
            margin-top: 5px;
            padding: 6px 12px;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 SSIC 代码生成器</h1>
        
        <div class="section">
            <h2>📋 实体信息</h2>
            <div class="button-group">
                <button class="btn-info" onclick="loadEntities()">刷新实体列表</button>
                <button class="btn-info" onclick="loadTemplates()">查看模板信息</button>
            </div>
            <div id="entityResult" class="result info" style="display: none;"></div>
        </div>

        <div class="section">
            <h2>🏗️ 代码生成</h2>

            <!-- 生成参数配置 -->
            <div class="config-section">
                <h3>生成配置</h3>
                <div class="form-group">
                    <label for="moduleName">模块名称 (可选，为空则生成所有模块):</label>
                    <input type="text" id="moduleName" placeholder="例如: Auth, Sys" />
                </div>
                <div class="form-group">
                    <label for="serviceControllerNames">服务/控制器名称 (可选，多个用逗号分隔):</label>
                    <input type="text" id="serviceControllerNames" placeholder="例如: User,Role,Permission" />
                </div>
                <div class="form-group">
                    <label for="serviceControllerDescriptions">服务/控制器中文描述 (可选，多个用逗号分隔，与上面名称一一对应):</label>
                    <input type="text" id="serviceControllerDescriptions" placeholder="例如: 用户管理,角色管理,权限管理" />
                    <div class="form-description">
                        <strong>📝 生成说明：</strong>输入英文实体名称和对应的中文描述，系统将生成带中文注释的服务和控制器文件<br/>
                        <strong>📁 文件结构：</strong>每个实体将生成以下文件：<br/>
                        &nbsp;&nbsp;• I{实体名}Service.cs（服务接口）<br/>
                        &nbsp;&nbsp;• {实体名}Service.cs（服务实现）<br/>
                        &nbsp;&nbsp;• {实体名}Controller.cs（控制器）<br/>
                        <strong>💡 示例：</strong>输入"User"和"用户管理"将生成带用户管理注释的 IUserService.cs、UserService.cs、UserController.cs
                    </div>
                </div>
            </div>

            <div class="button-group">
                <button class="btn-primary" onclick="generateModules()">生成模块化项目 (推荐)</button>
                <button class="btn-secondary" onclick="generateTraditional()">生成传统项目 (兼容)</button>
                <button class="btn-info" onclick="clearConfig()">清空配置</button>
            </div>
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>正在生成代码，请稍候...</p>
            </div>
            <div id="result" class="result" style="display: none;"></div>
        </div>

        <div class="section">
            <h2>📖 使用说明</h2>
            <div class="info result">
<strong>模块化项目结构 (推荐):</strong>
- 生成符合 SSIC.Modules.{ModuleName} 结构的代码
- 支持 Service 层架构，自动依赖注入
- 包含完整的项目文件和解决方案文件
- 适合大型项目和团队协作

<strong>传统项目结构 (兼容):</strong>
- 兼容旧版本的代码生成方式
- 使用 SSFB 命名空间
- 保持原有的文件夹结构

<strong>使用说明:</strong>
- 可以直接输入实体名称生成代码，无需依赖实体文件夹
- 如果不指定实体名称，会尝试从 SSIC.Entity 文件夹读取现有实体
- 确保 SSIC.Infrastructure/Template 文件夹中有模板文件

<strong>生成规则:</strong>
- 指定实体名称：为每个实体同时生成服务和控制器文件
- 不指定实体名称：生成所有现有实体的服务和控制器文件
            </div>
        </div>
    </div>

    <script>




        async function loadEntities() {
            try {
                showLoading(true);
                const response = await fetch('/api/CodeGenerator/entities');
                const data = await response.json();
                
                if (data.success) {
                    let html = `<strong>实体路径:</strong> ${data.entityPath}\n`;
                    html += `<strong>模块数量:</strong> ${data.moduleCount}\n\n`;
                    
                    data.modules.forEach(module => {
                        html += `<strong>模块: ${module.moduleName}</strong>\n`;
                        html += `实体数量: ${module.entities.length}\n`;
                        html += `实体列表: ${module.entities.map(e => e.entityName).join(', ')}\n\n`;
                    });
                    
                    showResult(html, 'info');
                } else {
                    showResult(`错误: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult(`请求失败: ${error.message}`, 'error');
            } finally {
                showLoading(false);
            }
        }

        async function loadTemplates() {
            try {
                showLoading(true);
                const response = await fetch('/api/CodeGenerator/templates');
                const data = await response.json();
                
                if (data.success) {
                    let html = `<strong>模板路径:</strong> ${data.templatePath}\n`;
                    html += `<strong>模板类型数量:</strong> ${data.templateTypeCount}\n\n`;
                    
                    data.templates.forEach(template => {
                        html += `<strong>模板类型: ${template.templateType}</strong>\n`;
                        html += `模板文件: ${template.templates.map(t => t.templateName).join(', ')}\n\n`;
                    });
                    
                    showResult(html, 'info');
                } else {
                    showResult(`错误: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult(`请求失败: ${error.message}`, 'error');
            } finally {
                showLoading(false);
            }
        }

        async function generateModules() {
            try {
                showLoading(true);

                // 获取配置参数
                const moduleName = document.getElementById('moduleName').value.trim() || null;
                const entityNamesInput = document.getElementById('serviceControllerNames').value.trim();
                const entityDescriptionsInput = document.getElementById('serviceControllerDescriptions').value.trim();

                // 解析数组参数并构建实体信息
                let entities = null;
                if (entityNamesInput) {
                    const entityNames = entityNamesInput.split(',').map(s => s.trim()).filter(s => s);
                    const entityDescriptions = entityDescriptionsInput ?
                        entityDescriptionsInput.split(',').map(s => s.trim()).filter(s => s) : [];

                    entities = entityNames.map((name, index) => ({
                        name: name,
                        description: entityDescriptions[index] || '' // 如果没有对应的描述，使用空字符串
                    }));
                }

                const requestBody = {
                    moduleName: moduleName,
                    entities: entities
                };

                const response = await fetch('/api/CodeGenerator/generate-modules', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });
                const data = await response.json();

                if (data.success) {
                    let html = `✅ ${data.message}\n\n`;
                    html += `生成时间: ${data.timestamp}\n\n`;

                    // 显示生成配置
                    html += `生成配置:\n`;
                    html += `- 模块名称: ${data.request.moduleName || '所有模块'}\n`;
                    html += `- 实体数量: ${data.request.entityCount || '所有实体'}\n`;
                    if (data.request.entityNames && data.request.entityNames.length > 0) {
                        html += `- 指定实体: ${data.request.entityNames.join(', ')}\n`;
                    }
                    html += `\n`;

                    html += `生成的项目结构:\n`;
                    data.generatedStructure.structure.forEach(line => {
                        html += `${line}\n`;
                    });

                    showResult(html, 'success');
                } else {
                    showResult(`❌ ${data.message}\n错误详情: ${data.error}`, 'error');
                }
            } catch (error) {
                showResult(`请求失败: ${error.message}`, 'error');
            } finally {
                showLoading(false);
            }
        }

        async function generateTraditional() {
            try {
                showLoading(true);
                const response = await fetch('/api/CodeGenerator/generate-traditional', {
                    method: 'POST'
                });
                const data = await response.json();
                
                if (data.success) {
                    let html = `✅ ${data.message}\n\n`;
                    html += `生成时间: ${data.timestamp}\n\n`;
                    html += `生成的项目结构:\n`;
                    data.generatedStructure.structure.forEach(line => {
                        html += `${line}\n`;
                    });
                    
                    showResult(html, 'success');
                } else {
                    showResult(`❌ ${data.message}\n错误详情: ${data.error}`, 'error');
                }
            } catch (error) {
                showResult(`请求失败: ${error.message}`, 'error');
            } finally {
                showLoading(false);
            }
        }

        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            const entityResultDiv = document.getElementById('entityResult');
            
            if (type === 'info') {
                entityResultDiv.textContent = message;
                entityResultDiv.className = `result ${type}`;
                entityResultDiv.style.display = 'block';
            } else {
                resultDiv.textContent = message;
                resultDiv.className = `result ${type}`;
                resultDiv.style.display = 'block';
            }
        }

        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        function clearConfig() {
            document.getElementById('moduleName').value = '';
            document.getElementById('serviceControllerNames').value = '';
            document.getElementById('serviceControllerDescriptions').value = '';
            showResult('配置已清空', 'info');
        }

        // 页面加载时自动加载实体信息
        window.onload = function() {
            loadEntities();
        };
    </script>
</body>
</html>
