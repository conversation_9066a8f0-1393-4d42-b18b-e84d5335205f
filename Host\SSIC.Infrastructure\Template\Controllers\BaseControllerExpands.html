﻿/*
 *接口编写处...
 *如果接口需要做Action的权限验证，请在Action上使用属性
 *如: [Permission(ActionPermissionOption.Add)]
 */
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using SSIC.Infrastructure.Authentication;
using SSIC.Infrastructure.BaseController;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using SSIC.Entity.{BusinessName};
using SSIC.Modules.{BusinessName}.Services.Interfaces;

namespace SSIC.Modules.{BusinessName}.Controllers
{
    /// <summary>
    /// {EntityName}控制器扩展类
    /// 在此处添加自定义的API接口方法
    /// </summary>
    public partial class {EntityName}Controller
    {
        // 在这里添加自定义的控制器方法
        //
        // 示例1：带HTTP特性的方法
        // /// <summary>
        // /// 根据名称查询{EntityName}
        // /// </summary>
        // /// <param name="name">名称</param>
        // /// <returns></returns>
        // [HttpGet("by-name/{name}")]
        // public async Task<IActionResult> GetByNameAsync(string name)
        // {
        //     var result = await {EntityName}Service.GetByNameAsync(name);
        //     return Success(result, "查询成功");
        // }
        //
        // 示例2：不带HTTP特性的方法（会自动推断为GET）
        // /// <summary>
        // /// 获取{EntityName}统计信息
        // /// </summary>
        // /// <returns></returns>
        // public IActionResult Get{EntityName}Statistics()
        // {
        //     return Success(new
        //     {
        //         total = 100,
        //         active = 80,
        //         lastUpdated = DateTime.Now
        //     }, "获取统计信息成功");
        // }
        //
        // 示例3：带权限验证的方法
        // /// <summary>
        // /// 自定义业务方法示例
        // /// </summary>
        // /// <returns></returns>
        // [HttpPost("custom")]
        // [Permission(ActionPermissionOption.Add)] // 权限验证示例
        // public async Task<IActionResult> CustomMethodAsync()
        // {
        //     // 实现自定义业务逻辑
        //     return Success("自定义方法执行成功");
        // }
    }
}