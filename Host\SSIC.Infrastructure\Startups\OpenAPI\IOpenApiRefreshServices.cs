using System;
using System.Threading.Tasks;

namespace SSIC.Infrastructure.Startups.OpenAPI
{
    /// <summary>
    /// 统一的OpenAPI刷新服务接口
    /// </summary>
    public interface IUnifiedOpenApiRefreshService
    {
        /// <summary>
        /// 刷新OpenAPI文档
        /// </summary>
        Task RefreshAsync();

        /// <summary>
        /// 获取模块信息
        /// </summary>
        Task<ModuleInfo[]> GetModuleInfoAsync();
    }

    /// <summary>
    /// 简单的OpenAPI刷新服务接口
    /// </summary>
    public interface ISimpleOpenApiRefreshService
    {
        /// <summary>
        /// 简单刷新
        /// </summary>
        Task RefreshAsync();
    }

    /// <summary>
    /// 模块信息
    /// </summary>
    public class ModuleInfo
    {
        public string Name { get; set; } = string.Empty;
        public string AssemblyName { get; set; } = string.Empty;
        public int ControllerCount { get; set; }
        public int ActionCount { get; set; }
        public DateTime LoadTime { get; set; }
        public string[] Controllers { get; set; } = Array.Empty<string>();
    }
}
