/*
 *接口编写处...
 *如果接口需要做Action的权限验证，请在Action上使用属性
 *如: [Permission(ActionPermissionOption.Add)]
 */
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using SSIC.Infrastructure.Authentication;
using SSIC.Infrastructure.BaseController;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using SSIC.Entity.Auth;
using SSIC.Modules.Auth.Services.Interfaces;

namespace SSIC.Modules.Auth.Controllers
{
    /// <summary>
    /// Permission控制器扩展类
    /// 在此处添加自定义的API接口方法
    /// </summary>
    public partial class PermissionController
    {
        /// <summary>
        /// 批量删除权限（带HTTP特性）
        /// </summary>
        /// <param name="permissionIds">权限ID列表</param>
        /// <returns>删除结果</returns>
        [HttpDelete("batch")]
        public IActionResult BatchDeletePermissions([FromBody] int[] permissionIds)
        {
            return Success(new
            {
                deletedIds = permissionIds,
                deletedCount = permissionIds.Length,
                deletedAt = DateTime.Now,
                deletedBy = "系统管理员"
            }, $"成功删除 {permissionIds.Length} 个权限");
        }

        /// <summary>
        /// 获取权限树结构（不带HTTP特性）
        /// </summary>
        /// <returns>权限树</returns>
        public IActionResult GetPermissionTree()
        {
            return Success(new
            {
                tree = new[]
                {
                    new
                    {
                        id = 1,
                        name = "用户管理",
                        children = new[]
                        {
                            new { id = 11, name = "查看用户" },
                            new { id = 12, name = "创建用户" },
                            new { id = 13, name = "编辑用户" },
                            new { id = 14, name = "删除用户" }
                        }
                    },
                    new
                    {
                        id = 2,
                        name = "角色管理",
                        children = new[]
                        {
                            new { id = 21, name = "查看角色" },
                            new { id = 22, name = "创建角色" },
                            new { id = 23, name = "编辑角色" },
                            new { id = 24, name = "删除角色" }
                        }
                    }
                },
                totalPermissions = 8,
                lastUpdated = DateTime.Now
            }, "获取权限树成功");
        }

        /// <summary>
        /// 权限控制器健康检查接口
        /// </summary>
        /// <returns>健康状态</returns>
        //[HttpGet("health")]
        //public override ActionResult Health()
        //{
        //    return base.Health();
        //}
    }
}