using FreeSql.DataAnnotations;
using SSIC.Entity.EntityBase;

namespace SSIC.Entity.Auth
{
    /// <summary>
    /// 用户实体
    /// </summary>
    [Table(Name = "auth_users")]
    public class User : EntityBasic
    {
        /// <summary>
        /// 用户名
        /// </summary>
        [Column(StringLength = 50, IsNullable = false)]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// 邮箱
        /// </summary>
        [Column(StringLength = 100)]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 手机号
        /// </summary>
        [Column(StringLength = 20)]
        public string? Phone { get; set; }

        /// <summary>
        /// 密码哈希
        /// </summary>
        [Column(StringLength = 255, IsNullable = false)]
        public string PasswordHash { get; set; } = string.Empty;

        /// <summary>
        /// 盐值
        /// </summary>
        [Column(StringLength = 50)]
        public string Salt { get; set; } = string.Empty;

        /// <summary>
        /// 全名
        /// </summary>
        [Column(StringLength = 100)]
        public string? FullName { get; set; }

        /// <summary>
        /// 头像URL
        /// </summary>
        [Column(StringLength = 500)]
        public string? Avatar { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 是否邮箱验证
        /// </summary>
        public bool IsEmailVerified { get; set; } = false;

        /// <summary>
        /// 是否手机验证
        /// </summary>
        public bool IsPhoneVerified { get; set; } = false;

        /// <summary>
        /// 最后登录时间
        /// </summary>
        public DateTime? LastLoginAt { get; set; }

        /// <summary>
        /// 最后登录IP
        /// </summary>
        [Column(StringLength = 45)]
        public string? LastLoginIp { get; set; }

        /// <summary>
        /// 登录失败次数
        /// </summary>
        public int LoginFailureCount { get; set; } = 0;

        /// <summary>
        /// 账户锁定到期时间
        /// </summary>
        public DateTime? LockoutEnd { get; set; }

        /// <summary>
        /// 用户角色关联
        /// </summary>
        [Navigate(ManyToMany = typeof(UserRole))]
        public virtual ICollection<Role> Roles { get; set; } = new List<Role>();
    }
}
