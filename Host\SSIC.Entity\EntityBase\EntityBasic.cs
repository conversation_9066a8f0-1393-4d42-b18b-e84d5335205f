﻿using FreeSql.DataAnnotations;

namespace SSIC.Entity.EntityBase
{
    /// <summary>
    /// 表结构常规操作字段
    /// </summary>
    public class EntityBasic
    {
        /// <summary>
        /// ID
        /// </summary>
        [Column(IsPrimary = true)]
        public Guid id { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string creator { get; set; }

        /// <summary>
        /// 创建人ID
        /// </summary>
        public Guid? createid { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? createtime { get; set; } = DateTime.Now;

        /// <summary>
        /// 修改人
        /// </summary>
        public string modifier { get; set; }

        /// <summary>
        /// 修改人ID
        /// </summary>
        public Guid? modifyid { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? modifytime { get; set; }

        /// <summary>
        /// 删除人
        /// </summary>
        public string deleted { get; set; }

        /// <summary>
        /// 删除人ID
        /// </summary>
        public Guid? deleteid { get; set; }

        /// <summary>
        /// 删除时间
        /// </summary>
        public DateTime? deletetime { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool? isdelete { get; set; } = false;

        /// <summary>
        /// 是否禁用
        /// </summary>
        public bool? islock { get; set; } = false;

        /// <summary>
        /// 组织ID
        /// </summary>
        public Guid? orgid { get; set; }

        /// <summary>
        /// 租户ID
        /// </summary>
        public Guid? tenantid { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Column(StringLength = -1)]
        public string remark { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        [Column(StringLength = 255)]
        public string desc { get; set; } //255

        /// <summary>
        /// 排序
        /// </summary>
        public int? ordersort { get; set; }

        /// <summary>
        /// 编码管理
        /// </summary>
        [Column(StringLength = 122)]
        public string code { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        public string name { get; set; }

        /// <summary>
        /// 细表子集
        /// </summary>
        [Column(IsIgnore = true)]
        public List<object> childs { get; set; }
    }
}