using Microsoft.AspNetCore.Mvc;
using Microsoft.OpenApi;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace SSIC.Infrastructure.Startups.Common
{
    /// <summary>
    /// OpenAPI工具类
    /// 统一处理OpenAPI文档生成的公共逻辑
    /// </summary>
    public static class OpenApiUtilities
    {
        /// <summary>
        /// 获取方法的摘要信息
        /// </summary>
        /// <param name="method">方法信息</param>
        /// <returns>摘要信息</returns>
        public static string GetMethodSummary(MethodInfo method)
        {
            // 这里可以从XML文档注释中提取摘要
            // 暂时返回默认格式
            return $"{method.DeclaringType?.Name}.{method.Name}";
        }

        /// <summary>
        /// 获取方法的详细描述
        /// </summary>
        /// <param name="method">方法信息</param>
        /// <returns>详细描述</returns>
        public static string GetMethodDescription(MethodInfo method)
        {
            // 这里可以从XML文档注释中提取描述
            // 暂时返回默认格式
            var httpMethods = ModuleUtilities.GetHttpMethods(method);
            return $"执行 {method.Name} 操作，支持的HTTP方法: {string.Join(", ", httpMethods)}";
        }

        /// <summary>
        /// 构建路由模板
        /// </summary>
        /// <param name="method">方法信息</param>
        /// <param name="controllerRoute">控制器路由</param>
        /// <returns>完整的路由模板</returns>
        public static string BuildRouteTemplate(MethodInfo method, string controllerRoute)
        {
            var routeAttribute = method.GetCustomAttribute<RouteAttribute>();
            if (routeAttribute != null)
            {
                return $"{controllerRoute.TrimEnd('/')}/{routeAttribute.Template?.TrimStart('/')}";
            }

            // 如果没有Route特性，使用方法名作为路由
            return $"{controllerRoute.TrimEnd('/')}/{method.Name}";
        }

        /// <summary>
        /// 创建OpenAPI操作对象
        /// </summary>
        /// <param name="method">方法信息</param>
        /// <param name="moduleName">模块名称</param>
        /// <param name="controllerName">控制器名称</param>
        /// <returns>OpenAPI操作对象</returns>
        public static Microsoft.OpenApi.Models.OpenApiOperation CreateOpenApiOperation(MethodInfo method, string moduleName, string controllerName)
        {
            var operation = new Microsoft.OpenApi.Models.OpenApiOperation
            {
                Summary = GetMethodSummary(method),
                Description = GetMethodDescription(method),
                OperationId = $"{moduleName}_{controllerName}_{method.Name}",
                Tags = new List<Microsoft.OpenApi.Models.OpenApiTag> { new Microsoft.OpenApi.Models.OpenApiTag { Name = moduleName } }
            };

            // 添加参数
            AddParameters(operation, method);

            // 添加响应
            AddResponses(operation, method);

            return operation;
        }

        /// <summary>
        /// 添加参数到OpenAPI操作
        /// </summary>
        /// <param name="operation">OpenAPI操作</param>
        /// <param name="method">方法信息</param>
        private static void AddParameters(Microsoft.OpenApi.Models.OpenApiOperation operation, MethodInfo method)
        {
            var parameters = method.GetParameters();
            foreach (var param in parameters)
            {
                // 跳过复杂类型参数（通常作为请求体）
                if (IsSimpleType(param.ParameterType))
                {
                    operation.Parameters.Add(new Microsoft.OpenApi.Models.OpenApiParameter
                    {
                        Name = param.Name,
                        In = Microsoft.OpenApi.Models.ParameterLocation.Query,
                        Required = !param.HasDefaultValue,
                        Schema = GetSchemaForType(param.ParameterType),
                        Description = $"参数 {param.Name}"
                    });
                }
            }
        }

        /// <summary>
        /// 添加响应到OpenAPI操作
        /// </summary>
        /// <param name="operation">OpenAPI操作</param>
        /// <param name="method">方法信息</param>
        private static void AddResponses(Microsoft.OpenApi.Models.OpenApiOperation operation, MethodInfo method)
        {
            // 添加默认成功响应
            operation.Responses.Add("200", new Microsoft.OpenApi.Models.OpenApiResponse
            {
                Description = "操作成功",
                Content = new Dictionary<string, Microsoft.OpenApi.Models.OpenApiMediaType>
                {
                    ["application/json"] = new Microsoft.OpenApi.Models.OpenApiMediaType
                    {
                        Schema = GetSchemaForType(method.ReturnType)
                    }
                }
            });

            // 添加默认错误响应
            operation.Responses.Add("400", new Microsoft.OpenApi.Models.OpenApiResponse
            {
                Description = "请求参数错误"
            });

            operation.Responses.Add("500", new Microsoft.OpenApi.Models.OpenApiResponse
            {
                Description = "服务器内部错误"
            });
        }

        /// <summary>
        /// 判断是否为简单类型
        /// </summary>
        /// <param name="type">类型</param>
        /// <returns>是否为简单类型</returns>
        private static bool IsSimpleType(Type type)
        {
            return type.IsPrimitive || 
                   type == typeof(string) || 
                   type == typeof(DateTime) || 
                   type == typeof(decimal) || 
                   type == typeof(Guid) ||
                   (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>) && 
                    IsSimpleType(type.GetGenericArguments()[0]));
        }

        /// <summary>
        /// 获取类型的OpenAPI架构
        /// </summary>
        /// <param name="type">类型</param>
        /// <returns>OpenAPI架构</returns>
        private static Microsoft.OpenApi.Models.OpenApiSchema GetSchemaForType(Type type)
        {
            if (type == typeof(string))
                return new Microsoft.OpenApi.Models.OpenApiSchema { Type = "string" };
            if (type == typeof(int) || type == typeof(int?))
                return new Microsoft.OpenApi.Models.OpenApiSchema { Type = "integer", Format = "int32" };
            if (type == typeof(long) || type == typeof(long?))
                return new Microsoft.OpenApi.Models.OpenApiSchema { Type = "integer", Format = "int64" };
            if (type == typeof(bool) || type == typeof(bool?))
                return new Microsoft.OpenApi.Models.OpenApiSchema { Type = "boolean" };
            if (type == typeof(DateTime) || type == typeof(DateTime?))
                return new Microsoft.OpenApi.Models.OpenApiSchema { Type = "string", Format = "date-time" };
            if (type == typeof(decimal) || type == typeof(decimal?))
                return new Microsoft.OpenApi.Models.OpenApiSchema { Type = "number", Format = "decimal" };
            if (type == typeof(Guid) || type == typeof(Guid?))
                return new Microsoft.OpenApi.Models.OpenApiSchema { Type = "string", Format = "uuid" };

            // 对于复杂类型，返回通用对象架构
            return new Microsoft.OpenApi.Models.OpenApiSchema { Type = "object" };
        }

        /// <summary>
        /// 清理动态模块路径（保留系统路径）
        /// </summary>
        /// <param name="document">OpenAPI文档</param>
        public static void ClearDynamicModulePaths(Microsoft.OpenApi.Models.OpenApiDocument document)
        {
            var pathsToRemove = document.Paths.Keys
                .Where(path => path.StartsWith("/api/") && !path.StartsWith("/api/system/"))
                .ToList();

            foreach (var path in pathsToRemove)
            {
                document.Paths.Remove(path);
            }
        }

        /// <summary>
        /// 创建模块标签
        /// </summary>
        /// <param name="document">OpenAPI文档</param>
        /// <param name="moduleName">模块名称</param>
        /// <param name="controllerCount">控制器数量</param>
        public static void AddModuleTag(Microsoft.OpenApi.Models.OpenApiDocument document, string moduleName, int controllerCount)
        {
            var tag = new Microsoft.OpenApi.Models.OpenApiTag
            {
                Name = moduleName,
                Description = $"{moduleName} 模块 (包含 {controllerCount} 个控制器)"
            };

            if (!document.Tags.Any(t => t.Name == moduleName))
            {
                document.Tags.Add(tag);
            }
        }
    }
}
