{"@t":"2025-08-22T01:23:04.4227713Z","@mt":"发现 {Count} 个模块文件","Count":3,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:04.9226338Z","@mt":"动态端点管理器初始化完成","SourceContext":"ApplicationBuilderExtensions","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:04.9273735Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules\\SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Controller.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:04.9300721Z","@mt":"模块 {PluginName} 版本 {Version} 已存在，跳过加载","@l":"Warning","PluginName":"SSIC.Modules.Auth.Controller","Version":"*******","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:04.9312076Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules\\SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Services.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:04.9321281Z","@mt":"模块 {PluginName} 版本 {Version} 已存在，跳过加载","@l":"Warning","PluginName":"SSIC.Modules.Auth.Services","Version":"*******","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:04.9331861Z","@mt":"开始使用AssemblyLoadContext加载插件: {PluginPath}","PluginPath":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules\\SSIC.Modules.Auth.Controller\\SSIC.Modules.Auth.Controller.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:04.9339197Z","@mt":"模块 {PluginName} 版本 {Version} 已存在，跳过加载","@l":"Warning","PluginName":"SSIC.Modules.Auth.Controller","Version":"*******","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"LAPTOP-F6SGUTN5","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:04.9645953Z","@mt":"发现 {Count} 个模块程序集","Count":3,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:04.9725628Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:04.9744708Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:04.9758378Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:04.9769724Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":12,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:04.9779546Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":3,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:04.9792644Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:05.0702666Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:05.0730574Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:05.0739386Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:05.0755980Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:05.9440319Z","@mt":"发现 {Count} 个模块程序集","Count":3,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:05.9453478Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:05.9460396Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:05.9466529Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:05.9476647Z","@mt":"发现 {Count} 个模块程序集","Count":3,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:05.9484972Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:05.9494243Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:05.9502157Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:05.9511253Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":12,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:06.4603875Z","@mt":"开始刷新OpenAPI文档...","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:06.4619220Z","@mt":"发现 {Count} 个模块程序集","Count":3,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:06.4627051Z","@mt":"发现 {Count} 个模块程序集","Count":3,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:06.4635888Z","@mt":"过滤后发现 {Count} 个活跃模块程序集","Count":3,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:06.4648123Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:06.4655903Z","@mt":"文档更新通知: 当前加载了 {ModuleCount} 个模块","ModuleCount":3,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:06.4663178Z","@mt":"已刷新OpenAPI文档","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:06.4667661Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:06.9776442Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:06.9795883Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:06.9807661Z","@mt":"开始监控模块目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:06.9812329Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:30.1936572Z","@mt":"初始化模块热加载监控...","@tr":"cccb5ee41dd338af7b9e5a621983f8c3","@sp":"e4d69ad903a8cd81","SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNF139V2LTFS:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:30.1963646Z","@mt":"为目录 {Directory} 设置文件监控器","@tr":"cccb5ee41dd338af7b9e5a621983f8c3","@sp":"e4d69ad903a8cd81","Directory":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules","SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNF139V2LTFS:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:30.1970306Z","@mt":"模块热加载监控初始化完成，监控 {DirectoryCount} 个目录","@tr":"cccb5ee41dd338af7b9e5a621983f8c3","@sp":"e4d69ad903a8cd81","DirectoryCount":1,"SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNF139V2LTFS:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:30.1985927Z","@mt":"初始化动态路由系统...","@tr":"cccb5ee41dd338af7b9e5a621983f8c3","@sp":"e4d69ad903a8cd81","SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNF139V2LTFS:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:30.1995974Z","@mt":"发现 {Count} 个模块程序集","@tr":"cccb5ee41dd338af7b9e5a621983f8c3","@sp":"e4d69ad903a8cd81","Count":3,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNF139V2LTFS:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:30.2011851Z","@mt":"动态路由系统初始化完成，发现 {ModuleCount} 个模块","@tr":"cccb5ee41dd338af7b9e5a621983f8c3","@sp":"e4d69ad903a8cd81","ModuleCount":3,"SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNF139V2LTFS:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:30.2027584Z","@mt":"发现 {Count} 个模块程序集","@tr":"cccb5ee41dd338af7b9e5a621983f8c3","@sp":"e4d69ad903a8cd81","Count":3,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNF139V2LTFS:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:30.2037434Z","@mt":"检测到模块变化: {ModuleName}","@tr":"cccb5ee41dd338af7b9e5a621983f8c3","@sp":"e4d69ad903a8cd81","ModuleName":"Auth","SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNF139V2LTFS:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:30.2047018Z","@mt":"开始刷新动态路由...","@tr":"cccb5ee41dd338af7b9e5a621983f8c3","@sp":"e4d69ad903a8cd81","SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNF139V2LTFS:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:30.2055092Z","@mt":"发现 {Count} 个模块程序集","@tr":"cccb5ee41dd338af7b9e5a621983f8c3","@sp":"e4d69ad903a8cd81","Count":3,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNF139V2LTFS:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:30.2065716Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"cccb5ee41dd338af7b9e5a621983f8c3","@sp":"e4d69ad903a8cd81","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFS:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:30.2072118Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"cccb5ee41dd338af7b9e5a621983f8c3","@sp":"e4d69ad903a8cd81","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFS:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:30.2078283Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"cccb5ee41dd338af7b9e5a621983f8c3","@sp":"e4d69ad903a8cd81","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFS:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:30.2087962Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"cccb5ee41dd338af7b9e5a621983f8c3","@sp":"e4d69ad903a8cd81","EndpointCount":12,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFS:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:30.2097763Z","@mt":"动态路由刷新完成，共 {EndpointCount} 个端点","@tr":"cccb5ee41dd338af7b9e5a621983f8c3","@sp":"e4d69ad903a8cd81","EndpointCount":12,"SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNF139V2LTFS:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:30.2119276Z","@mt":"首次请求触发端点刷新","@tr":"cccb5ee41dd338af7b9e5a621983f8c3","@sp":"e4d69ad903a8cd81","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNF139V2LTFS:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:30.2159194Z","@mt":"发现 {Count} 个模块程序集","@tr":"cccb5ee41dd338af7b9e5a621983f8c3","@sp":"e4d69ad903a8cd81","Count":3,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNF139V2LTFS:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:30.2184270Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"cccb5ee41dd338af7b9e5a621983f8c3","@sp":"e4d69ad903a8cd81","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFS:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:30.2195412Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"cccb5ee41dd338af7b9e5a621983f8c3","@sp":"e4d69ad903a8cd81","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFS:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:30.2210303Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"cccb5ee41dd338af7b9e5a621983f8c3","@sp":"e4d69ad903a8cd81","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFS:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:30.2215694Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"cccb5ee41dd338af7b9e5a621983f8c3","@sp":"e4d69ad903a8cd81","EndpointCount":12,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFS:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:30.2237771Z","@mt":"发现 {Count} 个模块程序集","@tr":"cccb5ee41dd338af7b9e5a621983f8c3","@sp":"e4d69ad903a8cd81","Count":3,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNF139V2LTFS:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:30.2246600Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"cccb5ee41dd338af7b9e5a621983f8c3","@sp":"e4d69ad903a8cd81","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFS:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:30.2282628Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"cccb5ee41dd338af7b9e5a621983f8c3","@sp":"e4d69ad903a8cd81","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFS:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:30.2300241Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"cccb5ee41dd338af7b9e5a621983f8c3","@sp":"e4d69ad903a8cd81","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFS:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:30.2314778Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"cccb5ee41dd338af7b9e5a621983f8c3","@sp":"e4d69ad903a8cd81","EndpointCount":12,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFS:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:30.2321849Z","@mt":"请求触发的端点刷新完成，发现 {ModuleCount} 个模块，{EndpointCount} 个端点","@tr":"cccb5ee41dd338af7b9e5a621983f8c3","@sp":"e4d69ad903a8cd81","ModuleCount":3,"EndpointCount":12,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNF139V2LTFS:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:30.2349787Z","@mt":"Failed to determine the https port for redirect.","@l":"Warning","@tr":"cccb5ee41dd338af7b9e5a621983f8c3","@sp":"e4d69ad903a8cd81","EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNF139V2LTFS:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:30.2687047Z","@mt":"开始统一OpenAPI文档转换...","@tr":"cccb5ee41dd338af7b9e5a621983f8c3","@sp":"e4d69ad903a8cd81","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedDocumentTransformer","RequestId":"0HNF139V2LTFS:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:30.2726371Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"cccb5ee41dd338af7b9e5a621983f8c3","@sp":"e4d69ad903a8cd81","Count":3,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedDocumentTransformer","RequestId":"0HNF139V2LTFS:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:30.2801619Z","@mt":"统一OpenAPI文档转换完成，包含 {ModuleCount} 个模块","@tr":"cccb5ee41dd338af7b9e5a621983f8c3","@sp":"e4d69ad903a8cd81","ModuleCount":1,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedDocumentTransformer","RequestId":"0HNF139V2LTFS:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFS","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:39.9860651Z","@mt":"发现 {Count} 个模块程序集","@tr":"0c9c240d181df0306ec55bab161aeecf","@sp":"a5c1457269c591bf","Count":3,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNF139V2LTFT:********","RequestPath":"/scalar/v1","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:39.9872942Z","@mt":"检测到模块变化: {ModuleName}","@tr":"0c9c240d181df0306ec55bab161aeecf","@sp":"a5c1457269c591bf","ModuleName":"Auth","SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNF139V2LTFT:********","RequestPath":"/scalar/v1","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:39.9881228Z","@mt":"开始刷新动态路由...","@tr":"0c9c240d181df0306ec55bab161aeecf","@sp":"a5c1457269c591bf","SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNF139V2LTFT:********","RequestPath":"/scalar/v1","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:39.9893213Z","@mt":"发现 {Count} 个模块程序集","@tr":"0c9c240d181df0306ec55bab161aeecf","@sp":"a5c1457269c591bf","Count":3,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNF139V2LTFT:********","RequestPath":"/scalar/v1","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:39.9904375Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"0c9c240d181df0306ec55bab161aeecf","@sp":"a5c1457269c591bf","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFT:********","RequestPath":"/scalar/v1","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:39.9920538Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"0c9c240d181df0306ec55bab161aeecf","@sp":"a5c1457269c591bf","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFT:********","RequestPath":"/scalar/v1","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:39.9934261Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"0c9c240d181df0306ec55bab161aeecf","@sp":"a5c1457269c591bf","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFT:********","RequestPath":"/scalar/v1","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:39.9941479Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"0c9c240d181df0306ec55bab161aeecf","@sp":"a5c1457269c591bf","EndpointCount":12,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFT:********","RequestPath":"/scalar/v1","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:39.9946894Z","@mt":"动态路由刷新完成，共 {EndpointCount} 个端点","@tr":"0c9c240d181df0306ec55bab161aeecf","@sp":"a5c1457269c591bf","EndpointCount":12,"SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNF139V2LTFT:********","RequestPath":"/scalar/v1","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.0591223Z","@mt":"发现 {Count} 个模块程序集","@tr":"1fd21f0da3cbb04cb4d50ab40eb09435","@sp":"abfee2533a39d918","Count":3,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNF139V2LTFU:********","RequestPath":"/scalar/scalar.aspnetcore.js","ConnectionId":"0HNF139V2LTFU","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.0592109Z","@mt":"发现 {Count} 个模块程序集","@tr":"eaf66f55c6ddf550940c9382b767da5b","@sp":"2ff0cfe5301458ed","Count":3,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNF139V2LTFT:********","RequestPath":"/scalar/scalar.js","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.0604097Z","@mt":"检测到模块变化: {ModuleName}","@tr":"1fd21f0da3cbb04cb4d50ab40eb09435","@sp":"abfee2533a39d918","ModuleName":"Auth","SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNF139V2LTFU:********","RequestPath":"/scalar/scalar.aspnetcore.js","ConnectionId":"0HNF139V2LTFU","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.0614447Z","@mt":"开始刷新动态路由...","@tr":"1fd21f0da3cbb04cb4d50ab40eb09435","@sp":"abfee2533a39d918","SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNF139V2LTFU:********","RequestPath":"/scalar/scalar.aspnetcore.js","ConnectionId":"0HNF139V2LTFU","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.0609725Z","@mt":"检测到模块变化: {ModuleName}","@tr":"eaf66f55c6ddf550940c9382b767da5b","@sp":"2ff0cfe5301458ed","ModuleName":"Auth","SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNF139V2LTFT:********","RequestPath":"/scalar/scalar.js","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.0636433Z","@mt":"开始刷新动态路由...","@tr":"eaf66f55c6ddf550940c9382b767da5b","@sp":"2ff0cfe5301458ed","SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNF139V2LTFT:********","RequestPath":"/scalar/scalar.js","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.0637041Z","@mt":"发现 {Count} 个模块程序集","@tr":"1fd21f0da3cbb04cb4d50ab40eb09435","@sp":"abfee2533a39d918","Count":3,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNF139V2LTFU:********","RequestPath":"/scalar/scalar.aspnetcore.js","ConnectionId":"0HNF139V2LTFU","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.0647274Z","@mt":"发现 {Count} 个模块程序集","@tr":"eaf66f55c6ddf550940c9382b767da5b","@sp":"2ff0cfe5301458ed","Count":3,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNF139V2LTFT:********","RequestPath":"/scalar/scalar.js","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.0652616Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"1fd21f0da3cbb04cb4d50ab40eb09435","@sp":"abfee2533a39d918","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFU:********","RequestPath":"/scalar/scalar.aspnetcore.js","ConnectionId":"0HNF139V2LTFU","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.0661561Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"eaf66f55c6ddf550940c9382b767da5b","@sp":"2ff0cfe5301458ed","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFT:********","RequestPath":"/scalar/scalar.js","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.0667894Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"1fd21f0da3cbb04cb4d50ab40eb09435","@sp":"abfee2533a39d918","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFU:********","RequestPath":"/scalar/scalar.aspnetcore.js","ConnectionId":"0HNF139V2LTFU","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.0675850Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"eaf66f55c6ddf550940c9382b767da5b","@sp":"2ff0cfe5301458ed","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFT:********","RequestPath":"/scalar/scalar.js","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.0682996Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"1fd21f0da3cbb04cb4d50ab40eb09435","@sp":"abfee2533a39d918","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFU:********","RequestPath":"/scalar/scalar.aspnetcore.js","ConnectionId":"0HNF139V2LTFU","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.0690408Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"eaf66f55c6ddf550940c9382b767da5b","@sp":"2ff0cfe5301458ed","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFT:********","RequestPath":"/scalar/scalar.js","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.0700960Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"1fd21f0da3cbb04cb4d50ab40eb09435","@sp":"abfee2533a39d918","EndpointCount":12,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFU:********","RequestPath":"/scalar/scalar.aspnetcore.js","ConnectionId":"0HNF139V2LTFU","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.0707416Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"eaf66f55c6ddf550940c9382b767da5b","@sp":"2ff0cfe5301458ed","EndpointCount":12,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFT:********","RequestPath":"/scalar/scalar.js","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.0713500Z","@mt":"动态路由刷新完成，共 {EndpointCount} 个端点","@tr":"1fd21f0da3cbb04cb4d50ab40eb09435","@sp":"abfee2533a39d918","EndpointCount":12,"SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNF139V2LTFU:********","RequestPath":"/scalar/scalar.aspnetcore.js","ConnectionId":"0HNF139V2LTFU","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.0718678Z","@mt":"动态路由刷新完成，共 {EndpointCount} 个端点","@tr":"eaf66f55c6ddf550940c9382b767da5b","@sp":"2ff0cfe5301458ed","EndpointCount":12,"SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNF139V2LTFT:********","RequestPath":"/scalar/scalar.js","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.2603209Z","@mt":"发现 {Count} 个模块程序集","@tr":"6951eecef02532a6e3c8b398a1d6acc9","@sp":"b0568d855648c48e","Count":3,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNF139V2LTFU:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFU","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.2613352Z","@mt":"检测到模块变化: {ModuleName}","@tr":"6951eecef02532a6e3c8b398a1d6acc9","@sp":"b0568d855648c48e","ModuleName":"Auth","SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNF139V2LTFU:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFU","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.2619039Z","@mt":"开始刷新动态路由...","@tr":"6951eecef02532a6e3c8b398a1d6acc9","@sp":"b0568d855648c48e","SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNF139V2LTFU:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFU","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.2630100Z","@mt":"发现 {Count} 个模块程序集","@tr":"6951eecef02532a6e3c8b398a1d6acc9","@sp":"b0568d855648c48e","Count":3,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNF139V2LTFU:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFU","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.2643146Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"6951eecef02532a6e3c8b398a1d6acc9","@sp":"b0568d855648c48e","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFU:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFU","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.2650966Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"6951eecef02532a6e3c8b398a1d6acc9","@sp":"b0568d855648c48e","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFU:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFU","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.2659030Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"6951eecef02532a6e3c8b398a1d6acc9","@sp":"b0568d855648c48e","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFU:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFU","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.2666191Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"6951eecef02532a6e3c8b398a1d6acc9","@sp":"b0568d855648c48e","EndpointCount":12,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFU:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFU","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.2672822Z","@mt":"动态路由刷新完成，共 {EndpointCount} 个端点","@tr":"6951eecef02532a6e3c8b398a1d6acc9","@sp":"b0568d855648c48e","EndpointCount":12,"SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNF139V2LTFU:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFU","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.2684596Z","@mt":"开始统一OpenAPI文档转换...","@tr":"6951eecef02532a6e3c8b398a1d6acc9","@sp":"b0568d855648c48e","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedDocumentTransformer","RequestId":"0HNF139V2LTFU:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFU","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.2695418Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"6951eecef02532a6e3c8b398a1d6acc9","@sp":"b0568d855648c48e","Count":3,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedDocumentTransformer","RequestId":"0HNF139V2LTFU:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFU","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.2704334Z","@mt":"统一OpenAPI文档转换完成，包含 {ModuleCount} 个模块","@tr":"6951eecef02532a6e3c8b398a1d6acc9","@sp":"b0568d855648c48e","ModuleCount":1,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedDocumentTransformer","RequestId":"0HNF139V2LTFU:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFU","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.2735810Z","@mt":"发现 {Count} 个模块程序集","@tr":"eaa7b5e58e0ebe8b1d7e2347a21178e1","@sp":"4b7b3d9cb4082e00","Count":3,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNF139V2LTFT:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.2744910Z","@mt":"检测到模块变化: {ModuleName}","@tr":"eaa7b5e58e0ebe8b1d7e2347a21178e1","@sp":"4b7b3d9cb4082e00","ModuleName":"Auth","SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNF139V2LTFT:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.2752299Z","@mt":"开始刷新动态路由...","@tr":"eaa7b5e58e0ebe8b1d7e2347a21178e1","@sp":"4b7b3d9cb4082e00","SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNF139V2LTFT:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.2764916Z","@mt":"发现 {Count} 个模块程序集","@tr":"eaa7b5e58e0ebe8b1d7e2347a21178e1","@sp":"4b7b3d9cb4082e00","Count":3,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNF139V2LTFT:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.2773967Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"eaa7b5e58e0ebe8b1d7e2347a21178e1","@sp":"4b7b3d9cb4082e00","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFT:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.2781640Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"eaa7b5e58e0ebe8b1d7e2347a21178e1","@sp":"4b7b3d9cb4082e00","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFT:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.2790001Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"eaa7b5e58e0ebe8b1d7e2347a21178e1","@sp":"4b7b3d9cb4082e00","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFT:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.2798598Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"eaa7b5e58e0ebe8b1d7e2347a21178e1","@sp":"4b7b3d9cb4082e00","EndpointCount":12,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFT:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.2805738Z","@mt":"动态路由刷新完成，共 {EndpointCount} 个端点","@tr":"eaa7b5e58e0ebe8b1d7e2347a21178e1","@sp":"4b7b3d9cb4082e00","EndpointCount":12,"SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNF139V2LTFT:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.2815798Z","@mt":"开始统一OpenAPI文档转换...","@tr":"eaa7b5e58e0ebe8b1d7e2347a21178e1","@sp":"4b7b3d9cb4082e00","SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedDocumentTransformer","RequestId":"0HNF139V2LTFT:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.2826000Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"eaa7b5e58e0ebe8b1d7e2347a21178e1","@sp":"4b7b3d9cb4082e00","Count":3,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedDocumentTransformer","RequestId":"0HNF139V2LTFT:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.2835041Z","@mt":"统一OpenAPI文档转换完成，包含 {ModuleCount} 个模块","@tr":"eaa7b5e58e0ebe8b1d7e2347a21178e1","@sp":"4b7b3d9cb4082e00","ModuleCount":1,"SourceContext":"SSIC.Infrastructure.Startups.OpenAPI.UnifiedDocumentTransformer","RequestId":"0HNF139V2LTFT:********","RequestPath":"/openapi/v1.json","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.8131695Z","@mt":"发现 {Count} 个模块程序集","@tr":"63ea5e02d80ca3805ec2d4e8293be3a7","@sp":"b289ffcc504cf5e9","Count":3,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNF139V2LTFT:********","RequestPath":"/favicon.ico","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.8143506Z","@mt":"检测到模块变化: {ModuleName}","@tr":"63ea5e02d80ca3805ec2d4e8293be3a7","@sp":"b289ffcc504cf5e9","ModuleName":"Auth","SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNF139V2LTFT:********","RequestPath":"/favicon.ico","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.8150496Z","@mt":"开始刷新动态路由...","@tr":"63ea5e02d80ca3805ec2d4e8293be3a7","@sp":"b289ffcc504cf5e9","SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNF139V2LTFT:********","RequestPath":"/favicon.ico","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.8162386Z","@mt":"发现 {Count} 个模块程序集","@tr":"63ea5e02d80ca3805ec2d4e8293be3a7","@sp":"b289ffcc504cf5e9","Count":3,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNF139V2LTFT:********","RequestPath":"/favicon.ico","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.8171614Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"63ea5e02d80ca3805ec2d4e8293be3a7","@sp":"b289ffcc504cf5e9","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFT:********","RequestPath":"/favicon.ico","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.8202472Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"63ea5e02d80ca3805ec2d4e8293be3a7","@sp":"b289ffcc504cf5e9","ModuleName":"Auth","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFT:********","RequestPath":"/favicon.ico","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.8218086Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"63ea5e02d80ca3805ec2d4e8293be3a7","@sp":"b289ffcc504cf5e9","ModuleName":"Auth","EndpointCount":6,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFT:********","RequestPath":"/favicon.ico","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.8226145Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"63ea5e02d80ca3805ec2d4e8293be3a7","@sp":"b289ffcc504cf5e9","EndpointCount":12,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNF139V2LTFT:********","RequestPath":"/favicon.ico","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-22T01:23:40.8232608Z","@mt":"动态路由刷新完成，共 {EndpointCount} 个端点","@tr":"63ea5e02d80ca3805ec2d4e8293be3a7","@sp":"b289ffcc504cf5e9","EndpointCount":12,"SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNF139V2LTFT:********","RequestPath":"/favicon.ico","ConnectionId":"0HNF139V2LTFT","MachineName":"LAPTOP-F6SGUTN5","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
