{"profiles": {"http": {"commandName": "Project", "launchBrowser": true, "launchUrl": "scalar/v1", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "http://localhost:5000"}, "https": {"commandName": "Project", "launchBrowser": true, "launchUrl": "scalar/v1", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "https://localhost:7000;http://localhost:5000"}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "scalar/v1", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}, "$schema": "http://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:44000", "sslPort": 44300}}}