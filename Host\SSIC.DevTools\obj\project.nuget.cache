{"version": 2, "dgSpecHash": "e8imkaNskg4=", "success": true, "projectFilePath": "F:\\SSIC\\Host\\SSIC.DevTools\\SSIC.DevTools.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\anglesharp\\1.3.0\\anglesharp.1.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\anglesharp.css\\1.0.0-beta.151\\anglesharp.css.1.0.0-beta.151.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.core\\1.47.1\\azure.core.1.47.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.identity\\1.14.2\\azure.identity.1.14.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bouncycastle.cryptography\\2.3.1\\bouncycastle.cryptography.2.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\castle.core\\5.2.1\\castle.core.5.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\dapr.aspnetcore\\1.16.0-rc13\\dapr.aspnetcore.1.16.0-rc13.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\dapr.client\\1.16.0-rc13\\dapr.client.1.16.0-rc13.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\dapr.common\\1.16.0-rc13\\dapr.common.1.16.0-rc13.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\dapr.protos\\1.16.0-rc13\\dapr.protos.1.16.0-rc13.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\dm.dmprovider\\8.3.1.28188\\dm.dmprovider.8.3.1.28188.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\dnsclient\\1.8.0\\dnsclient.1.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\freeredis\\1.4.0\\freeredis.1.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\freescheduler\\2.0.36\\freescheduler.2.0.36.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\freesql\\3.5.213-preview20250815\\freesql.3.5.213-preview20250815.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\freesql.all\\3.5.213-preview20250815\\freesql.all.3.5.213-preview20250815.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\freesql.cloud\\2.0.1\\freesql.cloud.2.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\freesql.dbcontext\\3.5.213-preview20250815\\freesql.dbcontext.3.5.213-preview20250815.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\freesql.provider.dameng\\3.5.213-preview20250815\\freesql.provider.dameng.3.5.213-preview20250815.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\freesql.provider.msaccess\\3.5.213-preview20250815\\freesql.provider.msaccess.3.5.213-preview20250815.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\freesql.provider.mysql\\3.5.213-preview20250815\\freesql.provider.mysql.3.5.213-preview20250815.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\freesql.provider.odbc\\3.5.213-preview20250815\\freesql.provider.odbc.3.5.213-preview20250815.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\freesql.provider.oracle\\3.5.213-preview20250815\\freesql.provider.oracle.3.5.213-preview20250815.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\freesql.provider.postgresql\\3.5.213-preview20250815\\freesql.provider.postgresql.3.5.213-preview20250815.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\freesql.provider.sqlite\\3.5.213-preview20250815\\freesql.provider.sqlite.3.5.213-preview20250815.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\freesql.provider.sqlserver\\3.5.213-preview20250815\\freesql.provider.sqlserver.3.5.213-preview20250815.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\freesql.repository\\3.5.213-preview20250815\\freesql.repository.3.5.213-preview20250815.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\google.api.commonprotos\\2.17.0\\google.api.commonprotos.2.17.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\google.protobuf\\3.31.1\\google.protobuf.3.31.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\grpc.core.api\\2.71.0\\grpc.core.api.2.71.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\grpc.net.client\\2.71.0\\grpc.net.client.2.71.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\grpc.net.common\\2.71.0\\grpc.net.common.2.71.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\idlebus\\1.5.3\\idlebus.1.5.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\k4os.compression.lz4\\1.3.8\\k4os.compression.lz4.1.3.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\k4os.compression.lz4.streams\\1.3.8\\k4os.compression.lz4.streams.1.3.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\k4os.hash.xxhash\\1.0.8\\k4os.hash.xxhash.1.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mapster\\7.4.2-pre02\\mapster.7.4.2-pre02.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mapster.core\\1.2.3-pre02\\mapster.core.1.2.3-pre02.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\masa.buildingblocks.configuration\\1.2.0-preview.10\\masa.buildingblocks.configuration.1.2.0-preview.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\masa.buildingblocks.data\\1.2.0-preview.10\\masa.buildingblocks.data.1.2.0-preview.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\masa.buildingblocks.data.contracts\\1.2.0-preview.10\\masa.buildingblocks.data.contracts.1.2.0-preview.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\masa.buildingblocks.development.daprstarter\\1.2.0-preview.10\\masa.buildingblocks.development.daprstarter.1.2.0-preview.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\masa.buildingblocks.exceptions\\1.2.0-preview.10\\masa.buildingblocks.exceptions.1.2.0-preview.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\masa.buildingblocks.globalization.i18n\\1.2.0-preview.10\\masa.buildingblocks.globalization.i18n.1.2.0-preview.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\masa.contrib.development.daprstarter\\1.2.0-preview.10\\masa.contrib.development.daprstarter.1.2.0-preview.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\masa.contrib.development.daprstarter.aspnetcore\\1.2.0-preview.10\\masa.contrib.development.daprstarter.aspnetcore.1.2.0-preview.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\masa.utils.caching.memory\\1.2.0-preview.10\\masa.utils.caching.memory.1.2.0-preview.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\masa.utils.extensions.dotnet\\1.2.0-preview.10\\masa.utils.extensions.dotnet.1.2.0-preview.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\masa.utils.models.config\\1.2.0-preview.10\\masa.utils.models.config.1.2.0-preview.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\masuit.tools.abstractions\\2025.5.0\\masuit.tools.abstractions.2025.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\masuit.tools.core\\2025.5.0\\masuit.tools.core.2025.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication.jwtbearer\\10.0.0-preview.7.25380.108\\microsoft.aspnetcore.authentication.jwtbearer.10.0.0-preview.7.25380.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.openapi\\9.0.1\\microsoft.aspnetcore.openapi.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\8.0.0\\microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.cryptography\\9.0.4\\microsoft.bcl.cryptography.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlclient\\6.1.1\\microsoft.data.sqlclient.6.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlclient.sni.runtime\\6.0.2\\microsoft.data.sqlclient.sni.runtime.6.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.dependencyvalidation.analyzers\\0.11.0\\microsoft.dependencyvalidation.analyzers.0.11.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore\\9.0.8\\microsoft.entityframeworkcore.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.abstractions\\9.0.8\\microsoft.entityframeworkcore.abstractions.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.analyzers\\9.0.8\\microsoft.entityframeworkcore.analyzers.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.ambientmetadata.application\\9.8.0\\microsoft.extensions.ambientmetadata.application.9.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.apidescription.server\\9.0.0\\microsoft.extensions.apidescription.server.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.compliance.abstractions\\9.8.0\\microsoft.extensions.compliance.abstractions.9.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration\\10.0.0-preview.7.25380.108\\microsoft.extensions.configuration.10.0.0-preview.7.25380.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\10.0.0-preview.7.25380.108\\microsoft.extensions.configuration.abstractions.10.0.0-preview.7.25380.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.binder\\10.0.0-preview.7.25380.108\\microsoft.extensions.configuration.binder.10.0.0-preview.7.25380.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.commandline\\10.0.0-preview.7.25380.108\\microsoft.extensions.configuration.commandline.10.0.0-preview.7.25380.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.environmentvariables\\10.0.0-preview.7.25380.108\\microsoft.extensions.configuration.environmentvariables.10.0.0-preview.7.25380.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.fileextensions\\10.0.0-preview.7.25380.108\\microsoft.extensions.configuration.fileextensions.10.0.0-preview.7.25380.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.json\\10.0.0-preview.7.25380.108\\microsoft.extensions.configuration.json.10.0.0-preview.7.25380.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.usersecrets\\10.0.0-preview.7.25380.108\\microsoft.extensions.configuration.usersecrets.10.0.0-preview.7.25380.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\10.0.0-preview.7.25380.108\\microsoft.extensions.dependencyinjection.10.0.0-preview.7.25380.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\10.0.0-preview.7.25380.108\\microsoft.extensions.dependencyinjection.abstractions.10.0.0-preview.7.25380.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.autoactivation\\9.8.0\\microsoft.extensions.dependencyinjection.autoactivation.9.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencymodel\\9.0.0\\microsoft.extensions.dependencymodel.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics\\10.0.0-preview.7.25380.108\\microsoft.extensions.diagnostics.10.0.0-preview.7.25380.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics.abstractions\\10.0.0-preview.7.25380.108\\microsoft.extensions.diagnostics.abstractions.10.0.0-preview.7.25380.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics.exceptionsummarization\\9.8.0\\microsoft.extensions.diagnostics.exceptionsummarization.9.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.abstractions\\10.0.0-preview.7.25380.108\\microsoft.extensions.fileproviders.abstractions.10.0.0-preview.7.25380.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.physical\\10.0.0-preview.7.25380.108\\microsoft.extensions.fileproviders.physical.10.0.0-preview.7.25380.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.filesystemglobbing\\10.0.0-preview.7.25380.108\\microsoft.extensions.filesystemglobbing.10.0.0-preview.7.25380.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.hosting\\10.0.0-preview.7.25380.108\\microsoft.extensions.hosting.10.0.0-preview.7.25380.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.hosting.abstractions\\10.0.0-preview.7.25380.108\\microsoft.extensions.hosting.abstractions.10.0.0-preview.7.25380.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.http.diagnostics\\9.8.0\\microsoft.extensions.http.diagnostics.9.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.http.resilience\\9.8.0\\microsoft.extensions.http.resilience.9.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\10.0.0-preview.7.25380.108\\microsoft.extensions.logging.10.0.0-preview.7.25380.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\10.0.0-preview.7.25380.108\\microsoft.extensions.logging.abstractions.10.0.0-preview.7.25380.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.configuration\\10.0.0-preview.7.25380.108\\microsoft.extensions.logging.configuration.10.0.0-preview.7.25380.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.console\\10.0.0-preview.7.25380.108\\microsoft.extensions.logging.console.10.0.0-preview.7.25380.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.debug\\10.0.0-preview.7.25380.108\\microsoft.extensions.logging.debug.10.0.0-preview.7.25380.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.eventlog\\10.0.0-preview.7.25380.108\\microsoft.extensions.logging.eventlog.10.0.0-preview.7.25380.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.eventsource\\10.0.0-preview.7.25380.108\\microsoft.extensions.logging.eventsource.10.0.0-preview.7.25380.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\10.0.0-preview.7.25380.108\\microsoft.extensions.options.10.0.0-preview.7.25380.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options.configurationextensions\\10.0.0-preview.7.25380.108\\microsoft.extensions.options.configurationextensions.10.0.0-preview.7.25380.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\10.0.0-preview.7.25380.108\\microsoft.extensions.primitives.10.0.0-preview.7.25380.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.resilience\\9.8.0\\microsoft.extensions.resilience.9.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.servicediscovery\\9.4.1\\microsoft.extensions.servicediscovery.9.4.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.servicediscovery.abstractions\\9.4.1\\microsoft.extensions.servicediscovery.abstractions.9.4.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.telemetry\\9.8.0\\microsoft.extensions.telemetry.9.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.telemetry.abstractions\\9.8.0\\microsoft.extensions.telemetry.abstractions.9.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.client\\4.73.1\\microsoft.identity.client.4.73.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.client.extensions.msal\\4.73.1\\microsoft.identity.client.extensions.msal.4.73.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.abstractions\\8.0.1\\microsoft.identitymodel.abstractions.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.jsonwebtokens\\8.0.1\\microsoft.identitymodel.jsonwebtokens.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.logging\\8.0.1\\microsoft.identitymodel.logging.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols\\8.0.1\\microsoft.identitymodel.protocols.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols.openidconnect\\8.0.1\\microsoft.identitymodel.protocols.openidconnect.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.tokens\\8.0.1\\microsoft.identitymodel.tokens.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.openapi\\1.6.24\\microsoft.openapi.1.6.24.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.sqlserver.server\\1.0.0\\microsoft.sqlserver.server.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.azure.containers.tools.targets\\1.22.1\\microsoft.visualstudio.azure.containers.tools.targets.1.22.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\miniprofiler.aspnetcore\\4.5.4\\miniprofiler.aspnetcore.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\miniprofiler.aspnetcore.mvc\\4.5.4\\miniprofiler.aspnetcore.mvc.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\miniprofiler.shared\\4.5.4\\miniprofiler.shared.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mysql.data\\9.1.0\\mysql.data.9.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nettopologysuite\\2.0.0\\nettopologysuite.2.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nettopologysuite.io.postgis\\2.1.0\\nettopologysuite.io.postgis.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\npgsql\\9.0.3\\npgsql.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\npgsql.legacypostgis\\5.0.18\\npgsql.legacypostgis.5.0.18.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\npgsql.nettopologysuite\\5.0.18\\npgsql.nettopologysuite.5.0.18.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opentelemetry\\1.12.0\\opentelemetry.1.12.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opentelemetry.api\\1.12.0\\opentelemetry.api.1.12.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opentelemetry.api.providerbuilderextensions\\1.12.0\\opentelemetry.api.providerbuilderextensions.1.12.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opentelemetry.exporter.opentelemetryprotocol\\1.12.0\\opentelemetry.exporter.opentelemetryprotocol.1.12.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opentelemetry.exporter.zipkin\\1.12.0\\opentelemetry.exporter.zipkin.1.12.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opentelemetry.extensions.hosting\\1.12.0\\opentelemetry.extensions.hosting.1.12.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opentelemetry.instrumentation.aspnetcore\\1.12.0\\opentelemetry.instrumentation.aspnetcore.1.12.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opentelemetry.instrumentation.grpccore\\1.0.0-beta.6\\opentelemetry.instrumentation.grpccore.1.0.0-beta.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opentelemetry.instrumentation.grpcnetclient\\1.12.0-beta.1\\opentelemetry.instrumentation.grpcnetclient.1.12.0-beta.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opentelemetry.instrumentation.http\\1.12.0\\opentelemetry.instrumentation.http.1.12.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opentelemetry.instrumentation.process\\1.12.0-beta.1\\opentelemetry.instrumentation.process.1.12.0-beta.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opentelemetry.instrumentation.runtime\\1.12.0\\opentelemetry.instrumentation.runtime.1.12.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\oracle.manageddataaccess.core\\23.6.1\\oracle.manageddataaccess.core.23.6.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\polly.core\\8.4.2\\polly.core.8.4.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\polly.extensions\\8.4.2\\polly.extensions.8.4.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\polly.ratelimiting\\8.4.2\\polly.ratelimiting.8.4.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\qrcoder\\1.6.0\\qrcoder.1.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\scalar.aspnetcore\\2.6.9\\scalar.aspnetcore.2.6.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog\\4.3.1-dev-02373\\serilog.4.3.1-dev-02373.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.aspnetcore\\9.0.0\\serilog.aspnetcore.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.enrichers.environment\\3.0.1\\serilog.enrichers.environment.3.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.enrichers.thread\\4.0.0\\serilog.enrichers.thread.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.expressions\\5.1.0-dev-02301\\serilog.expressions.5.1.0-dev-02301.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.extensions.hosting\\9.0.1-dev-02307\\serilog.extensions.hosting.9.0.1-dev-02307.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.extensions.logging\\9.0.3-dev-02320\\serilog.extensions.logging.9.0.3-dev-02320.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.formatting.compact\\3.0.0\\serilog.formatting.compact.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.settings.configuration\\9.0.1-dev-02317\\serilog.settings.configuration.9.0.1-dev-02317.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.console\\6.0.1-dev-00953\\serilog.sinks.console.6.0.1-dev-00953.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.debug\\3.0.0\\serilog.sinks.debug.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.file\\6.0.0\\serilog.sinks.file.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.opentelemetry\\4.2.1-dev-02306\\serilog.sinks.opentelemetry.4.2.1-dev-02306.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.seq\\9.0.0\\serilog.sinks.seq.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sharpcompress\\0.40.0\\sharpcompress.0.40.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sixlabors.fonts\\2.1.3\\sixlabors.fonts.2.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sixlabors.imagesharp\\3.1.11\\sixlabors.imagesharp.3.1.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sixlabors.imagesharp.drawing\\2.1.7\\sixlabors.imagesharp.drawing.2.1.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\stub.system.data.sqlite.core.netstandard\\1.0.119\\stub.system.data.sqlite.core.netstandard.1.0.119.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore\\9.0.3\\swashbuckle.aspnetcore.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swagger\\9.0.3\\swashbuckle.aspnetcore.swagger.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swaggergen\\9.0.3\\swashbuckle.aspnetcore.swaggergen.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swaggerui\\9.0.3\\swashbuckle.aspnetcore.swaggerui.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.clientmodel\\1.5.1\\system.clientmodel.1.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.codedom\\9.0.8\\system.codedom.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.configuration.configurationmanager\\9.0.8\\system.configuration.configurationmanager.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.odbc\\8.0.0\\system.data.odbc.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.oledb\\6.0.0\\system.data.oledb.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.sqlite.core\\1.0.119\\system.data.sqlite.core.1.0.119.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.eventlog\\10.0.0-preview.7.25380.108\\system.diagnostics.eventlog.10.0.0-preview.7.25380.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.performancecounter\\9.0.8\\system.diagnostics.performancecounter.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.directoryservices.protocols\\8.0.0\\system.directoryservices.protocols.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.identitymodel.tokens.jwt\\8.0.1\\system.identitymodel.tokens.jwt.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.management\\9.0.8\\system.management.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory.data\\8.0.1\\system.memory.data.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.pkcs\\9.0.4\\system.security.cryptography.pkcs.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.protecteddata\\9.0.8\\system.security.cryptography.protecteddata.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.permissions\\8.0.0\\system.security.permissions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.windows.extensions\\8.0.0\\system.windows.extensions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\workqueue\\1.3.0\\workqueue.1.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\zstdsharp.port\\0.8.5\\zstdsharp.port.0.8.5.nupkg.sha512"], "logs": []}