using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SSIC.Infrastructure.Startups.OpenAPI;
using SSIC.Infrastructure.Startups.Endpoints;
using SSIC.Infrastructure.Startups.ModuleManager;
using System;
using System.Collections.Concurrent;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

namespace SSIC.Infrastructure.Middleware
{
    /// <summary>
    /// 模块热加载监控中间件
    /// 监控模块文件变化并自动重新加载
    /// </summary>
    public class ModuleHotReloadMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<ModuleHotReloadMiddleware> _logger;
        private static readonly ConcurrentDictionary<string, FileSystemWatcher> _watchers = new();
        private static readonly ConcurrentDictionary<string, DateTime> _lastReloadTimes = new();
        private static bool _initialized = false;
        private static readonly object _initLock = new();

        public ModuleHotReloadMiddleware(RequestDelegate next, ILogger<ModuleHotReloadMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                // 确保只初始化一次
                if (!_initialized)
                {
                    await InitializeHotReloadAsync(context);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "模块热加载中间件初始化时发生错误");
            }

            await _next(context);
        }

        /// <summary>
        /// 初始化热加载监控
        /// </summary>
        private async Task InitializeHotReloadAsync(HttpContext context)
        {
            if (_initialized)
                return;

            lock (_initLock)
            {
                if (_initialized)
                    return;

                try
                {
                    _logger.LogInformation("初始化模块热加载监控...");

                    // 获取模块目录
                    var moduleDirectories = GetModuleDirectories();
                    
                    foreach (var directory in moduleDirectories)
                    {
                        SetupFileWatcher(directory, context.RequestServices);
                    }

                    _logger.LogInformation("模块热加载监控初始化完成，监控 {DirectoryCount} 个目录", moduleDirectories.Count);
                    _initialized = true;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "初始化模块热加载监控时发生错误");
                }
            }
        }

        /// <summary>
        /// 获取模块目录列表
        /// </summary>
        private List<string> GetModuleDirectories()
        {
            var directories = new List<string>();

            try
            {
                // 获取当前应用程序目录
                var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
                
                // 查找Modules目录
                var modulesDirectory = Path.Combine(baseDirectory, "Modules");
                if (Directory.Exists(modulesDirectory))
                {
                    directories.Add(modulesDirectory);
                    _logger.LogDebug("添加模块监控目录: {Directory}", modulesDirectory);
                }

                // 查找其他可能的模块目录
                var possibleDirectories = new[]
                {
                    Path.Combine(baseDirectory, "bin", "Modules"),
                    Path.Combine(baseDirectory, "bin", "Debug", "net10.0", "Modules"),
                    Path.Combine(baseDirectory, "bin", "Release", "net10.0", "Modules")
                };

                foreach (var dir in possibleDirectories)
                {
                    if (Directory.Exists(dir) && !directories.Contains(dir))
                    {
                        directories.Add(dir);
                        _logger.LogDebug("添加模块监控目录: {Directory}", dir);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取模块目录时发生错误");
            }

            return directories;
        }

        /// <summary>
        /// 设置文件监控器
        /// </summary>
        private void SetupFileWatcher(string directory, IServiceProvider serviceProvider)
        {
            try
            {
                if (_watchers.ContainsKey(directory))
                {
                    return; // 已经设置过监控器
                }

                var watcher = new FileSystemWatcher(directory)
                {
                    Filter = "*.dll",
                    IncludeSubdirectories = true,
                    NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.CreationTime | NotifyFilters.FileName
                };

                watcher.Changed += (sender, e) => OnModuleFileChanged(e, serviceProvider);
                watcher.Created += (sender, e) => OnModuleFileChanged(e, serviceProvider);
                watcher.Renamed += (sender, e) => OnModuleFileChanged(e, serviceProvider);

                watcher.EnableRaisingEvents = true;
                _watchers[directory] = watcher;

                _logger.LogInformation("为目录 {Directory} 设置文件监控器", directory);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置文件监控器时发生错误: {Directory}", directory);
            }
        }

        /// <summary>
        /// 处理模块文件变化事件
        /// </summary>
        private async void OnModuleFileChanged(FileSystemEventArgs e, IServiceProvider serviceProvider)
        {
            try
            {
                // 检查是否是模块文件
                if (!IsModuleFile(e.FullPath))
                {
                    return;
                }

                // 防止重复处理同一文件的变化
                var lastReloadKey = e.FullPath;
                var now = DateTime.Now;
                
                if (_lastReloadTimes.TryGetValue(lastReloadKey, out var lastReload) && 
                    (now - lastReload).TotalSeconds < 2)
                {
                    return; // 2秒内不重复处理
                }

                _lastReloadTimes[lastReloadKey] = now;

                _logger.LogInformation("检测到模块文件变化: {FilePath}, 事件类型: {ChangeType}", 
                    e.FullPath, e.ChangeType);

                // 延迟一点时间，确保文件写入完成
                await Task.Delay(1000);

                // 触发模块重新加载
                await ReloadModuleAsync(e.FullPath, serviceProvider);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理模块文件变化时发生错误: {FilePath}", e.FullPath);
            }
        }

        /// <summary>
        /// 检查是否是模块文件
        /// </summary>
        private bool IsModuleFile(string filePath)
        {
            try
            {
                var fileName = Path.GetFileNameWithoutExtension(filePath);
                return fileName.StartsWith("SSIC.Modules.", StringComparison.OrdinalIgnoreCase);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 重新加载模块
        /// </summary>
        private async Task ReloadModuleAsync(string filePath, IServiceProvider serviceProvider)
        {
            try
            {
                _logger.LogInformation("开始重新加载模块: {FilePath}", filePath);

                // 获取服务
                var moduleDiscoveryService = serviceProvider.GetService<IModuleDiscoveryService>();
                var endpointManagementService = serviceProvider.GetService<IEndpointManagementService>();
                var hotReloadService = serviceProvider.GetService<IUnifiedOpenApiRefreshService>();
                var dynamicEndpointManager = DynamicEndpointManager.Instance;

                if (moduleDiscoveryService == null || endpointManagementService == null)
                {
                    _logger.LogWarning("无法获取必要的服务，跳过模块重新加载");
                    return;
                }

                // 尝试加载程序集
                Assembly newAssembly = null;
                try
                {
                    // 注意：这里需要小心处理程序集加载，避免文件锁定
                    var assemblyBytes = await File.ReadAllBytesAsync(filePath);
                    newAssembly = Assembly.Load(assemblyBytes);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "加载程序集失败: {FilePath}", filePath);
                    return;
                }

                // 重新发现模块
                var modules = moduleDiscoveryService.DiscoverLoadedModules().ToList();
                
                // 刷新端点
                var endpoints = await endpointManagementService.RefreshEndpointsAsync(modules);
                
                // 刷新动态端点管理器
                if (dynamicEndpointManager != null)
                {
                    dynamicEndpointManager.RefreshEndpoints(serviceProvider, modules.Select(m => m.Assembly));
                }

                // 刷新OpenAPI文档
                if (hotReloadService != null)
                {
                    await hotReloadService.RefreshAsync();
                }

                _logger.LogInformation("模块重新加载完成: {FilePath}, 共 {EndpointCount} 个端点", 
                    filePath, endpoints.Count());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重新加载模块时发生错误: {FilePath}", filePath);
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public static void Dispose()
        {
            foreach (var watcher in _watchers.Values)
            {
                try
                {
                    watcher.EnableRaisingEvents = false;
                    watcher.Dispose();
                }
                catch
                {
                    // 忽略释放错误
                }
            }
            _watchers.Clear();
        }
    }

    /// <summary>
    /// 模块热加载中间件扩展方法
    /// </summary>
    public static class ModuleHotReloadMiddlewareExtensions
    {
        /// <summary>
        /// 添加模块热加载中间件
        /// </summary>
        public static IApplicationBuilder UseModuleHotReload(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<ModuleHotReloadMiddleware>();
        }
    }
}
