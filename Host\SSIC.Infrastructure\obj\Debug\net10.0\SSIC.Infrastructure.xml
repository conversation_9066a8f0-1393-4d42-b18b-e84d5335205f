<?xml version="1.0"?>
<doc>
    <assembly>
        <name>SSIC.Infrastructure</name>
    </assembly>
    <members>
        <member name="T:SSIC.Infrastructure.Authentication.BearerSecuritySchemeTransformer">
            <summary>
            Bearer安全方案转换器
            为OpenAPI文档添加JWT Bearer认证方案
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Authentication.BearerSecuritySchemeTransformer.TransformAsync(Microsoft.OpenApi.Models.OpenApiDocument,Microsoft.AspNetCore.OpenApi.OpenApiDocumentTransformerContext,System.Threading.CancellationToken)">
            <summary>
            转换OpenAPI文档，添加Bearer安全方案
            </summary>
            <param name="document">OpenAPI文档</param>
            <param name="context">转换上下文</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Authentication.JwtGenerator.GenerateSecurityToken(System.Security.Claims.ClaimsIdentity)">
            <summary>
            生成Token
            </summary>
            <param name="claimsIdentity"></param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.Authentication.JwtGenerator.GetPrincipal(System.String)">
            <summary>
            根据token获取对应的数据
            </summary>
            <param name="token"></param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.Authentication.PermissionHandler.HandleRequirementAsync(Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext,SSIC.Infrastructure.Authentication.PermissionRequirement)">
            <summary>
            授权策略
            </summary>
            <param name="context"></param>
            <param name="requirement"></param>
            <returns></returns>
        </member>
        <member name="T:SSIC.Infrastructure.BaseController.BaseController">
            <summary>
            基础控制器
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.BaseController.BaseController.GetService``1">
            <summary>
            获取指定类型的服务
            </summary>
            <typeparam name="TService">服务类型</typeparam>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseController.BaseController.GetOptionalService``1">
            <summary>
            获取指定类型的服务（可选）
            </summary>
            <typeparam name="TService">服务类型</typeparam>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseController.BaseController.Success(System.Object,System.String)">
            <summary>
            返回成功结果
            </summary>
            <param name="data">数据</param>
            <param name="message">消息</param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseController.BaseController.Error(System.String,SSIC.Infrastructure.Enums.OutCode)">
            <summary>
            返回失败结果
            </summary>
            <param name="message">错误消息</param>
            <param name="code">错误代码</param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseController.BaseController.PageResult(System.Object,System.Int64,System.String)">
            <summary>
            返回分页结果
            </summary>
            <param name="data">数据列表</param>
            <param name="total">总数</param>
            <param name="message">消息</param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseController.BaseController.ValidationError(System.String)">
            <summary>
            返回验证失败结果
            </summary>
            <param name="message">验证失败消息</param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseController.BaseController.Unauthorized(System.String)">
            <summary>
            返回未授权结果
            </summary>
            <param name="message">未授权消息</param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseController.BaseController.Forbidden(System.String)">
            <summary>
            返回禁止访问结果
            </summary>
            <param name="message">禁止访问消息</param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseController.BaseController.NotFound(System.String)">
            <summary>
            返回未找到结果
            </summary>
            <param name="message">未找到消息</param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseController.BaseController.LogInformation(System.String,System.Object[])">
            <summary>
            记录信息日志
            </summary>
            <param name="message">日志消息</param>
            <param name="args">参数</param>
        </member>
        <member name="M:SSIC.Infrastructure.BaseController.BaseController.LogWarning(System.String,System.Object[])">
            <summary>
            记录警告日志
            </summary>
            <param name="message">日志消息</param>
            <param name="args">参数</param>
        </member>
        <member name="M:SSIC.Infrastructure.BaseController.BaseController.LogError(System.Exception,System.String,System.Object[])">
            <summary>
            记录错误日志
            </summary>
            <param name="exception">异常</param>
            <param name="message">日志消息</param>
            <param name="args">参数</param>
        </member>
        <member name="M:SSIC.Infrastructure.BaseController.BaseController.Health">
            <summary>
            基础控制器健康检查接口
            </summary>
            <returns>健康状态</returns>
        </member>
        <member name="T:SSIC.Infrastructure.BaseProvider.IServiceBase">
            <summary>
            服务基础接口
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.BaseProvider.IServiceBase.ServiceName">
            <summary>
            获取服务名称
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.BaseProvider.ServiceBase">
            <summary>
            服务基础类
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.BaseProvider.ServiceBase.ServiceName">
            <summary>
            服务名称
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.BaseProvider.ServiceBase.LogInformation(System.String,System.Object[])">
            <summary>
            记录信息日志
            </summary>
            <param name="message">日志消息</param>
            <param name="args">参数</param>
        </member>
        <member name="M:SSIC.Infrastructure.BaseProvider.ServiceBase.LogWarning(System.String,System.Object[])">
            <summary>
            记录警告日志
            </summary>
            <param name="message">日志消息</param>
            <param name="args">参数</param>
        </member>
        <member name="M:SSIC.Infrastructure.BaseProvider.ServiceBase.LogError(System.Exception,System.String,System.Object[])">
            <summary>
            记录错误日志
            </summary>
            <param name="exception">异常</param>
            <param name="message">日志消息</param>
            <param name="args">参数</param>
        </member>
        <member name="T:SSIC.Infrastructure.ConfigurableOptions.IConfigurableOptions">
            <summary>
            配置属性接口（通过该接口进行适配）
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.ConfigurableOptions.Realization.ConfigurableOptions.AddAllConfigurableOptions(Microsoft.Extensions.DependencyInjection.IServiceCollection,Microsoft.Extensions.Configuration.IConfiguration@)">
            <summary>
            自动检索并注册所有实现 IConfigurableOptions 接口的配置选项，并将配置对象添加到服务集合中
            </summary>
            <param name="services">服务集合</param>
        </member>
        <member name="M:SSIC.Infrastructure.ConfigurableOptions.Realization.ConfigurableOptions.MergeJsonObjects(Newtonsoft.Json.Linq.JObject,Newtonsoft.Json.Linq.JObject)">
            <summary>
            合并两个 JSON 对象，同一属性进行覆盖，不同属性进行合并
            </summary>
            <param name="target">目标 JSON 对象</param>
            <param name="source">源 JSON 对象</param>
        </member>
        <member name="M:SSIC.Infrastructure.ConfigurableOptions.Realization.ConfigurableOptions.GetAllOptionsTypes">
            <summary>
            获取所有实现 IConfigurableOptions 接口的类型
            </summary>
            <returns>类型集合</returns>
        </member>
        <member name="M:SSIC.Infrastructure.ConfigurableOptions.Realization.ConfigurableOptions.GetEntityName``1">
            <summary>
            获取实体名称
            </summary>
            <typeparam name="T">类型参数</typeparam>
            <returns>实体名称</returns>
        </member>
        <member name="M:SSIC.Infrastructure.ConfigurableOptions.Realization.ConfigurableOptions.GetEntityName(System.Type)">
            <summary>
            获取实体名称
            </summary>
            <param name="type">类型</param>
            <returns>实体名称</returns>
        </member>
        <member name="M:SSIC.Infrastructure.ConfigurableOptions.Realization.ConfigurableOptions.GetConfigFilePath(System.String)">
            <summary>
            检索目录下的配置文件路径
            </summary>
            <param name="entityName">实体名称</param>
            <returns>配置文件路径</returns>
        </member>
        <member name="M:SSIC.Infrastructure.ConfigurableOptions.Realization.ConfigurableOptions.GetOptions``1(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            从服务集合中获取配置选项的实例
            </summary>
            <typeparam name="T">配置选项的类型</typeparam>
            <param name="services">服务集合</param>
            <returns>配置选项实例</returns>
        </member>
        <member name="M:SSIC.Infrastructure.ConfigurableOptions.Realization.ConfigurableOptions.GetOptions``1(System.IServiceProvider)">
            <summary>
            从服务提供者中获取配置选项的实例
            </summary>
            <typeparam name="T">配置选项的类型</typeparam>
            <param name="provider">服务提供者</param>
            <returns>配置选项实例</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Dapr.DaprClientProvider.InvokeServiceAsync``2(System.String,System.String,``0)">
            <summary>
            调用指定服务的指定方法
            </summary>
            <typeparam name="TRequest">请求数据的类型</typeparam>
            <typeparam name="TResponse">响应数据的类型</typeparam>
            <param name="appId">目标服务的 App ID</param>
            <param name="methodName">要调用的方法名称</param>
            <param name="data">请求数据</param>
            <returns>目标服务返回的数据</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Dapr.DaprClientProvider.PublishEventAsync``1(System.String,System.String,``0)">
            <summary>
            发布事件(向指定的 pub/sub 组件发布事件)
            </summary>
            <typeparam name="T"></typeparam>
            <param name="pubsubName">Pub/Sub 组件的名称</param>
            <param name="topicName">要发布的主题名称</param>
            <param name="data">要发布的事件数据</param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.Dapr.DaprClientProvider.GetStateAsync``1(System.String,System.String)">
            <summary>
            获取状态(从指定的状态存储中获取状态)
            </summary>
            <typeparam name="T"></typeparam>
            <param name="storeName">状态存储的名称</param>
            <param name="key">要获取的状态的键</param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.Dapr.DaprClientProvider.SaveStateAsync``1(System.String,System.String,``0)">
            <summary>
            保存状态(将状态保存到指定的状态存储中)
            </summary>
            <typeparam name="T"></typeparam>
            <param name="storeName">状态存储的名称</param>
            <param name="key">要保存的状态的键</param>
            <param name="value">要保存的状态数据</param>
            <returns></returns>
        </member>
        <member name="T:SSIC.Infrastructure.DependencyInjection.Extensions.ServiceCollectionExtensions">
            <summary>
            作用域注册服务
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.DependencyInjection.Interface.IScoped">
            <summary>
            作用域接口标记
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.DependencyInjection.Interface.ISingleton">
            <summary>
            单例接口标记
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.DependencyInjection.Interface.ITransient">
            <summary>
            瞬时接口标记
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.ActionPermissionOption.None">
            <summary>
            没有任何权限
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.ActionPermissionOption.Add">
            <summary>
            新增
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.ActionPermissionOption.Delete">
            <summary>
            删除
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.ActionPermissionOption.Update">
            <summary>
            修改
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.ActionPermissionOption.View">
            <summary>
            查看
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.ActionPermissionOption.Search">
            <summary>
            查找
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.ActionPermissionOption.Export">
            <summary>
            导出
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.ActionPermissionOption.Copy">
            <summary>
            复制
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.ActionPermissionOption.Check">
            <summary>
            审核
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.ActionPermissionOption.Upload">
            <summary>
            上传文件
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.ActionPermissionOption.Import">
            <summary>
            导入表数据Excel
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Enums.CheckOption">
            <summary>
            审核配置
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.CheckOption.NotCheck">
            <summary>
            未审核
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.CheckOption.Check">
            <summary>
            已审核
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.CheckOption.ReturnCheck">
            <summary>
            反审核
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Enums.OutCode">
            <summary>
            状态码
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.OutCode.Success">
            <summary>
            返回成功
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.OutCode.Fail">
            <summary>
            返回失败
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.OutCode.NoAuthority">
            <summary>
            未授权
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.OutCode.Unauthorized">
            <summary>
            权限不足
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.OutCode.NoHttp">
            <summary>
            无效地址/资源未找到
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.OutCode.NotFound">
            <summary>
            资源未找到
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.OutCode.ValidationError">
            <summary>
            数据验证失败
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.OutCode.Forbidden">
            <summary>
            禁止访问
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.OutCode.ServerError">
            <summary>
            服务器错误
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Enums.TagType">
            <summary>
            菜单标签类型
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.TagType.primary">
            <summary>
            正常
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.TagType.success">
            <summary>
            成功
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.TagType.warn">
            <summary>
            等待
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.TagType.error">
            <summary>
            错误
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Enums.TenatType">
            <summary>
            租户数据库使用类型
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.TenatType.IdenticalSql">
            <summary>
            同库共用
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.TenatType.DifferentSql">
            <summary>
            不同库
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Extensions.ApplicationBuilderExtensions">
            <summary>
            应用程序构建器扩展方法
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Extensions.ApplicationBuilderExtensions.UseSSICInfrastructure(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            使用SSIC基础设施中间件
            </summary>
            <param name="app">应用程序构建器</param>
            <returns>应用程序构建器</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Extensions.ApplicationBuilderExtensions.UseDynamicRouting(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            使用动态路由
            </summary>
            <param name="app">应用程序构建器</param>
            <returns>应用程序构建器</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Extensions.ApplicationBuilderExtensions.UseModuleHotReload(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            使用模块热加载（仅在开发环境）
            </summary>
            <param name="app">应用程序构建器</param>
            <returns>应用程序构建器</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Extensions.ApplicationBuilderExtensions.UseEndpointRefresh(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            使用端点刷新中间件
            </summary>
            <param name="app">应用程序构建器</param>
            <returns>应用程序构建器</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Extensions.ApplicationBuilderExtensions.InitializeAndRefreshModules(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            初始化并刷新所有模块端点
            </summary>
            <param name="app">应用程序构建器</param>
            <returns>应用程序构建器</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Extensions.ApplicationBuilderExtensions.InitializeDynamicEndpointManager(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            初始化动态端点管理器
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Extensions.ServiceCollectionExtensions">
            <summary>
            服务集合扩展方法
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Extensions.ServiceCollectionExtensions.AddSSICInfrastructure(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            添加SSIC基础设施服务
            </summary>
            <param name="services">服务集合</param>
            <returns>服务集合</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Extensions.ServiceCollectionExtensions.AddModuleManagement(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            添加模块管理服务
            </summary>
            <param name="services">服务集合</param>
            <returns>服务集合</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Extensions.ServiceCollectionExtensions.AddHotReloadServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            添加热加载服务
            </summary>
            <param name="services">服务集合</param>
            <returns>服务集合</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Extensions.ServiceCollectionExtensions.AddDynamicRouting(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            添加动态路由服务
            </summary>
            <param name="services">服务集合</param>
            <returns>服务集合</returns>
        </member>
        <member name="T:SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware">
            <summary>
            动态路由中间件
            支持模块热加载和路由动态更新
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware.InitializeRoutingAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            初始化路由系统
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware.ShouldRefreshRoutingAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            检查是否需要刷新路由
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware.RefreshRoutingAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            刷新路由系统
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware.TryMatchDynamicRouteAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            尝试匹配动态路由
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Middleware.DynamicRoutingMiddlewareExtensions">
            <summary>
            动态路由中间件扩展方法
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Middleware.DynamicRoutingMiddlewareExtensions.UseDynamicRouting(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            添加动态路由中间件
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware">
            <summary>
            模块热加载监控中间件
            监控模块文件变化并自动重新加载
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware.InitializeHotReloadAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            初始化热加载监控
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware.GetModuleDirectories">
            <summary>
            获取模块目录列表
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware.SetupFileWatcher(System.String,System.IServiceProvider)">
            <summary>
            设置文件监控器
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware.OnModuleFileChanged(System.IO.FileSystemEventArgs,System.IServiceProvider)">
            <summary>
            处理模块文件变化事件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware.IsModuleFile(System.String)">
            <summary>
            检查是否是模块文件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware.ReloadModuleAsync(System.String,System.IServiceProvider)">
            <summary>
            重新加载模块
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware.Dispose">
            <summary>
            释放资源
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Middleware.ModuleHotReloadMiddlewareExtensions">
            <summary>
            模块热加载中间件扩展方法
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Middleware.ModuleHotReloadMiddlewareExtensions.UseModuleHotReload(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            添加模块热加载中间件
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.OptionsEntity.CorsOptions">
            <summary>
            跨域配置设置
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.CorsOptions.PolicyName">
            <summary>
            策略名称
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.CorsOptions.WithOrigins">
            <summary>
            允许来源域名，没有配置则允许所有来源
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.CorsOptions.IsUse">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.OptionsEntity.DbInfoOptions">
            <summary>
            数据库配置类
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.DbInfoOptions.dataservice">
            <summary>
            数据库地址
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.DbInfoOptions.dataport">
            <summary>
            数据库端口号
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.DbInfoOptions.datatype">
            <summary>
            数据库类型
            SqlServer = 1,
            PostgreSQL = 2,
            Oracle = 3,
            Sqlite = 4,
            OdbcOracle = 5,
            OdbcSqlServer = 6,
            OdbcMySql = 7,
            OdbcPostgreSQL = 8,
            Odbc = 9,
            OdbcDamning = 10,
            MsAccess = 11,
            Dameng = 12,
            OdbcKingbaseES = 13,
            ShenTong = 14,
            KingbaseES = 15,
            Firebird = 16
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.DbInfoOptions.dataname">
            <summary>
            数据库名称
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.DbInfoOptions.datauid">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.DbInfoOptions.datapwd">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.DbInfoOptions.tenantid">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.DbInfoOptions.ComposeUrl">
            <summary>
            数据库地址
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.DbInfoOptions.tenantLists">
             <summary>
            租户详细
             </summary>
        </member>
        <member name="T:SSIC.Infrastructure.OptionsEntity.ModulePathOptions">
            <summary>
            模块路径配置选项
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.ModulePathOptions.Enabled">
            <summary>
            是否启用自定义模块路径功能
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.ModulePathOptions.DevelopmentBasePath">
            <summary>
            开发环境基础路径
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.ModulePathOptions.ProductionBasePath">
            <summary>
            生产环境基础路径
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.ModulePathOptions.ModulePath">
            <summary>
            模块相对路径
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.ModulePathOptions.ScanSubFolders">
            <summary>
            是否扫描子文件夹
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.ModulePathOptions.ModuleDirectoryPrefix">
            <summary>
            模块目录前缀
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.ModulePathOptions.ScanMode">
            <summary>
            扫描模式（0=ByPrefix, 1=ByDirectory, 2=ByPattern, 3=ByWildcardPattern, 4=AllDlls）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.ModulePathOptions.WildcardPatterns">
            <summary>
            通配符模式列表
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.ModulePathOptions.SearchOption">
            <summary>
            搜索选项
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.OptionsEntity.ModulePathOptions.GetBasePath(System.Boolean)">
            <summary>
            获取当前环境的基础路径
            </summary>
            <param name="isDevelopment">是否为开发环境</param>
            <returns>基础路径</returns>
        </member>
        <member name="M:SSIC.Infrastructure.OptionsEntity.ModulePathOptions.GetModuleDirectory(System.Boolean)">
            <summary>
            获取完整的模块目录路径
            </summary>
            <param name="isDevelopment">是否为开发环境</param>
            <returns>模块目录路径</returns>
        </member>
        <member name="M:SSIC.Infrastructure.OptionsEntity.ModulePathOptions.GetSearchOption">
            <summary>
            获取搜索选项枚举值
            </summary>
            <returns>搜索选项枚举</returns>
        </member>
        <member name="M:SSIC.Infrastructure.OptionsEntity.ModulePathOptions.GetModuleSubFolders(System.Boolean)">
            <summary>
            获取模块扫描路径列表
            </summary>
            <param name="isDevelopment">是否为开发环境</param>
            <returns>模块扫描路径列表</returns>
        </member>
        <member name="T:SSIC.Infrastructure.OptionsEntity.PageData">
            <summary>
            分页数据
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.PageData.Page">
            <summary>
            页码
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.PageData.Rows">
            <summary>
            行数
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.PageData.Total">
            <summary>
            总条数
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.PageData.TableName">
            <summary>
            表格名称
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.PageData.Sort">
            <summary>
            排序字段
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.PageData.Order">
            <summary>
            排序方式
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.PageData.Wheres">
            <summary>
            条件
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.PageData.Export">
            <summary>
            是否导出excel
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.PageData.Value">
            <summary>
            自定义条件
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.OptionsEntity.SerilogOptions">
            <summary>
            Serilog 配置选项类，用于映射配置文件（如 appsettings.json）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.SerilogOptions.Using">
            <summary>
            Serilog 配置根对象
            </summary>
            <summary>
            使用的 Serilog 插件列表（如 Serilog.Sinks.Console 等）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.SerilogOptions.WriteTo">
            <summary>
            日志写入配置列表（支持写入多个目的地，如 Console、File、Seq 等）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.SerilogOptions.Enrich">
            <summary>
            日志增强器配置（如 FromLogContext，添加更多上下文信息）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.SerilogOptions.Properties">
            <summary>
            附加到日志中的通用属性（如应用名、环境）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.SerilogOptions.MinimumLevel">
            <summary>
            最小日志级别配置（如 Debug、Information、Warning 等）
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.OptionsEntity.Properties">
            <summary>
            日志附加属性，如应用名和环境
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Properties.Application">
            <summary>
            应用程序名称
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Properties.Environment">
            <summary>
            当前运行环境（如 Development、Production）
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.OptionsEntity.Minimumlevel">
            <summary>
            最小日志级别配置
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Minimumlevel.Default">
            <summary>
            默认的最小日志级别
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.OptionsEntity.Writeto">
            <summary>
            日志写入配置
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Writeto.Name">
            <summary>
            Sink 名称（例如 Console、File、Seq 等）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Writeto.Args">
            <summary>
            Sink 参数配置
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.OptionsEntity.Args">
            <summary>
            日志写入参数
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Args.serverUrl">
            <summary>
            日志服务器地址（用于 Seq）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Args.apiKey">
            <summary>
            访问日志服务器所需的 API 密钥（可选）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Args.restrictedToMinimumLevel">
            <summary>
            写入日志的最小级别
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Args.batchPostingLimit">
            <summary>
            批量发送日志的最大条数
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Args.period">
            <summary>
            批量发送的时间间隔（格式如 "00:00:02"）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Args.outputTemplate">
            <summary>
            日志输出模板（控制日志内容格式）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Args.theme">
            <summary>
            控制台日志主题（如 SystemConsoleTheme.Literate）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Args.path">
            <summary>
            文件日志保存路径
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Args.rollingInterval">
            <summary>
            文件滚动间隔（如 "Day"、"Hour"）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Args.formatter">
            <summary>
            自定义日志格式器（如 CompactJsonFormatter）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Args.retainedFileCountLimit">
            <summary>
            保留的最大日志文件数量
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Args.fileSizeLimitBytes">
            <summary>
            单个日志文件的最大大小（字节数）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Args.rollOnFileSizeLimit">
            <summary>
            超过最大文件大小时是否滚动生成新文件
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.OptionsEntity.ServerOptions">
            <summary>
            动态服务配置  
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.ServerOptions.name">
            <summary>
            Aspire资源名称
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.ServerOptions.slnpath">
            <summary>
            项目名称
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.ServerOptions.slnname">
            <summary>
            项目名称
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.ServerOptions.type">
            <summary>
            项目类型
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.ServerOptions.port">
            <summary>
            port端口
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.ServerOptions.grpcprot">
            <summary>
            gRPC端口
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.OptionsEntity.TenantChange">
            <summary>
            租户设置
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.OptionsEntity.TenantChange.AsyncLocalTenantId">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.OptionsEntity.TenantChange.AsyncLocalTenanttype">
            <summary>
            租户类型1.同库;2.分库
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.OptionsEntity.TenantList">
            <summary>
            租户设置
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.TenantList.Tenantid">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.TenantList.Tenattype">
            <summary>
            租户类型
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.TenantList.Host">
            <summary>
            域名地址
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.ZipkinOptions.Endpoint">
             <summary>
            端点
             </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Orm.FreeSqlContexts.SqlCommandLog(IFreeSql)">
            <summary>
            监视数据库语句
            </summary>
            <param name="freeSql"></param>
        </member>
        <member name="M:SSIC.Infrastructure.Orm.FreeSqlContexts.GetTypesByTableAttribute">
            <summary>
            扫描 IEntity类所在程序集，反射得到类上有特性标签为TableAttribute 的所有类，该方法需在实体类上指定了 [Table(Name = "xxx")]特性标签
            </summary>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.Orm.MultiFreeSqlExtensions.Change``1(IFreeSql,``0)">
            <summary>
            切换数据库，填入连接ID
            </summary>
            <typeparam name="TDBKey"></typeparam>
            <param name="fsql"></param>
            <param name="dbkey"></param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.Orm.MultiFreeSqlExtensions.Register``1(IFreeSql,``0,System.Func{IFreeSql})">
            <summary>
            进行连接注册
            </summary>
            <typeparam name="TDBKey"></typeparam>
            <param name="fsql"></param>
            <param name="dbkey"></param>
            <param name="create"></param>
            <returns></returns>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.StartupAttribute">
            <summary>
            项目启动项特性
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Startups.StartupAttribute.Sort">
            <summary>
            排序，执行顺序由小到大
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.Common.ModuleUtilities">
            <summary>
            模块相关的公共工具类
            统一处理模块名称提取、HTTP方法提取、控制器类型判断等公共逻辑
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Common.ModuleUtilities.ExtractModuleName(System.String)">
            <summary>
            从程序集名称中提取模块名称
            支持新的项目结构：SSIC.Modules.Auth.Controller -> Auth
            支持旧的项目结构：SSIC.Modules.Auth -> Auth
            </summary>
            <param name="assemblyName">程序集名称</param>
            <returns>模块名称，如果无法提取则返回null</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Common.ModuleUtilities.ExtractHttpMethod(System.String)">
            <summary>
            从特性名称提取HTTP方法
            </summary>
            <param name="attributeName">特性名称</param>
            <returns>HTTP方法名称</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Common.ModuleUtilities.IsControllerType(System.Type)">
            <summary>
            判断类型是否为控制器类型
            </summary>
            <param name="type">要检查的类型</param>
            <returns>如果是控制器类型返回true</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Common.ModuleUtilities.ExtractControllerName(System.String)">
            <summary>
            从控制器类型名称中提取控制器名称（去掉Controller后缀）
            </summary>
            <param name="controllerTypeName">控制器类型名称</param>
            <returns>控制器名称</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Common.ModuleUtilities.IsActionMethod(System.Reflection.MethodInfo)">
            <summary>
            判断方法是否为动作方法
            </summary>
            <param name="method">要检查的方法</param>
            <returns>如果是动作方法返回true</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Common.ModuleUtilities.IsBaseControllerMethod(System.Reflection.MethodInfo)">
            <summary>
            判断方法是否为基类控制器方法
            </summary>
            <param name="method">要检查的方法</param>
            <returns>如果是基类方法返回true</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Common.ModuleUtilities.HasHttpMethodAttribute(System.Reflection.MethodInfo)">
            <summary>
            判断方法是否有HTTP方法特性
            </summary>
            <param name="method">要检查的方法</param>
            <returns>如果有HTTP方法特性返回true</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Common.ModuleUtilities.GetHttpMethods(System.Reflection.MethodInfo)">
            <summary>
            获取方法的HTTP方法列表
            </summary>
            <param name="method">要检查的方法</param>
            <returns>HTTP方法列表</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Common.ModuleUtilities.GetModuleAssemblies">
            <summary>
            获取所有模块程序集（排除已卸载的模块）
            </summary>
            <returns>模块程序集列表</returns>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.Common.ModuleUtilities.ActionRouteInfo">
            <summary>
            路由信息类
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Common.ModuleUtilities.GetMethodRouteInfo(System.Reflection.MethodInfo,System.String)">
            <summary>
            获取方法的路由信息
            </summary>
            <param name="method">方法信息</param>
            <param name="actionName">动作名称</param>
            <returns>路由信息</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Common.ModuleUtilities.BuildRouteTemplate(System.Reflection.MethodInfo,System.String,System.String,System.String)">
            <summary>
            构建路由模板
            </summary>
            <param name="method">方法信息</param>
            <param name="controllerRoute">控制器路由</param>
            <param name="actionName">动作名称</param>
            <param name="moduleName">模块名称</param>
            <returns>完整的路由模板</returns>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.Common.OpenApiUtilities">
            <summary>
            OpenAPI工具类
            统一处理OpenAPI文档生成的公共逻辑
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Common.OpenApiUtilities.GetMethodSummary(System.Reflection.MethodInfo)">
            <summary>
            获取方法的摘要信息
            </summary>
            <param name="method">方法信息</param>
            <returns>摘要信息</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Common.OpenApiUtilities.GetMethodDescription(System.Reflection.MethodInfo)">
            <summary>
            获取方法的详细描述
            </summary>
            <param name="method">方法信息</param>
            <returns>详细描述</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Common.OpenApiUtilities.BuildRouteTemplate(System.Reflection.MethodInfo,System.String)">
            <summary>
            构建路由模板
            </summary>
            <param name="method">方法信息</param>
            <param name="controllerRoute">控制器路由</param>
            <returns>完整的路由模板</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Common.OpenApiUtilities.CreateOpenApiOperation(System.Reflection.MethodInfo,System.String,System.String)">
            <summary>
            创建OpenAPI操作对象
            </summary>
            <param name="method">方法信息</param>
            <param name="moduleName">模块名称</param>
            <param name="controllerName">控制器名称</param>
            <returns>OpenAPI操作对象</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Common.OpenApiUtilities.AddParameters(Microsoft.OpenApi.Models.OpenApiOperation,System.Reflection.MethodInfo)">
            <summary>
            添加参数到OpenAPI操作
            </summary>
            <param name="operation">OpenAPI操作</param>
            <param name="method">方法信息</param>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Common.OpenApiUtilities.AddResponses(Microsoft.OpenApi.Models.OpenApiOperation,System.Reflection.MethodInfo)">
            <summary>
            添加响应到OpenAPI操作
            </summary>
            <param name="operation">OpenAPI操作</param>
            <param name="method">方法信息</param>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Common.OpenApiUtilities.IsSimpleType(System.Type)">
            <summary>
            判断是否为简单类型
            </summary>
            <param name="type">类型</param>
            <returns>是否为简单类型</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Common.OpenApiUtilities.GetSchemaForType(System.Type)">
            <summary>
            获取类型的OpenAPI架构
            </summary>
            <param name="type">类型</param>
            <returns>OpenAPI架构</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Common.OpenApiUtilities.ClearDynamicModulePaths(Microsoft.OpenApi.Models.OpenApiDocument)">
            <summary>
            清理动态模块路径（保留系统路径）
            </summary>
            <param name="document">OpenAPI文档</param>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Common.OpenApiUtilities.AddModuleTag(Microsoft.OpenApi.Models.OpenApiDocument,System.String,System.Int32)">
            <summary>
            创建模块标签
            </summary>
            <param name="document">OpenAPI文档</param>
            <param name="moduleName">模块名称</param>
            <param name="controllerCount">控制器数量</param>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointDataSource">
            <summary>
            动态端点数据源，用于管理插件的路由端点
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointManager">
            <summary>
            动态端点管理器，用于管理ASP.NET Core的路由端点
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointManager.GetDynamicEndpoints">
            <summary>
            获取动态端点数据源
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointManager.RecordModuleLoaded(System.String)">
            <summary>
            记录已加载的模块名称，并从未加载模块列表中移除
            </summary>
            <param name="moduleName">模块名称</param>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointManager.RecordUnloadedModule(System.String)">
            <summary>
            记录已卸载的模块名称
            </summary>
            <param name="moduleName">模块名称</param>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointManager.ClearUnloadedModules">
            <summary>
            清除已卸载模块记录
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointManager.IsModuleUnloaded(System.String)">
            <summary>
            检查模块是否已被卸载
            </summary>
            <param name="moduleName">模块名称</param>
            <returns>如果模块已被卸载返回true，否则返回false</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointManager.RebuildEndpoints(System.IServiceProvider,System.Collections.Generic.IEnumerable{System.Reflection.Assembly})">
            <summary>
            重建所有路由端点（使用统一的模块管理服务）
            </summary>
            <param name="serviceProvider">服务提供程序</param>
            <param name="loadedPluginAssemblies">已加载的插件程序集</param>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointManager.RefreshEndpoints(System.IServiceProvider,System.Collections.Generic.IEnumerable{System.Reflection.Assembly})">
            <summary>
            刷新路由端点（使用统一的模块管理服务）
            </summary>
            <param name="services">服务提供程序</param>
            <param name="loadedAssemblies">已加载的模块程序集集合（可选）</param>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointSource">
            <summary>
            动态端点源实现，从JSON文件读取端点定义并监控文件变化
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointSource.#ctor(Microsoft.Extensions.FileProviders.IFileProvider,System.String)">
            <summary>
            构造函数
            </summary>
            <param name="fileProvider">文件提供程序</param>
            <param name="endpointsPath">端点定义JSON文件所在目录</param>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointSource.GetEndpointsAsync">
            <summary>
            异步获取所有端点
            </summary>
            <returns>端点集合</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointSource.Watch(System.Action{System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Http.Endpoint}})">
            <summary>
            监控端点定义变化
            </summary>
            <param name="callback">端点变化回调方法</param>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.Endpoints.EndpointDefinition">
            <summary>
            端点定义类
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Startups.Endpoints.EndpointDefinition.RoutePattern">
            <summary>
            路由模式
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Startups.Endpoints.EndpointDefinition.DisplayName">
            <summary>
            显示名称
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Startups.Endpoints.EndpointDefinition.Order">
            <summary>
            顺序（优先级）
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware">
            <summary>
            端点刷新中间件，在第一次请求时强制刷新路由系统
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddlewareExtensions">
            <summary>
            中间件扩展方法
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.Endpoints.FixedEndpointDataSource">
            <summary>
            固定端点数据源，解决热重载问题
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Endpoints.FixedEndpointDataSource.ForceRefresh">
            <summary>
            强制更新数据源并通知监听者
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.Endpoints.IDynamicEndpointSource">
            <summary>
            动态端点源接口，用于获取动态端点定义
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Endpoints.IDynamicEndpointSource.GetEndpointsAsync">
            <summary>
            异步获取所有端点
            </summary>
            <returns>端点集合</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Endpoints.IDynamicEndpointSource.Watch(System.Action{System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Http.Endpoint}})">
            <summary>
            监控端点定义变化
            </summary>
            <param name="callback">端点变化回调方法</param>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.Extensions.ServiceCollectionExtensions">
            <summary>
            注册模块服务
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Extensions.ServiceCollectionExtensions.AddModuleServices(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Reflection.Assembly)">
            <summary>
            添加模块服务
            </summary>
            <param name="services">服务集合</param>
            <param name="moduleAssembly">模块程序集</param>
            <returns>服务集合</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Extensions.ServiceCollectionExtensions.ConfigureModuleServices(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Reflection.Assembly)">
            <summary>
            配置模块服务
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Extensions.ServiceCollectionExtensions.RegisterApplicationPart(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Reflection.Assembly)">
            <summary>
            注册应用程序部件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Extensions.ServiceCollectionExtensions.NotifyDynamicEndpointManager(System.Reflection.Assembly)">
            <summary>
            通知动态端点管理器
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HostBuilder">
            <summary>
            配置项目启动项
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.RoutePrefixConvention">
            <summary>
            路由前缀约定类 - 支持模块化路由
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.RoutePrefixConvention.GetActionRouteInfo(Microsoft.AspNetCore.Mvc.ApplicationModels.ActionModel)">
            <summary>
            根据HTTP特性确定方法的路由信息
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.RoutePrefixConvention.ConfigureParameterBindingSources(Microsoft.AspNetCore.Mvc.ApplicationModels.ActionModel)">
            <summary>
            配置参数的模型绑定源
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.RoutePrefixConvention.IsComplexType(System.Type)">
            <summary>
            判断是否为复杂类型（需要从JSON绑定的类型）
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HotReload.FileChangedEventHandler">
            <summary>
            文件变化事件处理委托
            </summary>
            <param name="filePath">文件路径</param>
            <param name="changeType">变化类型</param>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HotReload.IFileWatcher">
            <summary>
            简化的文件监控器接口
            </summary>
        </member>
        <member name="E:SSIC.Infrastructure.Startups.HotReload.IFileWatcher.FileChanged">
            <summary>
            文件变化事件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.IFileWatcher.StartWatching">
            <summary>
            开始监控
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.IFileWatcher.StopWatching">
            <summary>
            停止监控
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HotReload.FileWatcher">
            <summary>
            简化的文件监控器实现 - 使用.NET自带的FileSystemWatcher
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.FileWatcher.StartWatching">
            <summary>
            开始监控文件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.FileWatcher.StopWatching">
            <summary>
            停止监控文件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.FileWatcher.OnFileEvent(System.Object,System.IO.FileSystemEventArgs)">
            <summary>
            处理文件事件（创建、修改、删除）
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.FileWatcher.OnFileRenamed(System.Object,System.IO.RenamedEventArgs)">
            <summary>
            处理文件重命名事件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.FileWatcher.ProcessPendingEvents(System.Object)">
            <summary>
            防抖定时器回调 - 处理待处理的事件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.FileWatcher.IsModuleFile(System.String)">
            <summary>
            检查是否为模块文件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.FileWatcher.IsWildcardMatch(System.String,System.String)">
            <summary>
            简单的通配符匹配
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.FileWatcher.Dispose">
            <summary>
            释放资源
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.FileWatcher.Dispose(System.Boolean)">
            <summary>
            释放资源
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions">
            <summary>
            热插拔扩展方法
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.GlobalServiceProvider">
            <summary>
            全局服务提供程序
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.AddHotReload(Microsoft.AspNetCore.Builder.WebApplicationBuilder,System.Action{SSIC.Infrastructure.Startups.HotReload.HotReloadOptions})">
            <summary>
            在WebApplicationBuilder上添加热插拔支持，支持链式调用
            </summary>
            <param name="builder">Web应用程序构建器</param>
            <param name="configureOptions">配置选项</param>
            <returns>Web应用程序构建器</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.AddHotReload(Microsoft.AspNetCore.Builder.WebApplicationBuilder,System.Boolean)">
            <summary>
            在WebApplicationBuilder上添加热插拔支持，使用模块路径配置
            </summary>
            <param name="builder">Web应用程序构建器</param>
            <param name="enabled">是否启用热插拔功能，默认为false</param>
            <returns>Web应用程序构建器</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.AddHotReload(Microsoft.AspNetCore.Builder.WebApplicationBuilder,SSIC.Infrastructure.Startups.HotReload.HotReloadOptions)">
            <summary>
            在WebApplicationBuilder上添加热插拔支持，支持链式调用（重载版本，直接接收选项对象）
            </summary>
            <param name="builder">Web应用程序构建器</param>
            <param name="options">热插拔选项</param>
            <returns>Web应用程序构建器</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.UseHotReload(Microsoft.AspNetCore.Builder.WebApplication)">
            <summary>
            使用热插拔功能，初始化并启动热插拔模块
            </summary>
            <param name="app">Web应用程序</param>
            <returns>Web应用程序</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.UpdateRoutes(Microsoft.AspNetCore.Builder.WebApplication,SSIC.Infrastructure.Startups.HotReload.IPluginManager)">
            <summary>
            更新路由
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.RefreshOpenApiDocumentation(Microsoft.AspNetCore.Builder.WebApplication)">
            <summary>
            刷新OpenAPI文档和Scalar
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HandleFileDeleted(System.String,SSIC.Infrastructure.Startups.HotReload.IPluginManager,Microsoft.AspNetCore.Builder.WebApplication,Microsoft.Extensions.Logging.ILogger)">
            <summary>
            处理文件删除事件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.ForceCleanupApplicationParts(Microsoft.AspNetCore.Builder.WebApplication,System.String,Microsoft.Extensions.Logging.ILogger)">
            <summary>
            强制清理ApplicationPartManager中的程序集部件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.ForceRefreshOpenApiAfterUnload(Microsoft.AspNetCore.Builder.WebApplication,Microsoft.Extensions.Logging.ILogger)">
            <summary>
            强制刷新OpenAPI文档（模块卸载后）
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HandleFileChanged(System.String,SSIC.Infrastructure.Startups.HotReload.IPluginManager,Microsoft.AspNetCore.Builder.WebApplication,Microsoft.Extensions.Logging.ILogger)">
            <summary>
            处理文件创建或修改事件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.ForceReleaseModuleResourcesAsync(System.String)">
            <summary>
            强制释放指定模块的所有资源，用于确保模块文件夹可以被删除
            </summary>
            <param name="moduleName">模块名称（不含扩展名）</param>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HotReload.HotReloadOptions">
            <summary>
            简化的热加载配置选项
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Startups.HotReload.HotReloadOptions.Enabled">
            <summary>
            是否启用热加载功能
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Startups.HotReload.HotReloadOptions.DebounceMs">
            <summary>
            防抖延迟（毫秒）- 设置为0禁用防抖，立即触发
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Startups.HotReload.HotReloadOptions.ForceGarbageCollection">
            <summary>
            是否在卸载后强制垃圾回收
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Startups.HotReload.HotReloadOptions.VerboseLogging">
            <summary>
            是否记录详细日志
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Startups.HotReload.HotReloadOptions.PreventDuplicateRegistration">
            <summary>
            是否启用模块重复注册检查，避免同一模块被多次加载
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Startups.HotReload.HotReloadOptions.ForceUnloadBeforeReload">
            <summary>
            重新加载模块时是否强制先卸载旧版本
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Startups.HotReload.HotReloadOptions.UnloadTimeoutMs">
            <summary>
            模块卸载超时时间（毫秒）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Startups.HotReload.HotReloadOptions.EnableVersionTracking">
            <summary>
            是否启用模块版本跟踪，防止加载相同版本
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Startups.HotReload.HotReloadOptions.MaxRetryCount">
            <summary>
            同一模块的最大重试加载次数
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Startups.HotReload.HotReloadOptions.RetryIntervalMs">
            <summary>
            模块加载失败后的重试间隔（毫秒）
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HotReload.IModuleScanner">
            <summary>
            模块扫描器
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.IModuleScanner.ScanModulesAsync">
            <summary>
            扫描模块
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.IModuleScanner.IsValidModuleFile(System.String)">
            <summary>
            检查文件是否为有效模块
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HotReload.ModuleScanner">
            <summary>
            模块扫描器实现
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.ModuleScanner.ScanModulesAsync">
            <summary>
            扫描所有监控路径下的模块
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.ModuleScanner.ScanDirectoryForModules(System.String)">
            <summary>
            扫描目录中的模块文件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.ModuleScanner.IsValidModuleFile(System.String)">
            <summary>
            检查文件是否为有效模块
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.ModuleScanner.IsWildcardMatch(System.String,System.String)">
            <summary>
            简单的通配符匹配
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HotReload.IPluginManager">
            <summary>
            插件管理器接口
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.IPluginManager.InitializeAsync">
            <summary>
            初始化插件管理器
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.IPluginManager.LoadPluginAsync(System.String)">
            <summary>
            加载插件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.IPluginManager.UnloadPluginAsync(System.String)">
            <summary>
            卸载插件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.IPluginManager.ReloadPluginAsync(System.String)">
            <summary>
            重新加载插件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.IPluginManager.GetLoadedPlugins">
            <summary>
            获取已加载的插件列表
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.IPluginManager.ForceReleaseModuleResourcesAsync(System.String)">
            <summary>
            强制释放模块相关的所有资源，用于确保模块文件夹可以被删除
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HotReload.PluginManager">
            <summary>
            插件管理器实现
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.PluginManager.InitializeAsync">
            <summary>
            初始化插件管理器
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.PluginManager.LoadPluginAsync(System.String)">
            <summary>
            加载插件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.PluginManager.LoadAssemblyFromMemoryMappedFileAsync(System.String)">
            <summary>
            使用内存映射文件加载程序集
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.PluginManager.UnloadPluginAsync(System.String)">
            <summary>
            卸载插件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.PluginManager.ReloadPluginAsync(System.String)">
            <summary>
            重新加载插件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.PluginManager.ForceReleaseModuleResourcesAsync(System.String)">
            <summary>
            强制释放模块相关的所有资源，用于确保模块文件夹可以被删除
            </summary>
            <param name="moduleName">模块名称</param>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.PluginManager.GetLoadedPlugins">
            <summary>
            获取已加载的插件列表
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.PluginManager.ConfigureModuleServicesAsync(System.Reflection.Assembly)">
            <summary>
            配置模块服务
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.PluginManager.CleanupTempFiles(System.Object)">
            <summary>
            定期清理临时文件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.PluginManager.Dispose">
            <summary>
            释放资源
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.PluginManager.Dispose(System.Boolean)">
            <summary>
            释放资源
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.PluginManager.LoadAssemblyFromFile(System.String)">
            <summary>
            简化的程序集加载方法
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HotReload.MemoryMappedAssemblyLoadContext">
            <summary>
            内存映射文件程序集加载上下文
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.MemoryMappedAssemblyLoadContext.LoadFromMemoryMappedFile">
            <summary>
            从内存映射文件加载程序集
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.IStartups">
            <summary>
            启动项接口
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService">
            <summary>
            端点管理服务实现
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService.CreateEndpointsFromModule(SSIC.Infrastructure.Startups.ModuleManager.ModuleInfo)">
            <summary>
            从模块信息创建端点
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService.CreateEndpoint(SSIC.Infrastructure.Startups.ModuleManager.EndpointCreationOptions)">
            <summary>
            创建单个端点
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService.CreateMvcEndpoint(SSIC.Infrastructure.Startups.ModuleManager.ModuleInfo,SSIC.Infrastructure.Startups.ModuleManager.ControllerInfo,SSIC.Infrastructure.Startups.ModuleManager.ActionInfo)">
            <summary>
            创建MVC控制器端点
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService.RefreshEndpointsAsync(System.Collections.Generic.IEnumerable{SSIC.Infrastructure.Startups.ModuleManager.ModuleInfo})">
            <summary>
            批量刷新端点
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService.ValidateEndpoint(Microsoft.AspNetCore.Http.Endpoint)">
            <summary>
            验证端点是否有效
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService.CreateDefaultHandler(System.String)">
            <summary>
            创建默认的请求处理器
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService.CreateMvcHandler(Microsoft.AspNetCore.Mvc.Controllers.ControllerActionDescriptor)">
            <summary>
            创建MVC请求处理器
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.ModuleManager.IEndpointManagementService">
            <summary>
            端点管理服务接口
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.IEndpointManagementService.CreateEndpointsFromModule(SSIC.Infrastructure.Startups.ModuleManager.ModuleInfo)">
            <summary>
            从模块信息创建端点
            </summary>
            <param name="moduleInfo">模块信息</param>
            <returns>端点列表</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.IEndpointManagementService.CreateEndpoint(SSIC.Infrastructure.Startups.ModuleManager.EndpointCreationOptions)">
            <summary>
            创建单个端点
            </summary>
            <param name="options">端点创建选项</param>
            <returns>端点</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.IEndpointManagementService.CreateMvcEndpoint(SSIC.Infrastructure.Startups.ModuleManager.ModuleInfo,SSIC.Infrastructure.Startups.ModuleManager.ControllerInfo,SSIC.Infrastructure.Startups.ModuleManager.ActionInfo)">
            <summary>
            创建MVC控制器端点
            </summary>
            <param name="moduleInfo">模块信息</param>
            <param name="controllerInfo">控制器信息</param>
            <param name="actionInfo">动作信息</param>
            <returns>端点</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.IEndpointManagementService.RefreshEndpointsAsync(System.Collections.Generic.IEnumerable{SSIC.Infrastructure.Startups.ModuleManager.ModuleInfo})">
            <summary>
            批量刷新端点
            </summary>
            <param name="modules">模块列表</param>
            <returns>所有端点</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.IEndpointManagementService.ValidateEndpoint(Microsoft.AspNetCore.Http.Endpoint)">
            <summary>
            验证端点是否有效
            </summary>
            <param name="endpoint">端点</param>
            <returns>是否有效</returns>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.ModuleManager.EndpointCreationOptions">
            <summary>
            端点创建选项
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.ModuleManager.IModuleDiscoveryService">
            <summary>
            模块发现服务接口
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.IModuleDiscoveryService.DiscoverLoadedModules">
            <summary>
            发现所有已加载的模块
            </summary>
            <returns>模块信息列表</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.IModuleDiscoveryService.DiscoverControllersInAssembly(System.Reflection.Assembly)">
            <summary>
            从程序集中发现控制器
            </summary>
            <param name="assembly">程序集</param>
            <returns>控制器信息列表</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.IModuleDiscoveryService.DiscoverActionsInController(System.Type)">
            <summary>
            从控制器类型中发现动作方法
            </summary>
            <param name="controllerType">控制器类型</param>
            <returns>动作方法信息列表</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.IModuleDiscoveryService.BuildRouteTemplate(SSIC.Infrastructure.Startups.ModuleManager.ModuleInfo,SSIC.Infrastructure.Startups.ModuleManager.ControllerInfo,SSIC.Infrastructure.Startups.ModuleManager.ActionInfo)">
            <summary>
            构建控制器的路由模板
            </summary>
            <param name="moduleInfo">模块信息</param>
            <param name="controllerInfo">控制器信息</param>
            <param name="actionInfo">动作信息（可选）</param>
            <returns>路由模板</returns>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.ModuleManager.ModuleInfo">
            <summary>
            模块信息
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.ModuleManager.ControllerInfo">
            <summary>
            控制器信息
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.ModuleManager.ActionInfo">
            <summary>
            动作方法信息
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService">
            <summary>
            模块发现服务实现
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService.DiscoverLoadedModules">
            <summary>
            发现所有已加载的模块
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService.DiscoverControllersInAssembly(System.Reflection.Assembly)">
            <summary>
            从程序集中发现控制器
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService.DiscoverActionsInController(System.Type)">
            <summary>
            从控制器类型中发现动作方法
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService.BuildRouteTemplate(SSIC.Infrastructure.Startups.ModuleManager.ModuleInfo,SSIC.Infrastructure.Startups.ModuleManager.ControllerInfo,SSIC.Infrastructure.Startups.ModuleManager.ActionInfo)">
            <summary>
            构建控制器的路由模板
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.OpenAPI.IUnifiedOpenApiRefreshService">
            <summary>
            统一的OpenAPI刷新服务接口
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.OpenAPI.IUnifiedOpenApiRefreshService.RefreshAsync">
            <summary>
            刷新OpenAPI文档
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.OpenAPI.IUnifiedOpenApiRefreshService.GetModuleInfoAsync">
            <summary>
            获取模块信息
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.OpenAPI.ISimpleOpenApiRefreshService">
            <summary>
            简单的OpenAPI刷新服务接口
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.OpenAPI.ISimpleOpenApiRefreshService.RefreshAsync">
            <summary>
            简单刷新
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.OpenAPI.ModuleInfo">
            <summary>
            模块信息
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.OpenAPI.OpenApiConfigurationExtensions">
            <summary>
            OpenApi 统一配置扩展
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.OpenAPI.OpenApiConfigurationExtensions.AddSSICOpenApi(Microsoft.Extensions.DependencyInjection.IServiceCollection,Microsoft.AspNetCore.Hosting.IWebHostEnvironment)">
            <summary>
            添加 SSIC OpenApi 配置
            </summary>
            <param name="services">服务集合</param>
            <param name="environment">环境信息</param>
            <returns>服务集合</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.OpenAPI.OpenApiConfigurationExtensions.GenerateCustomOperationId(Microsoft.AspNetCore.Mvc.Controllers.ControllerActionDescriptor)">
            <summary>
            生成自定义操作ID
            </summary>
            <param name="controllerAction">控制器动作描述符</param>
            <returns>操作ID</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.OpenAPI.OpenApiConfigurationExtensions.ConfigureSecurityScheme(Microsoft.OpenApi.Models.OpenApiDocument)">
            <summary>
            配置安全方案
            </summary>
            <param name="document">OpenApi 文档</param>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.OpenAPI.OpenApiConfigurationExtensions.ConfigureXmlComments(Microsoft.AspNetCore.OpenApi.OpenApiOptions,Microsoft.AspNetCore.Hosting.IWebHostEnvironment)">
            <summary>
            配置XML注释文档
            </summary>
            <param name="options">OpenAPI选项</param>
            <param name="environment">环境信息</param>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.OpenAPI.UnifiedDocumentTransformer">
            <summary>
            统一的OpenAPI文档转换器
            合并了DynamicModuleDocumentTransformer和HotReloadOpenApiDocumentTransformer的功能
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.OpenAPI.UnifiedDocumentTransformer.ProcessModuleAssembly(Microsoft.OpenApi.Models.OpenApiDocument,System.Reflection.Assembly,System.Collections.Generic.Dictionary{System.String,System.Collections.Generic.HashSet{System.String}},System.Threading.CancellationToken)">
            <summary>
            处理模块程序集
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.OpenAPI.UnifiedDocumentTransformer.ProcessController(Microsoft.OpenApi.Models.OpenApiDocument,System.Type,System.String,System.Collections.Generic.HashSet{System.String},System.Threading.CancellationToken)">
            <summary>
            处理控制器
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.OpenAPI.UnifiedDocumentTransformer.ProcessAction(Microsoft.OpenApi.Models.OpenApiDocument,System.Reflection.MethodInfo,System.String,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            处理动作方法
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.OpenAPI.UnifiedDocumentTransformer.AddOperationToDocument(Microsoft.OpenApi.Models.OpenApiDocument,System.String,System.String,System.Reflection.MethodInfo,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            添加操作到文档
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.OpenAPI.UnifiedDocumentTransformer.UpdateDocumentInfo(Microsoft.OpenApi.Models.OpenApiDocument,System.Collections.Generic.Dictionary{System.String,System.Collections.Generic.HashSet{System.String}})">
            <summary>
            更新文档信息
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.OpenAPI.UnifiedDocumentTransformer.AddModuleTags(Microsoft.OpenApi.Models.OpenApiDocument,System.Collections.Generic.Dictionary{System.String,System.Collections.Generic.HashSet{System.String}})">
            <summary>
            添加模块标签
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.OpenAPI.UnifiedDocumentTransformer.AddHotReloadExtension(Microsoft.OpenApi.Models.OpenApiDocument,System.Collections.Generic.Dictionary{System.String,System.Collections.Generic.HashSet{System.String}})">
            <summary>
            添加热重载扩展信息
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService">
            <summary>
            统一的OpenAPI刷新服务实现
            合并了HotReloadOpenApiRefreshService和OpenApiRefreshService的功能
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService.RefreshAsync">
            <summary>
            刷新OpenAPI文档
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService.FilterActiveModules(System.Collections.Generic.IEnumerable{SSIC.Infrastructure.Startups.ModuleManager.ModuleInfo})">
            <summary>
            过滤活跃的模块（排除已卸载的模块）
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService.RefreshOpenApiDocuments(Microsoft.Extensions.DependencyInjection.IServiceScope,System.Collections.Generic.IEnumerable{SSIC.Infrastructure.Startups.ModuleManager.ModuleInfo})">
            <summary>
            实际刷新 OpenAPI 文档生成器
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService.AddModuleXmlComments(SSIC.Infrastructure.Startups.ModuleManager.ModuleInfo)">
            <summary>
            为模块添加 XML 注释文档
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService.GetModuleInfoAsync">
            <summary>
            获取模块信息
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.OpenAPI.UnifiedOpenApiRefreshService.NotifyDocumentUpdate(System.Int32)">
            <summary>
            通知文档更新
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.OpenAPI.SimpleOpenApiRefreshService">
            <summary>
            简单的OpenAPI刷新服务实现
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.OpenAPI.SimpleOpenApiRefreshService.RefreshAsync">
            <summary>
            简单刷新
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ProgramStartup.AddStartup(Microsoft.AspNetCore.Builder.WebApplicationBuilder,System.Boolean)">
            <summary>
            添加应用程序启动配置
            </summary>
            <param name="builder">Web应用程序构建器</param>
            <param name="UseServiceDefault">是否使用服务默认配置</param>
            <returns>构建的Web应用程序</returns>
        </member>
        <member name="T:SSIC.Infrastructure.ViewModel.OutMenu">
            <summary>
            菜单实体
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutMenu.name">
            <summary>
            菜单名
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutMenu.icon">
            <summary>
            菜单图标,如果没有，则会尝试使用route.meta.icon
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutMenu.path">
            <summary>
            菜单路径
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutMenu.disabled">
            <summary>
            是否禁用
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutMenu.component">
             <summary>
            
             </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutMenu.redirect">
            <summary>
            重定向
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutMenu.meta">
            <summary>
            菜单属性
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.ViewModel.meta">
            <summary>
            菜单属性
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.meta.title">
            <summary>
            路由title  一般必填
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.meta.ignoreAuth">
            <summary>
            是否忽略权限，只在权限模式为Role的时候有效
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.meta.ignoreKeepAlive">
            roles?: RoleEnum[];
             <summary>
            是否忽略KeepAlive缓存
             </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.meta.affix">
             <summary>
            是否固定标签
             </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.meta.icon">
            <summary>
             图标，也是菜单图标
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.meta.frameSrc">
            <summary>
            内嵌iframe的地址
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.meta.transitionName">
            <summary>
            指定该路由切换的动画名
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.meta.hideBreadcrumb">
            <summary>
            隐藏该路由在面包屑上面的显示
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.meta.carryParam">
            <summary>
            如果该路由会携带参数，且需要在tab页上面显示。则需要设置为true
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.meta.currentActiveMenu">
            <summary>
            当前激活的菜单。用于配置详情页时左侧激活的菜单路径
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.meta.hideTab">
            <summary>
            当前路由不再标签页显示
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.meta.hideMenu">
            <summary>
            当前路由不再菜单显示
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.ViewModel.tag">
            <summary>
            菜单标签设置
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.tag.dot">
            <summary>
            为true则显示小圆点
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.tag.content">
            <summary>
            内容
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.tag.type">
            <summary>
            类型
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.ViewModel.OutPermission">
            <summary>
            权限输出表
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutPermission.name">
            <summary>
            权限名称
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutPermission.code">
            <summary>
            权限代号
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutPermission.icon">
            <summary>
            按钮图标
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutPermission.color">
            <summary>
            按钮颜色
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutPermission.titlekey">
            <summary>
            多语言标签t
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutPermission.type">
            <summary>
            按钮类型0.弹窗,1.接口
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.ViewModel.OutputView">
            <summary>
            输出数据实体
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutputView.c">
            <summary>
            输出代号
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutputView.m">
            <summary>
            输出内容
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutputView.d">
            <summary>
            输出数据
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.ViewModel.OutTable">
            <summary>
            输出表格数据
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutTable.items">
            <summary>
            数据组
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutTable.total">
            <summary>
            总计
            </summary>
        </member>
    </members>
</doc>
