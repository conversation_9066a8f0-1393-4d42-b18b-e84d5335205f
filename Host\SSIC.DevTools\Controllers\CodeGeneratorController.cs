using Microsoft.AspNetCore.Mvc;
using SSIC.DevTools.BuilderTools;
using Serilog;

namespace SSIC.DevTools.Controllers
{
    /// <summary>
    /// 代码生成器API控制器
    /// 支持通过实体名称数组同时生成对应的服务和控制器代码
    /// 实体名称数组格式：["User", "Role", "Permission"]
    /// 每个实体名称将生成：I{EntityName}Service.cs、{EntityName}Service.cs、{EntityName}Controller.cs
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class CodeGeneratorController : ControllerBase
    {
        /// <summary>
        /// 生成模块化项目代码
        /// </summary>
        /// <param name="request">生成请求参数</param>
        /// <returns></returns>
        [HttpPost("generate-modules")]
        public async Task<IActionResult> GenerateModules([FromBody] GenerateModulesRequest? request = null)
        {
            try
            {
                Log.Information("开始通过API生成模块化项目代码...");

                var generator = new CreateModuleProject();
                string result;

                if (!string.IsNullOrEmpty(request?.ModuleName))
                {
                    // 生成指定模块
                    // 优先使用Entities，如果为空则使用EntityNames作为向后兼容
                    var entities = request.Entities;
                    var entityNames = entities?.Select(e => e.Name).ToArray() ?? request.EntityNames ?? request.ServiceNames;
                    result = generator.CreateModuleFiles(request.ModuleName, entities, entityNames, request.ControllerNames);
                    Log.Information($"指定模块 {request.ModuleName} 代码生成完成");
                }
                else
                {
                    // 生成所有模块
                    // 优先使用Entities，如果为空则使用EntityNames作为向后兼容
                    var entities = request?.Entities;
                    var entityNames = entities?.Select(e => e.Name).ToArray() ?? request?.EntityNames ?? request?.ServiceNames;
                    result = generator.CreateModuleFiles(entities, entityNames, request?.ControllerNames);
                    Log.Information("所有模块代码生成完成");
                }

                return Ok(new
                {
                    success = true,
                    message = result,
                    timestamp = DateTime.Now,
                    request = new
                    {
                        moduleName = request?.ModuleName,
                        entityNames = request?.EntityNames,
                        serviceNames = request?.ServiceNames,
                        controllerNames = request?.ControllerNames,
                        entityCount = request?.EntityNames?.Length ?? 0,
                        serviceCount = request?.ServiceNames?.Length ?? 0,
                        controllerCount = request?.ControllerNames?.Length ?? 0
                    },
                    generatedStructure = new
                    {
                        description = "生成的模块化项目结构",
                        structure = new[]
                        {
                            "SSIC.Modules/{ModuleName}/",
                            "├── SSIC.Modules.{ModuleName}.Services/",
                            "│   ├── Interfaces/",
                            "│   │   ├── I{EntityName}Service.cs",
                            "│   │   └── Expands/I{EntityName}Service.cs",
                            "│   ├── Implementations/",
                            "│   │   ├── {EntityName}Service.cs",
                            "│   │   └── Expands/{EntityName}Service.cs",
                            "│   └── SSIC.Modules.{ModuleName}.Services.csproj",
                            "├── SSIC.Modules.{ModuleName}.Controller/",
                            "│   ├── Controllers/",
                            "│   │   ├── {EntityName}Controller.cs",
                            "│   │   └── Expands/{EntityName}Controller.cs",
                            "│   ├── Program.cs",
                            "│   └── SSIC.Modules.{ModuleName}.Controller.csproj",
                            "└── SSIC.Modules.{ModuleName}.sln"
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                Log.Error(ex, "生成模块化项目代码时发生错误");

                return StatusCode(500, new
                {
                    success = false,
                    message = "代码生成失败",
                    error = ex.Message,
                    timestamp = DateTime.Now
                });
            }
        }

        /// <summary>
        /// 生成传统项目代码
        /// </summary>
        /// <returns></returns>
        [HttpPost("generate-traditional")]
        public async Task<IActionResult> GenerateTraditional()
        {
            try
            {
                Log.Information("开始通过API生成传统项目代码...");
                
                var generator = new CreateProject();
                var result = generator.createfile();
                
                Log.Information("传统项目代码生成完成");
                
                return Ok(new
                {
                    success = true,
                    message = "传统项目代码生成完成",
                    timestamp = DateTime.Now,
                    generatedStructure = new
                    {
                        description = "生成的传统项目结构",
                        structure = new[]
                        {
                            "SSFB.Business/{BusinessName}/",
                            "├── IService/",
                            "│   ├── I{EntityName}Service.cs",
                            "│   └── Expands/I{EntityName}Service.cs",
                            "└── Service/",
                            "    ├── {EntityName}Service.cs",
                            "    └── Expands/{EntityName}Service.cs",
                            "SSFB.WebApi/Controllers/{BusinessName}/",
                            "├── {EntityName}Controller.cs",
                            "└── Expands/{EntityName}Controller.cs"
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                Log.Error(ex, "生成传统项目代码时发生错误");
                
                return StatusCode(500, new
                {
                    success = false,
                    message = "代码生成失败",
                    error = ex.Message,
                    timestamp = DateTime.Now
                });
            }
        }

        /// <summary>
        /// 获取实体信息
        /// </summary>
        /// <returns></returns>
        [HttpGet("entities")]
        public async Task<IActionResult> GetEntities()
        {
            try
            {
                var ServerHost = Directory.GetCurrentDirectory();
                var HostPath = ServerHost.Substring(0, ServerHost.LastIndexOf("\\"));
                DirectoryInfo theFolder = new DirectoryInfo($"{HostPath}\\SSIC.Entity\\");
                
                if (!theFolder.Exists)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "SSIC.Entity文件夹不存在",
                        path = theFolder.FullName
                    });
                }

                DirectoryInfo[] dirInfo = theFolder.GetDirectories()
                    .Where(n => n.Name.ToLower() != "entitybase" && 
                               n.Name.ToLower() != "tenantentity" && 
                               n.Name.ToLower() != "bin" && 
                               n.Name.ToLower() != "obj")
                    .ToArray();

                var modules = dirInfo.Select(dir => new
                {
                    moduleName = dir.Name,
                    path = dir.FullName,
                    entities = dir.GetFiles("*.cs").Select(f => new
                    {
                        entityName = f.Name.Replace(".cs", ""),
                        fileName = f.Name,
                        lastModified = f.LastWriteTime
                    }).ToArray()
                }).ToArray();

                return Ok(new
                {
                    success = true,
                    message = "获取实体信息成功",
                    entityPath = theFolder.FullName,
                    moduleCount = modules.Length,
                    modules = modules,
                    timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                Log.Error(ex, "获取实体信息时发生错误");
                
                return StatusCode(500, new
                {
                    success = false,
                    message = "获取实体信息失败",
                    error = ex.Message,
                    timestamp = DateTime.Now
                });
            }
        }

        /// <summary>
        /// 获取模板信息
        /// </summary>
        /// <returns></returns>
        [HttpGet("templates")]
        public async Task<IActionResult> GetTemplates()
        {
            try
            {
                var ServerHost = Directory.GetCurrentDirectory();
                var HostPath = ServerHost.Substring(0, ServerHost.LastIndexOf("\\"));
                DirectoryInfo templateFolder = new DirectoryInfo($"{HostPath}\\SSIC.Infrastructure\\Template\\");
                
                if (!templateFolder.Exists)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "Template文件夹不存在",
                        path = templateFolder.FullName
                    });
                }

                var templates = templateFolder.GetDirectories().Select(dir => new
                {
                    templateType = dir.Name,
                    path = dir.FullName,
                    templates = dir.GetFiles("*.html").Select(f => new
                    {
                        templateName = f.Name,
                        fileName = f.Name,
                        lastModified = f.LastWriteTime
                    }).ToArray()
                }).ToArray();

                return Ok(new
                {
                    success = true,
                    message = "获取模板信息成功",
                    templatePath = templateFolder.FullName,
                    templateTypeCount = templates.Length,
                    templates = templates,
                    timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                Log.Error(ex, "获取模板信息时发生错误");
                
                return StatusCode(500, new
                {
                    success = false,
                    message = "获取模板信息失败",
                    error = ex.Message,
                    timestamp = DateTime.Now
                });
            }
        }
    }

    /// <summary>
    /// 生成模块请求参数
    /// </summary>
    public class GenerateModulesRequest
    {
        /// <summary>
        /// 模块名称，为空则生成所有模块
        /// </summary>
        public string? ModuleName { get; set; }

        /// <summary>
        /// 要生成的实体信息数组，包含实体名称和中文描述
        /// </summary>
        public EntityInfo[]? Entities { get; set; }

        /// <summary>
        /// 要生成的实体名称数组，为空则生成所有实体对应的服务和控制器（已弃用，使用Entities参数）
        /// </summary>
        public string[]? EntityNames { get; set; }

        /// <summary>
        /// 要生成的服务名称数组，为空则生成所有实体对应的服务（已弃用，使用Entities参数）
        /// </summary>
        public string[]? ServiceNames { get; set; }

        /// <summary>
        /// 要生成的控制器名称数组，为空则生成所有实体对应的控制器（已弃用，使用Entities参数）
        /// </summary>
        public string[]? ControllerNames { get; set; }
    }

    /// <summary>
    /// 实体信息
    /// </summary>
    public class EntityInfo
    {
        /// <summary>
        /// 实体名称（英文）
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 实体中文描述
        /// </summary>
        public string Description { get; set; } = string.Empty;
    }
}
