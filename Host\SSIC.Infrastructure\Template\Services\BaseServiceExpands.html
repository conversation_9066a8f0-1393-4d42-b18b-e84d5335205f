/*
*代码由框架生成,任何更改都可能导致被代码生成器覆盖
*Service提供业务逻辑操作，如果要增加业务逻辑请在当前目录下Expands文件夹{EntityName}Service编写代码
*/
using SSIC.Modules.{BusinessName}.Services.Interfaces;
using SSIC.Infrastructure.BaseProvider;
using SSIC.Infrastructure.DependencyInjection;
using SSIC.Entity.{BusinessName};
using FreeSql;
using Microsoft.Extensions.Logging;

namespace SSIC.Modules.{BusinessName}.Services.Implementations
{
    /// <summary>
    /// {EntityName}服务实现扩展类
    /// 在此处实现自定义的业务逻辑方法
    /// </summary>
    public partial class {EntityName}Service
    {
        // 在此处添加自定义业务方法实现
        // 例如：
        // /// <summary>
        // /// 根据名称查询{EntityName}
        // /// </summary>
        // /// <param name="name">名称</param>
        // /// <returns></returns>
        // public async Task<List<{EntityName}>> GetByNameAsync(string name)
        // {
        //     return await Repository.Where(x => x.Name == name).ToListAsync();
        // }
    }
}
