﻿using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json.Linq;
using SSIC.Entity;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Text.Json;

namespace SSIC.Infrastructure.ConfigurableOptions.Realization
{
    public static class ConfigurableOptions
    {
        /// <summary>
        /// 自动检索并注册所有实现 IConfigurableOptions 接口的配置选项，并将配置对象添加到服务集合中
        /// </summary>
        /// <param name="services">服务集合</param>
        public static void AddAllConfigurableOptions(this IServiceCollection services, ref IConfiguration configuration)
        {
            // 获取应用程序的基目录
            var directory = AppDomain.CurrentDomain.BaseDirectory;
            // 获取基目录下所有以 "settings.json" 结尾的文件
            var settingsFiles = Directory.GetFiles(directory, "*settings.json");

            // 合并所有配置文件
            var mergedConfig = new JObject();
            foreach (var settingsFile in settingsFiles)
            {
                var json = File.ReadAllText(settingsFile);
                var jObject = JObject.Parse(json);
                MergeJsonObjects(mergedConfig, jObject);
            }

            // 拿到原本的 IConfiguration
            var existingConfiguration = configuration;

            // 创建新的配置（保留旧的，加上新流）
            var mergedConfigStream = new MemoryStream(Encoding.UTF8.GetBytes(mergedConfig.ToString()));

            var finalConfiguration = new ConfigurationBuilder()
                .AddConfiguration(existingConfiguration)
                .AddJsonStream(mergedConfigStream)
                .Build();

            // 替换掉 builder.Configuration
            configuration = finalConfiguration;
            //Console.WriteLine($"配置合并完成:{ConfigurationToJson(finalConfiguration)}");
            // 然后正常注册服务
           services.AddSingleton<IConfiguration>(finalConfiguration);
            // 获取所有实现 IConfigurableOptions 接口的类型
            var optionsTypes = GetAllOptionsTypes();

            // 将每个配置选项的实例注册为单例服务
            foreach (var optionsType in optionsTypes)
            {
                var entityName = GetEntityName(optionsType);
                if (typeof(IConfigurableOptions).IsAssignableFrom(optionsType))
                {
                    var options = Activator.CreateInstance(optionsType);
                    configuration.GetSection(entityName).Bind(options);
                    services.AddSingleton(optionsType, options);
                }

                // ✅ 如果是 List<T> 也进行注册
                var listType = typeof(List<>).MakeGenericType(optionsType);
                var configSection = finalConfiguration.GetSection(entityName);
                var listInstance = (IList)Activator.CreateInstance(listType);

                foreach (var obj in configSection.GetChildren())
                {
                    var instance = Activator.CreateInstance(optionsType);
                    obj.Bind(instance);
                    listInstance.Add(instance);
                }

                services.AddSingleton(listType, listInstance);

            }
        }

        /// <summary>
        /// 合并两个 JSON 对象，同一属性进行覆盖，不同属性进行合并
        /// </summary>
        /// <param name="target">目标 JSON 对象</param>
        /// <param name="source">源 JSON 对象</param>
        private static void MergeJsonObjects(JObject target, JObject source)
        {
            foreach (var property in source.Properties())
            {
                if (target[property.Name] == null)
                {
                    target[property.Name] = property.Value;
                }
                else if (target[property.Name].Type == JTokenType.Object && property.Value.Type == JTokenType.Object)
                {
                    MergeJsonObjects((JObject)target[property.Name], (JObject)property.Value);
                }
                else
                {
                    target[property.Name] = property.Value; // 冲突时覆盖策略
                }
            }
        }
        public static string ConfigurationToJson(IConfiguration configuration)
        {
            var dict = new Dictionary<string, object>();
            foreach (var child in configuration.GetChildren())
            {
                dict[child.Key] = GetValue(child);
            }

            var options = new JsonSerializerOptions
            {
                WriteIndented = true // 格式化输出
            };

            return JsonSerializer.Serialize(dict, options);
        }

        private static object GetValue(IConfigurationSection section)
        {
            if (section.GetChildren() is var children && children.Any())
            {
                var dict = new Dictionary<string, object>();
                foreach (var child in children)
                {
                    dict[child.Key] = GetValue(child);
                }
                return dict;
            }
            else
            {
                return section.Value ?? string.Empty;
            }
        }
        /// <summary>
        /// 获取所有实现 IConfigurableOptions 接口的类型
        /// </summary>
        /// <returns>类型集合</returns>
        private static IEnumerable<Type> GetAllOptionsTypes()
        {
            return AppDomain.CurrentDomain.GetAssemblies()
                .SelectMany(a => a.GetTypes())
                .Where(t => typeof(IConfigurableOptions).IsAssignableFrom(t) && t.IsClass && !t.IsAbstract);
        }

        /// <summary>
        /// 获取实体名称
        /// </summary>
        /// <typeparam name="T">类型参数</typeparam>
        /// <returns>实体名称</returns>
        private static string GetEntityName<T>()
        {
            var entityName = typeof(T).Name;

            if (entityName.ToLower().EndsWith("options"))
            {
                entityName = entityName.Substring(0, entityName.Length - "options".Length);
            }

            return entityName;
        }

        /// <summary>
        /// 获取实体名称
        /// </summary>
        /// <param name="type">类型</param>
        /// <returns>实体名称</returns>
        private static string GetEntityName(Type type)
        {
            var entityName = type.Name;

            if (entityName.ToLower().EndsWith("options"))
            {
                entityName = entityName.Substring(0, entityName.Length - "options".Length);
            }

            return entityName;
        }

        /// <summary>
        /// 检索目录下的配置文件路径
        /// </summary>
        /// <param name="entityName">实体名称</param>
        /// <returns>配置文件路径</returns>
        private static string GetConfigFilePath(string entityName)
        {
            var directory = AppDomain.CurrentDomain.BaseDirectory;
            var settingsFiles = Directory.GetFiles(directory, "*settings.json");

            var configFilePath = settingsFiles.FirstOrDefault(file =>
                Path.GetFileNameWithoutExtension(file).Equals($"{entityName}settings", StringComparison.OrdinalIgnoreCase));

            if (configFilePath == null)
            {
                throw new FileNotFoundException($"没有找到匹配的配置文件: {entityName}settings.json");
            }

            return configFilePath;
        }

        /// <summary>
        /// 从服务集合中获取配置选项的实例
        /// </summary>
        /// <typeparam name="T">配置选项的类型</typeparam>
        /// <param name="services">服务集合</param>
        /// <returns>配置选项实例</returns>
        public static T GetOptions<T>(this IServiceCollection services)
            where T : class
        {
            using (var serviceProvider = services.BuildServiceProvider())
            {
                return serviceProvider.GetRequiredService<T>();
            }
        }

        /// <summary>
        /// 从服务提供者中获取配置选项的实例
        /// </summary>
        /// <typeparam name="T">配置选项的类型</typeparam>
        /// <param name="provider">服务提供者</param>
        /// <returns>配置选项实例</returns>
        public static T GetOptions<T>(this IServiceProvider provider)
            where T : class
        {
            return provider.GetRequiredService<T>();
        }
        public static List<T> GetOptionsList<T>(this IServiceProvider provider) where T : class
        {
            var options = provider.GetService<IEnumerable<T>>();
            return options?.ToList() ?? throw new InvalidOperationException($"No service for type 'List<{typeof(T).FullName}>' has been registered.");
        }
    }
}
