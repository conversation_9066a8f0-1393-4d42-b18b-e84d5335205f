# SSIC 代码生成器

## 🎯 概述

SSIC 代码生成器是一个强大的工具，用于自动生成符合SSIC模块化架构的代码。支持两种生成模式：

1. **模块化项目结构** (推荐) - 生成符合 `SSIC.Modules.{ModuleName}` 结构的代码
2. **传统项目结构** (兼容) - 兼容旧版本的代码生成方式

## 🚀 快速开始

### 方式1：Web界面 (推荐)

1. 启动 SSIC.DevTools 项目
2. 访问 `http://localhost:5000/codegen` 或 `http://localhost:5000`
3. 在Web界面中点击相应按钮生成代码

### 方式2：API调用

```bash
# 生成模块化项目
curl -X POST http://localhost:5000/api/CodeGenerator/generate-modules

# 生成传统项目
curl -X POST http://localhost:5000/api/CodeGenerator/generate-traditional

# 获取实体信息
curl http://localhost:5000/api/CodeGenerator/entities

# 获取模板信息
curl http://localhost:5000/api/CodeGenerator/templates
```

### 方式3：代码调用

```csharp
// 生成模块化项目
var generator = new CreateModuleProject();
var result = generator.CreateModuleFiles();

// 生成传统项目
var traditionalGenerator = new CreateProject();
var traditionalResult = traditionalGenerator.createfile();
```

## 🏗️ 生成的项目结构

### 模块化项目结构

```
SSIC.Modules/
├── SSIC.Modules.{ModuleName}/
│   ├── SSIC.Modules.{ModuleName}.Services/
│   │   ├── Interfaces/
│   │   │   ├── I{EntityName}Service.cs          # 服务接口
│   │   │   └── Expands/
│   │   │       └── I{EntityName}Service.cs      # 扩展接口
│   │   ├── Implementations/
│   │   │   ├── {EntityName}Service.cs           # 服务实现
│   │   │   └── Expands/
│   │   │       └── {EntityName}Service.cs       # 扩展实现
│   │   ├── ActionModels/                        # API模型
│   │   ├── Configuration/                       # 配置类
│   │   └── SSIC.Modules.{ModuleName}.Services.csproj
│   ├── SSIC.Modules.{ModuleName}.Controllers/
│   │   ├── Controllers/
│   │   │   ├── {EntityName}Controller.cs        # 控制器
│   │   │   └── Expands/
│   │   │       └── {EntityName}Controller.cs    # 扩展控制器
│   │   ├── Program.cs                           # 启动文件
│   │   └── SSIC.Modules.{ModuleName}.Controllers.csproj
│   └── SSIC.Modules.{ModuleName}.sln            # 解决方案文件
```

### 传统项目结构

```
SSFB.Business/
├── {BusinessName}/
│   ├── IService/
│   │   ├── I{EntityName}Service.cs
│   │   └── Expands/
│   │       └── I{EntityName}Service.cs
│   └── Service/
│       ├── {EntityName}Service.cs
│       └── Expands/
│           └── {EntityName}Service.cs
SSFB.WebApi/Controllers/
└── {BusinessName}/
    ├── {EntityName}Controller.cs
    └── Expands/
        └── {EntityName}Controller.cs
```

## 📋 使用前提

1. **实体文件存在**：确保 `SSIC.Entity` 文件夹中有实体类文件
2. **模板文件存在**：确保 `SSIC.Infrastructure/Template` 文件夹中有模板文件
3. **项目结构正确**：按照SSIC标准项目结构组织代码

## 🔧 模板文件

代码生成器使用以下模板文件：

### 服务层模板
- `IServices/BaseIService.html` - 服务接口基础模板
- `IServices/BaseIServiceExpands.html` - 服务接口扩展模板
- `Services/BaseService.html` - 服务实现基础模板
- `Services/BaseServiceExpands.html` - 服务实现扩展模板

### 控制器模板
- `Controllers/BaseController.html` - 控制器基础模板
- `Controllers/BaseControllerExpands.html` - 控制器扩展模板

## ✨ 特性

### 模块化项目的优势

1. **清晰的架构分层**：Controller → Service → Repository
2. **自动依赖注入**：服务接口继承 `IScoped`，自动注册
3. **完整的项目文件**：包含 `.csproj` 和 `.sln` 文件
4. **支持扩展**：使用 partial 类和 Expands 文件夹
5. **标准化命名**：符合SSIC命名规范

### 生成的代码特点

1. **服务接口**：继承 `IServiceBase<T>` 和 `IScoped`
2. **服务实现**：继承 `ServiceBase<T>`，提供完整CRUD操作
3. **控制器**：继承 `BaseController<TEntity, TService>`
4. **自动路由**：API路由自动配置为 `api/{EntityName}`

## 🎯 最佳实践

### 1. 代码扩展

在 `Expands` 文件夹中添加自定义业务逻辑：

```csharp
// SSIC.Modules.Auth.Services.Interfaces.Expands.IUserService
public partial interface IUserService
{
    Task<User> GetUserByUsernameAsync(string username);
    Task<bool> ValidatePasswordAsync(int userId, string password);
}

// SSIC.Modules.Auth.Services.Implementations.Expands.UserService
public partial class UserService
{
    public async Task<User> GetUserByUsernameAsync(string username)
    {
        return await _fsqlRepository.Select
            .Where(u => u.Username == username && u.IsActive)
            .FirstAsync();
    }
}
```

### 2. 依赖注入使用

```csharp
[ApiController]
[Route("api/[controller]")]
public class AuthController : ControllerBase
{
    private readonly IUserService _userService;
    
    public AuthController(IUserService userService)
    {
        _userService = userService;
    }
    
    [HttpPost("login")]
    public async Task<IActionResult> Login(LoginRequest request)
    {
        var user = await _userService.GetUserByUsernameAsync(request.Username);
        // 业务逻辑...
    }
}
```

### 3. 配置管理

```csharp
// 在Services项目中创建配置类
public class AuthModuleSettings : IConfigurableOptions
{
    public JwtSettings Jwt { get; set; } = new();
    public PasswordPolicySettings PasswordPolicy { get; set; } = new();
}

// 在服务中使用配置
public class AuthService : ServiceBase<User>, IAuthService
{
    private readonly AuthModuleSettings _settings;
    
    public AuthService(IFreeSql fsql, AuthModuleSettings settings) : base(fsql)
    {
        _settings = settings;
    }
}
```

## 🐛 故障排除

### 常见问题

1. **实体文件夹不存在**
   - 确保 `SSIC.Entity` 文件夹存在且包含实体类文件

2. **模板文件缺失**
   - 检查 `SSIC.Infrastructure/Template` 文件夹是否包含所有必要的模板文件

3. **生成的代码编译错误**
   - 检查实体类是否正确定义
   - 确保项目引用正确

4. **依赖注入失败**
   - 确保服务接口继承了 `IScoped`
   - 检查 `HostBuilder` 是否正确配置了依赖注入扫描

### 日志查看

代码生成器会在以下位置生成日志：
- 控制台输出
- `logs/codegen-{date}.txt` 文件

## 📞 支持

如果遇到问题或需要帮助，请：

1. 查看生成的日志文件
2. 检查实体和模板文件是否正确
3. 确认项目结构符合SSIC标准

## 🎉 总结

SSIC 代码生成器提供了强大而灵活的代码生成能力，支持现代化的模块化架构，同时保持与旧版本的兼容性。通过Web界面、API或代码调用，都能轻松生成高质量的业务代码。
