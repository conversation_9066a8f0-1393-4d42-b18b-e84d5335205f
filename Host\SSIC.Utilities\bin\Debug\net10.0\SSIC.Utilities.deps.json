{"runtimeTarget": {"name": ".NETCoreApp,Version=v10.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v10.0": {"SSIC.Utilities/1.0.0": {"dependencies": {"Masuit.Tools.Core": "2025.5.0", "QRCoder": "1.6.0"}, "runtime": {"SSIC.Utilities.dll": {}}}, "AngleSharp/1.3.0": {"runtime": {"lib/net8.0/AngleSharp.dll": {"assemblyVersion": "1.3.0.0", "fileVersion": "1.3.0.0"}}}, "AngleSharp.Css/1.0.0-beta.151": {"dependencies": {"AngleSharp": "1.3.0"}, "runtime": {"lib/net8.0/AngleSharp.Css.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Castle.Core/5.2.1": {"dependencies": {"System.Diagnostics.EventLog": "9.0.8"}, "runtime": {"lib/net6.0/Castle.Core.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.2.1.0"}}}, "DnsClient/1.8.0": {"runtime": {"lib/net8.0/DnsClient.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}}}, "Masuit.Tools.Abstractions/2025.5.0": {"dependencies": {"AngleSharp": "1.3.0", "AngleSharp.Css": "1.0.0-beta.151", "Castle.Core": "5.2.1", "DnsClient": "1.8.0", "Microsoft.Extensions.Configuration.Json": "9.0.8", "Newtonsoft.Json": "13.0.3", "SharpCompress": "0.40.0", "SixLabors.ImageSharp": "3.1.11", "SixLabors.ImageSharp.Drawing": "2.1.7", "System.Diagnostics.PerformanceCounter": "9.0.8", "System.Management": "9.0.8"}, "runtime": {"lib/net9.0/Masuit.Tools.Abstractions.dll": {"assemblyVersion": "2.5.9.0", "fileVersion": "2.5.9.0"}}}, "Masuit.Tools.Core/2025.5.0": {"dependencies": {"Masuit.Tools.Abstractions": "2025.5.0", "Microsoft.EntityFrameworkCore": "9.0.8"}, "runtime": {"lib/net9.0/Masuit.Tools.Core.dll": {"assemblyVersion": "2.5.9.0", "fileVersion": "2.5.9.1"}}}, "Microsoft.EntityFrameworkCore/9.0.8": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.8", "Microsoft.Extensions.Caching.Memory": "9.0.8", "Microsoft.Extensions.Logging": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "9.0.8.0", "fileVersion": "9.0.825.36802"}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.8": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "9.0.8.0", "fileVersion": "9.0.825.36802"}}}, "Microsoft.Extensions.Caching.Abstractions/9.0.8": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Caching.Memory/9.0.8": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.8", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Configuration/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.8": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.8", "Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.8", "Microsoft.Extensions.FileProviders.Physical": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Configuration.Json/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.8", "Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.8", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.DependencyInjection/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.8": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.8": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.8": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.8", "Microsoft.Extensions.FileSystemGlobbing": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.8": {"runtime": {"lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Logging/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Options/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Primitives/9.0.8": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "QRCoder/1.6.0": {"runtime": {"lib/net6.0/QRCoder.dll": {"assemblyVersion": "1.6.0.0", "fileVersion": "1.6.0.0"}}}, "SharpCompress/0.40.0": {"dependencies": {"ZstdSharp.Port": "0.8.5"}, "runtime": {"lib/net8.0/SharpCompress.dll": {"assemblyVersion": "0.40.0.0", "fileVersion": "0.40.0.0"}}}, "SixLabors.Fonts/2.1.3": {"runtime": {"lib/net6.0/SixLabors.Fonts.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.1.3.0"}}}, "SixLabors.ImageSharp/3.1.11": {"runtime": {"lib/net6.0/SixLabors.ImageSharp.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.1.11.0"}}}, "SixLabors.ImageSharp.Drawing/2.1.7": {"dependencies": {"SixLabors.Fonts": "2.1.3", "SixLabors.ImageSharp": "3.1.11"}, "runtime": {"lib/net6.0/SixLabors.ImageSharp.Drawing.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.1.7.0"}}}, "System.CodeDom/9.0.8": {"runtime": {"lib/net9.0/System.CodeDom.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "System.Configuration.ConfigurationManager/9.0.8": {"dependencies": {"System.Diagnostics.EventLog": "9.0.8", "System.Security.Cryptography.ProtectedData": "9.0.8"}, "runtime": {"lib/net9.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "System.Diagnostics.EventLog/9.0.8": {"runtime": {"lib/net9.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Diagnostics.EventLog.Messages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "9.0.0.0", "fileVersion": "0.0.0.0"}, "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "System.Diagnostics.PerformanceCounter/9.0.8": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.8"}, "runtime": {"lib/net9.0/System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Diagnostics.PerformanceCounter.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "System.Management/9.0.8": {"dependencies": {"System.CodeDom": "9.0.8"}, "runtime": {"lib/net9.0/System.Management.dll": {"assemblyVersion": "9.0.0.8", "fileVersion": "9.0.825.36511"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Management.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "9.0.0.8", "fileVersion": "9.0.825.36511"}}}, "System.Security.Cryptography.ProtectedData/9.0.8": {"runtime": {"lib/net9.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "ZstdSharp.Port/0.8.5": {"runtime": {"lib/net9.0/ZstdSharp.dll": {"assemblyVersion": "0.8.5.0", "fileVersion": "0.8.5.0"}}}}}, "libraries": {"SSIC.Utilities/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AngleSharp/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-iHzfn4cK6CmhuURNdEpmSQCq5/HZFldEpkbnmqT9My8+6l2Sz3F+NxoqRA8z/jTkWB+SAu5boRdp4v/WtyjuIQ==", "path": "anglesharp/1.3.0", "hashPath": "anglesharp.1.3.0.nupkg.sha512"}, "AngleSharp.Css/1.0.0-beta.151": {"type": "package", "serviceable": true, "sha512": "sha512-oEnqXQcwpc/kkUIi2rxWHfFrmKlcJFpZZZjhEIHVc+aEJFo3U+5cptOHByQh+FW0PCW6ssVJ+GGBgbgyN8YPiw==", "path": "anglesharp.css/1.0.0-beta.151", "hashPath": "anglesharp.css.1.0.0-beta.151.nupkg.sha512"}, "Castle.Core/5.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-wHARzQA695jwwKreOzNsq54KiGqKP38tv8hi8e2FXDEC/sA6BtrX90tVPDkOfVu13PbEzr00TCV8coikl+D1Iw==", "path": "castle.core/5.2.1", "hashPath": "castle.core.5.2.1.nupkg.sha512"}, "DnsClient/1.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-RRwtaCXkXWsx0mmsReGDqCbRLtItfUbkRJlet1FpdciVhyMGKcPd57T1+8Jki9ojHlq9fntVhXQroOOgRak8DQ==", "path": "dnsclient/1.8.0", "hashPath": "dnsclient.1.8.0.nupkg.sha512"}, "Masuit.Tools.Abstractions/2025.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-GHUH5VrQU+sqW+y+sd4T4ySv8O3F95BMNKcCLpt9QA3khRO2scV6eSI2j6xkZPGnaBkdIONz5wTxXrlDQIoGNQ==", "path": "masuit.tools.abstractions/2025.5.0", "hashPath": "masuit.tools.abstractions.2025.5.0.nupkg.sha512"}, "Masuit.Tools.Core/2025.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-JtWJz6R+vh2oOpNQSwm1ElFNmO3cKIqCor4tEoUHSq4axSigsuLuJlaviOZ+c3EyF8ELrKvjH42bTz2girDxRg==", "path": "masuit.tools.core/2025.5.0", "hashPath": "masuit.tools.core.2025.5.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-bNGdPhN762+BIIO5MFYLjafRqkSS1MqLOc/erd55InvLnFxt9H3N5JNsuag1ZHyBor1VtD42U0CHpgqkWeAYgQ==", "path": "microsoft.entityframeworkcore/9.0.8", "hashPath": "microsoft.entityframeworkcore.9.0.8.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-B2yfAIQRRAQ4zvvWqh+HudD+juV3YoLlpXnrog3tU0PM9AFpuq6xo0+mEglN1P43WgdcUiF+65CWBcZe35s15Q==", "path": "microsoft.entityframeworkcore.abstractions/9.0.8", "hashPath": "microsoft.entityframeworkcore.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-4h7bsVoKoiK+SlPM+euX/ayGnKZhl47pPCidLTiio9xyG+vgVVfcYxcYQgjm0SCrdSxjG0EGIAKF8EFr3G8Ifw==", "path": "microsoft.extensions.caching.abstractions/9.0.8", "hashPath": "microsoft.extensions.caching.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-grR+oPyj8HVn4DT8CFUUdSw2pZZKS13KjytFe4txpHQliGM1GEDotohmjgvyl3hm7RFB3FRqvbouEX3/1ewp5A==", "path": "microsoft.extensions.caching.memory/9.0.8", "hashPath": "microsoft.extensions.caching.memory.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-6m+8Xgmf8UWL0p/oGqBM+0KbHE5/ePXbV1hKXgC59zEv0aa0DW5oiiyxDbK5kH5j4gIvyD5uWL0+HadKBJngvQ==", "path": "microsoft.extensions.configuration/9.0.8", "hashPath": "microsoft.extensions.configuration.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-yNou2KM35RvzOh4vUFtl2l33rWPvOCoba+nzEDJ+BgD8aOL/jew4WPCibQvntRfOJ2pJU8ARygSMD+pdjvDHuA==", "path": "microsoft.extensions.configuration.abstractions/9.0.8", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-2jgx58Jpk3oKT7KRn8x/cFf3QDTjQP+KUbyBnynAcB2iBx1Eq9EdNMCu0QEbYuaZOaQru/Kwdffary+hn58Wwg==", "path": "microsoft.extensions.configuration.fileextensions/9.0.8", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-vjxzcnL7ul322+kpvELisXaZl8/5MYs6JfI9DZLQWsao1nA/4FL48yPwDK986hbJTWc64JxOOaMym0SQ/dy32w==", "path": "microsoft.extensions.configuration.json/9.0.8", "hashPath": "microsoft.extensions.configuration.json.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-JJjI2Fa+QtZcUyuNjbKn04OjIUX5IgFGFu/Xc+qvzh1rXdZHLcnqqVXhR4093bGirTwacRlHiVg1XYI9xum6QQ==", "path": "microsoft.extensions.dependencyinjection/9.0.8", "hashPath": "microsoft.extensions.dependencyinjection.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-xY3lTjj4+ZYmiKIkyWitddrp1uL5uYiweQjqo4BKBw01ZC4HhcfgLghDpPZcUlppgWAFqFy9SgkiYWOMx365pw==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.8", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-4zZbQ4w+hCMm9J+z5NOj3giIPT2MhZxx05HX/MGuAmDBbjOuXlYIIRN+t4V6OLxy5nXZIcXO+dQMB/OWubuDkw==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.8", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-FlOe2i7UUIfY0l0ChaIYtlXjdWWutR4DMRKZaGD6z4G1uVTteFkbBfxUIoi1uGmrZQxXe/yv/cfwiT0tK2xyXA==", "path": "microsoft.extensions.fileproviders.physical/9.0.8", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-96Ub5LmwYfIGVoXkbe4kjs+ivK6fLBTwKJAOMfUNV0R+AkZRItlgROFqXEWMUlXBTPM1/kKu26Ueu5As6RDzJA==", "path": "microsoft.extensions.filesystemglobbing/9.0.8", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-Z/7ze+0iheT7FJeZPqJKARYvyC2bmwu3whbm/48BJjdlGVvgDguoCqJIkI/67NkroTYobd5geai1WheNQvWrgA==", "path": "microsoft.extensions.logging/9.0.8", "hashPath": "microsoft.extensions.logging.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-pYnAffJL7ARD/HCnnPvnFKSIHnTSmWz84WIlT9tPeQ4lHNiu0Az7N/8itihWvcF8sT+VVD5lq8V+ckMzu4SbOw==", "path": "microsoft.extensions.logging.abstractions/9.0.8", "hashPath": "microsoft.extensions.logging.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-OmTaQ0v4gxGQkehpwWIqPoEiwsPuG/u4HUsbOFoWGx4DKET2AXzopnFe/fE608FIhzc/kcg2p8JdyMRCCUzitQ==", "path": "microsoft.extensions.options/9.0.8", "hashPath": "microsoft.extensions.options.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-tizSIOEsIgSNSSh+hKeUVPK7xmTIjR8s+mJWOu1KXV3htvNQiPMFRMO17OdI1y/4ZApdBVk49u/08QGC9yvLug==", "path": "microsoft.extensions.primitives/9.0.8", "hashPath": "microsoft.extensions.primitives.9.0.8.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "QRCoder/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-XmPA81eo+oRxBuyVdswsSkTGTE1d3thfF11Z1PdD7oB56A6HU4G4AAOdySmGRMb/cljwlFTMWUtosGEnwpS6GA==", "path": "qrcoder/1.6.0", "hashPath": "qrcoder.1.6.0.nupkg.sha512"}, "SharpCompress/0.40.0": {"type": "package", "serviceable": true, "sha512": "sha512-yP/aFX1jqGikVF7u2f05VEaWN4aCaKNLxSas82UgA2GGVECxq/BcqZx3STHCJ78qilo1azEOk1XpBglIuGMb7w==", "path": "sharpcompress/0.40.0", "hashPath": "sharpcompress.0.40.0.nupkg.sha512"}, "SixLabors.Fonts/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-ORWbZ5BHrC/LZvo+Y09MnoJq5VUKD85LsYALk+YI7CHFra+m5arCkz00IntDM6SrAiB22bvSdKtKmuCyHOKlqg==", "path": "sixlabors.fonts/2.1.3", "hashPath": "sixlabors.fonts.2.1.3.nupkg.sha512"}, "SixLabors.ImageSharp/3.1.11": {"type": "package", "serviceable": true, "sha512": "sha512-JfPLyigLthuE50yi6tMt7Amrenr/fA31t2CvJyhy/kQmfulIBAqo5T/YFUSRHtuYPXRSaUHygFeh6Qd933EoSw==", "path": "sixlabors.imagesharp/3.1.11", "hashPath": "sixlabors.imagesharp.3.1.11.nupkg.sha512"}, "SixLabors.ImageSharp.Drawing/2.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-9KwCo9Fa350cx6ckpsy8NqXQZKwir4RQ8Kj0sdCmJA7wsK9FMyfgC527Sn4l/D6bj2ditSHlhS7dGzcgGszvSQ==", "path": "sixlabors.imagesharp.drawing/2.1.7", "hashPath": "sixlabors.imagesharp.drawing.2.1.7.nupkg.sha512"}, "System.CodeDom/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-kxg1TE/BMUAaMve/lPH9Bds+CFF4JPrN9WkQqHejc7UP6PO4KDAjZNF0uhvpZmoLR+EvHdRt232J++ZDI7aGqA==", "path": "system.codedom/9.0.8", "hashPath": "system.codedom.9.0.8.nupkg.sha512"}, "System.Configuration.ConfigurationManager/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-BR70HYY5jeOa/jBrd/wyydpuqhFyla2ybQRL/UPBIiSvctVqi18iQoM44Gx41jy7t6wuAdKuTnie6io4j8aq3w==", "path": "system.configuration.configurationmanager/9.0.8", "hashPath": "system.configuration.configurationmanager.9.0.8.nupkg.sha512"}, "System.Diagnostics.EventLog/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-gebRF3JLLJ76jz1CQpvwezNapZUjFq20JQsaGHzBH0DzlkHBLpdhwkOei9usiOkIGMwU/L0ALWpNe1JE+5/itw==", "path": "system.diagnostics.eventlog/9.0.8", "hashPath": "system.diagnostics.eventlog.9.0.8.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-9jQDqjiJ9VF9M2eVdT09gfU8yg5mhqEkfiZeB3hMjlYApNVJK6vXt9u5T8Oj5Hc+H9zNmrRzTjkXtEF5HcbAfg==", "path": "system.diagnostics.performancecounter/9.0.8", "hashPath": "system.diagnostics.performancecounter.9.0.8.nupkg.sha512"}, "System.Management/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-D7icHff0OpEEIOLsTgnPL+Ff1VmJ3tzA8y20LNKgv4hx1MFMA3XGAVlWMCkLQfBkyvEwib6rMG6nP27lf98KRA==", "path": "system.management/9.0.8", "hashPath": "system.management.9.0.8.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-w7+KCnqmtDboV8dxTLxlUltasP7AgzNFdTLq1D/ey50ykgXW+CJBIQkzYZjgPzmjKB+/PGGUKYrH7TSbwrDtRw==", "path": "system.security.cryptography.protecteddata/9.0.8", "hashPath": "system.security.cryptography.protecteddata.9.0.8.nupkg.sha512"}, "ZstdSharp.Port/0.8.5": {"type": "package", "serviceable": true, "sha512": "sha512-TR4j17WeVSEb3ncgL2NqlXEqcy04I+Kk9CaebNDplUeL8XOgjkZ7fP4Wg4grBdPLIqsV86p2QaXTkZoRMVOsew==", "path": "zstdsharp.port/0.8.5", "hashPath": "zstdsharp.port.0.8.5.nupkg.sha512"}}}