﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Microsoft.AspNetCore.Mvc.ApplicationParts;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.Extensions.DependencyInjection;
using SSIC.Infrastructure.Startups.Endpoints;
using SSIC.Infrastructure.Startups.HotReload;
using SSIC.Infrastructure.Startups.ModuleManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace SSIC.Infrastructure.Startups.Extensions
{
    /// <summary>
    /// 注册模块服务
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// 添加模块服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="moduleAssembly">模块程序集</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddModuleServices(this IServiceCollection services, Assembly moduleAssembly)
        {
            try
            {
                // 调用模块的配置方法
                ConfigureModuleServices(services, moduleAssembly);

                // 模块发现逻辑已移至统一的服务中，这里只需要记录日志
                Console.OutputEncoding = System.Text.Encoding.UTF8;
                Console.WriteLine($"Registering module: {moduleAssembly.GetName().Name}");

                // 注册到应用程序部件管理器
                RegisterApplicationPart(services, moduleAssembly);

                // 通知动态端点管理器
                NotifyDynamicEndpointManager(moduleAssembly);

                return services;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"添加模块服务时发生错误: {ex.Message}");
                return services;
            }
        }

        /// <summary>
        /// 配置模块服务
        /// </summary>
        private static void ConfigureModuleServices(IServiceCollection services, Assembly moduleAssembly)
        {
            // 查找模块中的 Startup 类或其他配置类
            var startupType = moduleAssembly.GetTypes()
                .FirstOrDefault(t => t.Name == "Startup" || t.Name.EndsWith("Module"));

            if (startupType != null)
            {
                // 尝试调用 ConfigureServices 方法
                var configureServicesMethod = startupType.GetMethod("ConfigureServices");
                if (configureServicesMethod != null)
                {
                    var startup = Activator.CreateInstance(startupType);
                    configureServicesMethod.Invoke(startup, new object[] { services });
                }
            }
        }

        /// <summary>
        /// 注册应用程序部件
        /// </summary>
        private static void RegisterApplicationPart(IServiceCollection services, Assembly moduleAssembly)
        {
            try
            {
                // 使用全局 ServiceProvider 获取 ApplicationPartManager
                var globalServiceProvider = HotReloadExtensions.GlobalServiceProvider;
                if (globalServiceProvider != null)
                {
                    var partManager = globalServiceProvider.GetService<ApplicationPartManager>();
                    if (partManager != null)
                    {
                        var assemblyName = moduleAssembly.GetName().Name;

                        // 检查是否已经注册了相同名称的程序集（避免重复注册）
                        var existingParts = partManager.ApplicationParts
                            .OfType<AssemblyPart>()
                            .Where(p => p.Assembly.GetName().Name == assemblyName)
                            .ToList();

                        if (existingParts.Any())
                        {
                            // 移除所有相同名称的旧版本
                            foreach (var existingPart in existingParts)
                            {
                                partManager.ApplicationParts.Remove(existingPart);
                                Console.WriteLine($"🔄 已从ApplicationPartManager移除旧版本模块: {assemblyName}");
                            }
                        }

                        // 添加新版本
                        var assemblyPart = new AssemblyPart(moduleAssembly);
                        partManager.ApplicationParts.Add(assemblyPart);
                        Console.WriteLine($"✅ 已将模块 {assemblyName} 添加到ApplicationPartManager");
                    }
                    else
                    {
                        Console.WriteLine($"❌ 未找到ApplicationPartManager，无法注册模块 {moduleAssembly.GetName().Name}");
                    }
                }
                else
                {
                    Console.WriteLine($"❌ GlobalServiceProvider 为空，无法注册模块 {moduleAssembly.GetName().Name}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 注册应用程序部件时发生错误: {ex.Message}");
                Console.WriteLine($"   堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 通知动态端点管理器
        /// </summary>
        private static void NotifyDynamicEndpointManager(Assembly moduleAssembly)
        {
            try
            {
                var assemblyName = moduleAssembly.GetName().Name;
                if (assemblyName != null && assemblyName.StartsWith("SSIC.Modules."))
                {
                    var dynamicEndpointManager = DynamicEndpointManager.Instance;
                    if (dynamicEndpointManager != null)
                    {
                        // 从已卸载记录中移除当前模块
                        dynamicEndpointManager.RecordModuleLoaded(assemblyName);
                        Console.WriteLine($"模块 {assemblyName} 已通知动态端点管理器");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"通知动态端点管理器时发生错误: {ex.Message}");
            }
        }
    }
}