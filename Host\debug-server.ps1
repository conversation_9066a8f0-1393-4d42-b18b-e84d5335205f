# 调试服务器启动脚本

Write-Host "=== SSIC HostServer 调试脚本 ===" -ForegroundColor Green

# 1. 检查端口占用
Write-Host "1. 检查端口5246占用情况..." -ForegroundColor Yellow
try {
    $portCheck = netstat -an | Select-String ":5246"
    if ($portCheck) {
        Write-Host "端口5246已被占用:" -ForegroundColor Red
        Write-Host $portCheck -ForegroundColor Red
    } else {
        Write-Host "端口5246未被占用" -ForegroundColor Green
    }
} catch {
    Write-Host "无法检查端口状态: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. 检查模块文件
Write-Host "`n2. 检查模块文件..." -ForegroundColor Yellow
$authModulePath = "SSIC.HostServer\Modules\SSIC.Modules.Auth.dll"
$sysModulePath = "SSIC.HostServer\Modules\SSIC.Modules.Sys.dll"

if (Test-Path $authModulePath) {
    Write-Host "✓ Auth模块文件存在: $authModulePath" -ForegroundColor Green
} else {
    Write-Host "✗ Auth模块文件不存在: $authModulePath" -ForegroundColor Red
}

if (Test-Path $sysModulePath) {
    Write-Host "✓ Sys模块文件存在: $sysModulePath" -ForegroundColor Green
} else {
    Write-Host "✗ Sys模块文件不存在: $sysModulePath" -ForegroundColor Red
}

# 3. 启动HostServer
Write-Host "`n3. 启动HostServer..." -ForegroundColor Yellow
try {
    Set-Location "SSIC.HostServer"
    Write-Host "当前目录: $(Get-Location)" -ForegroundColor Cyan
    
    # 启动服务器并等待
    Write-Host "正在启动服务器..." -ForegroundColor Yellow
    $process = Start-Process -FilePath "dotnet" -ArgumentList "run" -PassThru -NoNewWindow
    
    # 等待服务器启动
    Write-Host "等待服务器启动..." -ForegroundColor Yellow
    Start-Sleep -Seconds 10
    
    # 检查进程是否还在运行
    if ($process.HasExited) {
        Write-Host "✗ 服务器启动失败，进程已退出" -ForegroundColor Red
        Write-Host "退出代码: $($process.ExitCode)" -ForegroundColor Red
    } else {
        Write-Host "✓ 服务器进程正在运行 (PID: $($process.Id))" -ForegroundColor Green
        
        # 测试API连接
        Write-Host "`n4. 测试API连接..." -ForegroundColor Yellow
        $testUrls = @(
            "http://localhost:5246/scalar/v1",
            "http://localhost:5246/api/auth/role/test",
            "http://localhost:5246/api/sys/weatherforecast/hello"
        )
        
        foreach ($url in $testUrls) {
            Write-Host "测试: $url" -ForegroundColor Cyan
            try {
                $response = Invoke-WebRequest -Uri $url -Method GET -TimeoutSec 5 -ErrorAction Stop
                Write-Host "  ✓ 成功 - 状态码: $($response.StatusCode)" -ForegroundColor Green
            } catch {
                Write-Host "  ✗ 失败 - $($_.Exception.Message)" -ForegroundColor Red
            }
        }
        
        Write-Host "`n服务器正在运行，按任意键停止..." -ForegroundColor Yellow
        Read-Host
        
        # 停止服务器
        Write-Host "停止服务器..." -ForegroundColor Yellow
        $process.Kill()
        $process.WaitForExit()
        Write-Host "服务器已停止" -ForegroundColor Green
    }
    
} catch {
    Write-Host "启动服务器时出错: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    Set-Location ".."
}

Write-Host "`n=== 调试完成 ===" -ForegroundColor Green
