<?xml version="1.0"?>
<doc>
    <assembly>
        <name>SSIC.Modules.Auth.Services</name>
    </assembly>
    <members>
        <member name="T:SSIC.Modules.Auth.Services.Implementations.PermissionService">
            <summary>
            Permission服务实现扩展类
            在此处实现自定义的业务逻辑方法
            </summary>
            <summary>
            Permission服务实现类（权限管理）
            实现Permission实体的业务逻辑操作
            </summary>
        </member>
        <member name="M:SSIC.Modules.Auth.Services.Implementations.PermissionService.#ctor(IFreeSql,Microsoft.Extensions.Logging.ILogger{SSIC.Modules.Auth.Services.Implementations.PermissionService})">
            <summary>
            初始化Permission服务
            </summary>
            <param name="fsql">FreeSql实例</param>
            <param name="logger">日志记录器</param>
        </member>
        <member name="T:SSIC.Modules.Auth.Services.Implementations.RoleService">
            <summary>
            Role服务实现扩展类
            在此处实现自定义的业务逻辑方法
            </summary>
            <summary>
            Role服务实现类（角色管理）
            实现Role实体的业务逻辑操作
            </summary>
        </member>
        <member name="M:SSIC.Modules.Auth.Services.Implementations.RoleService.#ctor(IFreeSql,Microsoft.Extensions.Logging.ILogger{SSIC.Modules.Auth.Services.Implementations.RoleService})">
            <summary>
            初始化Role服务
            </summary>
            <param name="fsql">FreeSql实例</param>
            <param name="logger">日志记录器</param>
        </member>
        <member name="T:SSIC.Modules.Auth.Services.Implementations.UserService">
            <summary>
            User服务实现扩展类
            在此处实现自定义的业务逻辑方法
            </summary>
            <summary>
            User服务实现类（用户管理）
            实现User实体的业务逻辑操作
            </summary>
        </member>
        <member name="M:SSIC.Modules.Auth.Services.Implementations.UserService.#ctor(IFreeSql,Microsoft.Extensions.Logging.ILogger{SSIC.Modules.Auth.Services.Implementations.UserService})">
            <summary>
            初始化User服务
            </summary>
            <param name="fsql">FreeSql实例</param>
            <param name="logger">日志记录器</param>
        </member>
        <member name="T:SSIC.Modules.Auth.Services.Interfaces.IPermissionService">
            <summary>
            Permission服务接口扩展
            在此处添加自定义的业务方法接口定义
            </summary>
            <summary>
            Permission服务接口（权限管理）
            提供Permission实体的业务逻辑操作
            </summary>
        </member>
        <member name="T:SSIC.Modules.Auth.Services.Interfaces.IRoleService">
            <summary>
            Role服务接口扩展
            在此处添加自定义的业务方法接口定义
            </summary>
            <summary>
            Role服务接口（角色管理）
            提供Role实体的业务逻辑操作
            </summary>
        </member>
        <member name="T:SSIC.Modules.Auth.Services.Interfaces.IUserService">
            <summary>
            User服务接口扩展
            在此处添加自定义的业务方法接口定义
            </summary>
            <summary>
            User服务接口（用户管理）
            提供User实体的业务逻辑操作
            </summary>
        </member>
    </members>
</doc>
