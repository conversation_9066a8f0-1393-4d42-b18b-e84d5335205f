/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *Service提供业务逻辑操作，如果要增加业务逻辑请在当前目录下Expands文件夹IPermissionService编写接口
 */
using SSIC.Infrastructure.BaseProvider;
using SSIC.Infrastructure.DependencyInjection;
using SSIC.Infrastructure.DependencyInjection.Interface;
using SSIC.Entity.Auth;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SSIC.Modules.Auth.Services.Interfaces
{
    /// <summary>
    /// Permission服务接口（权限管理）
    /// 提供Permission实体的业务逻辑操作
    /// </summary>
    public partial interface IPermissionService : IServiceBase, IScoped
    {
        // 在此处添加Permission特定的业务方法
    }
}
