﻿using Microsoft.Extensions.Logging;
using SSIC.Infrastructure.OptionsEntity;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace SSIC.Infrastructure.Startups.HotReload
{
    /// <summary>
    /// 模块扫描器
    /// </summary>
    public interface IModuleScanner
    {
        /// <summary>
        /// 扫描模块
        /// </summary>
        Task<List<string>> ScanModulesAsync();

        /// <summary>
        /// 检查文件是否为有效模块
        /// </summary>
        bool IsValidModuleFile(string filePath);
    }

    /// <summary>
    /// 模块扫描器实现
    /// </summary>
    public class ModuleScanner : IModuleScanner
    {
        private readonly HotReloadOptions _options;
        private readonly ModulePathOptions _modulePathOptions;
        private readonly ILogger<ModuleScanner> _logger;

        public ModuleScanner(HotReloadOptions options, ModulePathOptions modulePathOptions, ILogger<ModuleScanner> logger)
        {
            _options = options ?? throw new ArgumentNullException(nameof(options));
            _modulePathOptions = modulePathOptions ?? throw new ArgumentNullException(nameof(modulePathOptions));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 扫描所有监控路径下的模块
        /// </summary>
        public async Task<List<string>> ScanModulesAsync()
        {
            var moduleFiles = new List<string>();

            // 使用模块路径配置扫描
            var moduleDirectory = _modulePathOptions.GetModuleDirectory(true);

            try
            {
                if (!Directory.Exists(moduleDirectory))
                {
                    _logger.LogWarning("模块目录不存在: {Path}", moduleDirectory);
                    return moduleFiles;
                }

                // 根据配置的扫描模式获取模块文件
                var subFolders = _modulePathOptions.GetModuleSubFolders(true);

                foreach (var folder in subFolders)
                {
                    if (!Directory.Exists(folder))
                        continue;
                    // 扫描当前文件夹中的模块文件
                    var files = await Task.Run(() => ScanDirectoryForModules(folder));
                    moduleFiles.AddRange(files);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "扫描模块目录失败: {Path}", moduleDirectory);
            }

            return moduleFiles;
        }

        /// <summary>
        /// 扫描目录中的模块文件
        /// </summary>
        private List<string> ScanDirectoryForModules(string directory)
        {
            var moduleFiles = new List<string>();

            try
            {
                // 根据通配符模式扫描文件
                foreach (var pattern in _modulePathOptions.WildcardPatterns)
                {
                    var files = Directory.GetFiles(directory, pattern, _modulePathOptions.GetSearchOption());

                    foreach (var file in files)
                    {
                        if (IsValidModuleFile(file))
                        {
                            moduleFiles.Add(file);
                            _logger.LogDebug("找到模块文件: {File}", file);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "扫描目录失败: {Path}", directory);
            }

            return moduleFiles;
        }

        /// <summary>
        /// 检查文件是否为有效模块
        /// </summary>
        public bool IsValidModuleFile(string filePath)
        {
            if (!File.Exists(filePath))
                return false;

            var fileName = Path.GetFileName(filePath);

            // 检查文件扩展名
            if (!fileName.EndsWith(".dll", StringComparison.OrdinalIgnoreCase))
                return false;

            // 使用通配符模式匹配
            foreach (var pattern in _modulePathOptions.WildcardPatterns)
            {
                if (IsWildcardMatch(fileName, pattern))
                    return true;
            }

            return false;
        }

        /// <summary>
        /// 简单的通配符匹配
        /// </summary>
        private static bool IsWildcardMatch(string input, string pattern)
        {
            if (string.IsNullOrEmpty(pattern))
                return false;

            // 简单的通配符匹配：支持 * 和 ?
            var regex = "^" + pattern.Replace("*", ".*").Replace("?", ".") + "$";
            return System.Text.RegularExpressions.Regex.IsMatch(input, regex, System.Text.RegularExpressions.RegexOptions.IgnoreCase);
        }
    }
} 