﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.logging.abstractions\10.0.0-preview.7.25380.108\buildTransitive\net8.0\Microsoft.Extensions.Logging.Abstractions.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.logging.abstractions\10.0.0-preview.7.25380.108\buildTransitive\net8.0\Microsoft.Extensions.Logging.Abstractions.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.options\10.0.0-preview.7.25380.108\buildTransitive\net8.0\Microsoft.Extensions.Options.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.options\10.0.0-preview.7.25380.108\buildTransitive\net8.0\Microsoft.Extensions.Options.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration.binder\10.0.0-preview.7.25380.108\buildTransitive\netstandard2.0\Microsoft.Extensions.Configuration.Binder.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration.binder\10.0.0-preview.7.25380.108\buildTransitive\netstandard2.0\Microsoft.Extensions.Configuration.Binder.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.telemetry.abstractions\9.8.0\buildTransitive\net8.0\Microsoft.Extensions.Telemetry.Abstractions.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.telemetry.abstractions\9.8.0\buildTransitive\net8.0\Microsoft.Extensions.Telemetry.Abstractions.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.http.resilience\9.8.0\buildTransitive\net8.0\Microsoft.Extensions.Http.Resilience.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.http.resilience\9.8.0\buildTransitive\net8.0\Microsoft.Extensions.Http.Resilience.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration.usersecrets\10.0.0-preview.7.25380.108\buildTransitive\net8.0\Microsoft.Extensions.Configuration.UserSecrets.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration.usersecrets\10.0.0-preview.7.25380.108\buildTransitive\net8.0\Microsoft.Extensions.Configuration.UserSecrets.targets')" />
    <Import Project="$(NuGetPackageRoot)aspire.hosting.orchestration.win-x64\9.4.1\buildTransitive\Aspire.Hosting.Orchestration.win-x64.targets" Condition="Exists('$(NuGetPackageRoot)aspire.hosting.orchestration.win-x64\9.4.1\buildTransitive\Aspire.Hosting.Orchestration.win-x64.targets')" />
    <Import Project="$(NuGetPackageRoot)aspire.hosting.apphost\9.4.1\build\Aspire.Hosting.AppHost.targets" Condition="Exists('$(NuGetPackageRoot)aspire.hosting.apphost\9.4.1\build\Aspire.Hosting.AppHost.targets')" />
    <Import Project="$(NuGetPackageRoot)aspire.dashboard.sdk.win-x64\9.4.1\buildTransitive\Aspire.Dashboard.Sdk.win-x64.targets" Condition="Exists('$(NuGetPackageRoot)aspire.dashboard.sdk.win-x64\9.4.1\buildTransitive\Aspire.Dashboard.Sdk.win-x64.targets')" />
  </ImportGroup>
</Project>