﻿using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Primitives;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Routing.Patterns;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.Infrastructure;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Routing.Matching;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;

namespace SSIC.Infrastructure.Startups.Endpoints
{
    /// <summary>
    /// 动态端点数据源，用于管理插件的路由端点
    /// </summary>
    public class DynamicEndpointDataSource : EndpointDataSource
    {
        private readonly List<Endpoint> _endpoints = new List<Endpoint>();
        private readonly object _lock = new object();
        private readonly ILogger<DynamicEndpointDataSource> _logger;

        public DynamicEndpointDataSource() {
            var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
            _logger = loggerFactory.CreateLogger<DynamicEndpointDataSource>();


        }

        public override IReadOnlyList<Endpoint> Endpoints => _endpoints.AsReadOnly();

        public void AddEndpoint(Endpoint endpoint)
        {
            lock (_lock)
            {
                _endpoints.Add(endpoint);
                NotifyChange();
            }
        }

        private CancellationTokenSource _cts = new CancellationTokenSource();

        public override IChangeToken GetChangeToken()
        {
            _logger.LogWarning("GetChangeToken 被调用");
            return new CancellationChangeToken(_cts.Token);
        }

        public void NotifyChange()
        {
            var previous = Interlocked.Exchange(ref _cts, new CancellationTokenSource());
            previous.Cancel(); // 触发路由系统变更感知
        }

        public void Clear()
        {
            lock (_lock)
            {
                _endpoints.Clear();
                NotifyChange();
            }
        }

        public void AddRange(IEnumerable<Endpoint> endpoints)
        {
            lock (_lock)
            {
                _endpoints.AddRange(endpoints);
                NotifyChange();
            }
        }
    }

    /// <summary>
    /// 动态端点管理器，用于管理ASP.NET Core的路由端点
    /// </summary>
    public class DynamicEndpointManager
    {
        private static readonly Lazy<DynamicEndpointManager> _instance = new Lazy<DynamicEndpointManager>(() => new DynamicEndpointManager());
        private readonly DynamicEndpointDataSource _dynamicEndpointDataSource = new DynamicEndpointDataSource();
        private readonly ILogger<DynamicEndpointManager> _logger;
        private readonly HashSet<string> _unloadedModuleNames = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
        private static IServiceProvider _serviceProvider;

        public static DynamicEndpointManager Instance => _instance.Value;

        // 初始化方法，在服务提供程序可用后调用
        public static void Initialize(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        private DynamicEndpointManager()
        {
            var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
            _logger = loggerFactory.CreateLogger<DynamicEndpointManager>();
        }

        /// <summary>
        /// 获取动态端点数据源
        /// </summary>
        public EndpointDataSource GetDynamicEndpoints()
        {
            return _dynamicEndpointDataSource;
        }

        /// <summary>
        /// 记录已加载的模块名称，并从未加载模块列表中移除
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        public void RecordModuleLoaded(string moduleName)
        {
            if (!string.IsNullOrEmpty(moduleName))
            {
                if (_unloadedModuleNames.Remove(moduleName))
                {
                    _logger.LogInformation("从未加载模块列表中移除已加载模块: {ModuleName}", moduleName);
                }
                else
                {
                    _logger.LogDebug("模块已经被标记为已加载: {ModuleName}", moduleName);
                }
            }
        }

        /// <summary>
        /// 记录已卸载的模块名称
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        public void RecordUnloadedModule(string moduleName)
        {
            if (!string.IsNullOrEmpty(moduleName))
            {
                _unloadedModuleNames.Add(moduleName);
                _logger.LogInformation("记录已卸载模块: {ModuleName}", moduleName);

                // 立即触发端点数据源更新
                try
                {
                    _dynamicEndpointDataSource.NotifyChange();
                    _logger.LogInformation("已通知端点数据源更新（模块卸载）");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "通知端点数据源更新失败");
                }
            }
        }

        /// <summary>
        /// 清除已卸载模块记录
        /// </summary>
        public void ClearUnloadedModules()
        {
            _unloadedModuleNames.Clear();
            _logger.LogInformation("已清除所有已卸载模块记录");
        }

        /// <summary>
        /// 检查模块是否已被卸载
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        /// <returns>如果模块已被卸载返回true，否则返回false</returns>
        public bool IsModuleUnloaded(string moduleName)
        {
            return !string.IsNullOrEmpty(moduleName) && _unloadedModuleNames.Contains(moduleName);
        }

        /// <summary>
        /// 重建所有路由端点（使用统一的模块管理服务）
        /// </summary>
        /// <param name="serviceProvider">服务提供程序</param>
        /// <param name="loadedPluginAssemblies">已加载的插件程序集</param>
        public void RebuildEndpoints(IServiceProvider serviceProvider, IEnumerable<Assembly> loadedPluginAssemblies)
        {
            try
            {
                _logger.LogInformation("开始重建路由端点...");

                // 清空现有端点
                _dynamicEndpointDataSource.Clear();
                _logger.LogInformation("[Refresh] DataSource Hash: {Hash}", _dynamicEndpointDataSource.GetHashCode());

                // 使用统一的模块发现和端点管理服务
                var moduleDiscoveryService = serviceProvider.GetService(typeof(SSIC.Infrastructure.Startups.ModuleManager.IModuleDiscoveryService))
                    as SSIC.Infrastructure.Startups.ModuleManager.IModuleDiscoveryService;
                var endpointManagementService = serviceProvider.GetService(typeof(SSIC.Infrastructure.Startups.ModuleManager.IEndpointManagementService))
                    as SSIC.Infrastructure.Startups.ModuleManager.IEndpointManagementService;

                if (moduleDiscoveryService != null && endpointManagementService != null)
                {
                    // 发现模块
                    var modules = moduleDiscoveryService.DiscoverLoadedModules().ToList();

                    // 创建端点
                    var allEndpoints = new List<Endpoint>();
                    foreach (var module in modules)
                    {
                        var moduleEndpoints = endpointManagementService.CreateEndpointsFromModule(module);
                        allEndpoints.AddRange(moduleEndpoints);
                    }

                    // 添加到动态数据源
                    _dynamicEndpointDataSource.AddRange(allEndpoints);

                    _logger.LogInformation("路由重建完成，共 {EndpointCount} 个端点", allEndpoints.Count);
                }
                else
                {
                    _logger.LogWarning("无法获取模块管理服务，跳过端点重建");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重建路由端点时发生错误");
            }
        }




        


        /// <summary>
        /// 刷新路由端点（使用统一的模块管理服务）
        /// </summary>
        /// <param name="services">服务提供程序</param>
        /// <param name="loadedAssemblies">已加载的模块程序集集合（可选）</param>
        public void RefreshEndpoints(IServiceProvider services, IEnumerable<Assembly> loadedAssemblies = null)
        {
            try
            {
                _logger.LogInformation("开始刷新路由端点...");

                // 使用统一的端点管理服务
                var endpointManagementService = services.GetService(typeof(SSIC.Infrastructure.Startups.ModuleManager.IEndpointManagementService))
                    as SSIC.Infrastructure.Startups.ModuleManager.IEndpointManagementService;
                var moduleDiscoveryService = services.GetService(typeof(SSIC.Infrastructure.Startups.ModuleManager.IModuleDiscoveryService))
                    as SSIC.Infrastructure.Startups.ModuleManager.IModuleDiscoveryService;

                if (endpointManagementService != null && moduleDiscoveryService != null)
                {
                    // 发现模块
                    var modules = moduleDiscoveryService.DiscoverLoadedModules().ToList();

                    // 刷新端点
                    var refreshTask = endpointManagementService.RefreshEndpointsAsync(modules);
                    var endpoints = refreshTask.GetAwaiter().GetResult();

                    // 更新端点数据源
                    _dynamicEndpointDataSource.Clear();
                    _dynamicEndpointDataSource.AddRange(endpoints);

                    _logger.LogInformation("已刷新路由端点，共 {Count} 个端点", endpoints.Count());
                }
                else
                {
                    _logger.LogWarning("无法获取端点管理服务，跳过端点刷新");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新路由端点时出错");
            }
        }
        

        

        

        

    }
} 