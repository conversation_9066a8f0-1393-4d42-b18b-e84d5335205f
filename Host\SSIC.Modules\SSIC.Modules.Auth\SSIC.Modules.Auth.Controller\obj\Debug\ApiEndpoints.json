[{"ContainingType": "SSIC.Modules.Auth.Controllers.PermissionController", "Method": "BatchDeletePermissions", "RelativePath": "api/auth/permission/batch", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "permissionIds", "Type": "System.Int32[]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SSIC.Modules.Auth.Controllers.PermissionController", "Method": "GetPermissionTree", "RelativePath": "api/auth/permission/GetPermissionTree", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SSIC.Modules.Auth.Controllers.PermissionController", "Method": "GetPermissionTree", "RelativePath": "api/auth/permission/GetPermissionTree", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SSIC.Infrastructure.BaseController.BaseController", "Method": "Health", "RelativePath": "api/auth/permission/health", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SSIC.Modules.Auth.Controllers.RoleController", "Method": "AssignPermissions", "RelativePath": "api/auth/role/assign-permissions/{roleId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "roleId", "Type": "System.Int32", "IsRequired": false}, {"Name": "permissionIds", "Type": "System.Int32[]", "IsRequired": true}, {"Name": "roleId", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SSIC.Infrastructure.BaseController.BaseController", "Method": "Health", "RelativePath": "api/auth/role/health", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SSIC.Modules.Auth.Controllers.RoleController", "Method": "QueryRoleDetails", "RelativePath": "api/auth/role/QueryRoleDetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SSIC.Modules.Auth.Controllers.RoleController", "Method": "QueryRoleDetails", "RelativePath": "api/auth/role/QueryRoleDetails", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SSIC.Modules.Auth.Controllers.UserController", "Method": "CreateUser", "RelativePath": "api/auth/user/create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "SSIC.Modules.Auth.Controllers.UserController", "Method": "GetUserStatistics", "RelativePath": "api/auth/user/GetUserStatistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SSIC.Modules.Auth.Controllers.UserController", "Method": "GetUserStatistics", "RelativePath": "api/auth/user/GetUserStatistics", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SSIC.Infrastructure.BaseController.BaseController", "Method": "Health", "RelativePath": "api/auth/user/health", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}]