<?xml version="1.0"?>
<doc>
    <assembly>
        <name>SSIC.Infrastructure</name>
    </assembly>
    <members>
        <member name="T:SSIC.Infrastructure.Authentication.BearerSecuritySchemeTransformer">
            <summary>
            Scalar.AspNetCore 添加 JWT 认证
            </summary>
            <param name="authenticationSchemeProvider"></param>
        </member>
        <member name="M:SSIC.Infrastructure.Authentication.BearerSecuritySchemeTransformer.#ctor(Microsoft.AspNetCore.Authentication.IAuthenticationSchemeProvider)">
            <summary>
            Scalar.AspNetCore 添加 JWT 认证
            </summary>
            <param name="authenticationSchemeProvider"></param>
        </member>
        <member name="M:SSIC.Infrastructure.Authentication.JwtGenerator.GenerateSecurityToken(System.Security.Claims.ClaimsIdentity)">
            <summary>
            生成Token
            </summary>
            <param name="claimsIdentity"></param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.Authentication.JwtGenerator.GetPrincipal(System.String)">
            <summary>
            根据token获取对应的数据
            </summary>
            <param name="token"></param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.Authentication.PermissionHandler.HandleRequirementAsync(Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext,SSIC.Infrastructure.Authentication.PermissionRequirement)">
            <summary>
            授权策略
            </summary>
            <param name="context"></param>
            <param name="requirement"></param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseController.BaseController`2.Insert(`0)">
            <summary>
            新增一条数据
            </summary>
            <param name="entities"></param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseController.BaseController`2.InsertList(System.Collections.Generic.List{`0})">
            <summary>
            新增多条数据
            </summary>
            <param name="entities"></param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseController.BaseController`2.Update(`0)">
            <summary>
            修改一条数据
            </summary>
            <param name="entities"></param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseController.BaseController`2.UpdateList(System.Collections.Generic.List{`0})">
            <summary>
            批量修改数据
            </summary>
            <param name="entities"></param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseController.BaseController`2.Delete(`0)">
            <summary>
            删除一条数据
            </summary>
            <param name="entities"></param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseController.BaseController`2.DeleteList(System.Collections.Generic.List{`0})">
            <summary>
            批量删除数据
            </summary>
            <param name="entities"></param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseController.BaseController`2.Check(`0)">
            <summary>
            数据单条审核
            </summary>
            <param name="entities"></param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseController.BaseController`2.CheckList(System.Collections.Generic.List{`0})">
            <summary>
            数据批量审核
            </summary>
            <param name="entities"></param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseController.BaseController`2.PageData(SSIC.Infrastructure.OptionsEntity.PageData)">
            <summary>
            获取分页数据
            </summary>
            <param name="loadData"></param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseProvider.IServiceBase`1.DeleteAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            根据条件表达式删除,返回条数
            </summary>
            <param name="predicate">表达式</param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseProvider.IServiceBase`1.DeleteAsync(`0)">
            <summary>
            根据实体进行删除
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseProvider.IServiceBase`1.InsertAsync(`0)">
            <summary>
            根据实体进行增加数据并返回实体
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseProvider.IServiceBase`1.InsertAsyncList(System.Collections.Generic.List{`0})">
            <summary>
            批量插入数据,并返回实体集合
            </summary>
            <param name="entitys"></param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseProvider.IServiceBase`1.UpdateAsync(`0)">
            <summary>
            根据实体ID进行数据更新
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseProvider.IServiceBase`1.UpdateAsync(System.Collections.Generic.List{`0})">
            <summary>
            根据实体集合进行数据更新并返回影响条数
            </summary>
            <param name="entitys"></param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseProvider.IServiceBase`1.UpdateAsync(System.Linq.Expressions.Expression{System.Func{`0,`0}},System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            动态更新
            </summary>
            <param name="columns">要更新的字段</param>
            <param name="whereExpression">条件表达式</param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseProvider.IServiceBase`1.FindOneColumns(System.Linq.Expressions.Expression{System.Func{`0,`0}},System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{`0,System.Object}},System.Boolean)">
            <summary>
            返回指定字段数组
            </summary>
            <param name="columns">指定字段</param>
            <param name="whereExpression">条件表达式</param>
            <param name="strOrderByFileds">排序字段</param>
            <param name="IsDesc">排序方式true为desc,false为asc</param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseProvider.IServiceBase`1.FindAllList(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{`0,System.Object}},System.Boolean)">
            <summary>
            根据条件返回整表数据
            </summary>
            <param name="whereExpression">查询条件Lambda</param>
            <param name="strOrderByFileds">排序字段</param>
            <param name="IsDesc">排序方式true为desc,false为asc</param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseProvider.IServiceBase`1.FirstAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            查询第一条数据
            </summary>
            <param name="whereExpression"></param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseProvider.IServiceBase`1.QueryPage(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Int32,System.Int32,System.Linq.Expressions.Expression{System.Func{`0,System.Object}},System.Boolean)">
            <summary>
            分页查询数据
            </summary>
            <param name="whereExpression">查询条件Lambda</param>
            <param name="intPageIndex">起始页</param>
            <param name="intPageSize">条数</param>
            <param name="strOrderByFileds">排序字段</param>
            <param name="IsDesc">排序方式true为desc,false为asc</param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseProvider.IServiceBase`1.QueryPageStr(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Int32,System.Int32,System.String,System.Boolean)">
            <summary>
            分页查询数据
            </summary>
            <param name="whereExpression">查询条件Lambda</param>
            <param name="intPageIndex">起始页</param>
            <param name="intPageSize">条数</param>
            <param name="strOrderByFileds">排序字段</param>
            <param name="IsDesc">排序方式true为desc,false为asc</param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseProvider.IServiceBase`1.QueryPageDyn(FreeSql.Internal.Model.DynamicFilterInfo,System.Int32,System.Int32,System.Linq.Expressions.Expression{System.Func{`0,System.Object}},System.Boolean)">
            <summary>
            分页查询数据
            </summary>
            <param name="dynamicFilterInfo">动态过滤条件</param>
            <param name="intPageIndex">起始页</param>
            <param name="intPageSize">条数</param>
            <param name="strOrderByFileds">排序字段</param>
            <param name="IsDesc">排序方式true为desc,false为asc</param>
        </member>
        <member name="M:SSIC.Infrastructure.BaseProvider.IServiceBase`1.QueryPageDynStr(FreeSql.Internal.Model.DynamicFilterInfo,System.Int32,System.Int32,System.String,System.Boolean)">
            <summary>
            分页查询数据
            </summary>
            <param name="dynamicFilterInfo">动态过滤条件</param>
            <param name="intPageIndex">起始页</param>
            <param name="intPageSize">条数</param>
            <param name="strOrderByFileds">排序字段</param>
            <param name="IsDesc">排序方式true为desc,false为asc</param>
        </member>
        <member name="F:SSIC.Infrastructure.BaseProvider.ServiceBase`1.DisGlobalFilter">
            <summary>
            是否禁用全局过滤器
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.BaseProvider.ServiceBase`1.DeleteAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            根据条件表达式删除,返回条数
            </summary>
            <param name="predicate">表达式</param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseProvider.ServiceBase`1.DeleteAsync(`0)">
            <summary>
            根据实体进行删除
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseProvider.ServiceBase`1.FindAllList(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{`0,System.Object}},System.Boolean)">
            <summary>
            根据条件返回整表数据
            </summary>
            <param name="whereExpression">查询条件Lambda</param>
            <param name="strOrderByFileds">排序字段</param>
            <param name="IsDesc">排序方式true为desc,false为asc</param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseProvider.ServiceBase`1.FirstAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            查询第一条数据
            </summary>
            <param name="whereExpression"></param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseProvider.ServiceBase`1.FindOneColumns(System.Linq.Expressions.Expression{System.Func{`0,`0}},System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{`0,System.Object}},System.Boolean)">
            <summary>
            返回指定字段数组
            </summary>
            <param name="columns">指定字段</param>
            <param name="whereExpression">条件表达式</param>
            <param name="strOrderByFileds">排序字段</param>
            <param name="IsDesc">排序方式true为desc,false为asc</param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseProvider.ServiceBase`1.InsertAsync(`0)">
            <summary>
            根据实体进行增加数据并返回实体
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseProvider.ServiceBase`1.InsertAsyncList(System.Collections.Generic.List{`0})">
            <summary>
            批量插入数据,并返回实体集合
            </summary>
            <param name="entitys"></param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseProvider.ServiceBase`1.QueryPage(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Int32,System.Int32,System.Linq.Expressions.Expression{System.Func{`0,System.Object}},System.Boolean)">
            <summary>
            分页查询数据
            </summary>
            <param name="whereExpression">查询条件Lambda</param>
            <param name="intPageIndex">起始页</param>
            <param name="intPageSize">条数</param>
            <param name="strOrderByFileds">排序字段</param>
            <param name="IsDesc">排序方式true为desc,false为asc</param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseProvider.ServiceBase`1.QueryPageStr(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Int32,System.Int32,System.String,System.Boolean)">
            <summary>
            分页查询数据
            </summary>
            <param name="whereExpression">查询条件Lambda</param>
            <param name="intPageIndex">起始页</param>
            <param name="intPageSize">条数</param>
            <param name="strOrderByFileds">排序字段</param>
            <param name="IsDesc">排序方式true为desc,false为asc</param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseProvider.ServiceBase`1.QueryPageDyn(FreeSql.Internal.Model.DynamicFilterInfo,System.Int32,System.Int32,System.Linq.Expressions.Expression{System.Func{`0,System.Object}},System.Boolean)">
            <summary>
            分页查询数据
            </summary>
            <param name="dynamicFilterInfo">动态过滤条件</param>
            <param name="intPageIndex">起始页</param>
            <param name="intPageSize">条数</param>
            <param name="strOrderByFileds">排序字段</param>
            <param name="IsDesc">排序方式true为desc,false为asc</param>
        </member>
        <member name="M:SSIC.Infrastructure.BaseProvider.ServiceBase`1.QueryPageDynStr(FreeSql.Internal.Model.DynamicFilterInfo,System.Int32,System.Int32,System.String,System.Boolean)">
            <summary>
            分页查询数据
            </summary>
            <param name="dynamicFilterInfo">动态过滤条件</param>
            <param name="intPageIndex">起始页</param>
            <param name="intPageSize">条数</param>
            <param name="strOrderByFileds">排序字段</param>
            <param name="IsDesc">排序方式true为desc,false为asc</param>
        </member>
        <member name="M:SSIC.Infrastructure.BaseProvider.ServiceBase`1.UpdateAsync(`0)">
            <summary>
            根据实体ID进行数据更新
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseProvider.ServiceBase`1.UpdateAsync(System.Collections.Generic.List{`0})">
            <summary>
            根据实体集合进行数据更新并返回影响条数
            </summary>
            <param name="entitys"></param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.BaseProvider.ServiceBase`1.UpdateAsync(System.Linq.Expressions.Expression{System.Func{`0,`0}},System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            动态更新
            </summary>
            <param name="columns">要更新的字段</param>
            <param name="whereExpression">条件表达式</param>
            <returns></returns>
        </member>
        <member name="T:SSIC.Infrastructure.ConfigurableOptions.IConfigurableOptions">
            <summary>
            配置属性接口（通过该接口进行适配）
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.ConfigurableOptions.Realization.ConfigurableOptions.AddAllConfigurableOptions(Microsoft.Extensions.DependencyInjection.IServiceCollection,Microsoft.Extensions.Configuration.IConfiguration@)">
            <summary>
            自动检索并注册所有实现 IConfigurableOptions 接口的配置选项，并将配置对象添加到服务集合中
            </summary>
            <param name="services">服务集合</param>
        </member>
        <member name="M:SSIC.Infrastructure.ConfigurableOptions.Realization.ConfigurableOptions.MergeJsonObjects(Newtonsoft.Json.Linq.JObject,Newtonsoft.Json.Linq.JObject)">
            <summary>
            合并两个 JSON 对象，同一属性进行覆盖，不同属性进行合并
            </summary>
            <param name="target">目标 JSON 对象</param>
            <param name="source">源 JSON 对象</param>
        </member>
        <member name="M:SSIC.Infrastructure.ConfigurableOptions.Realization.ConfigurableOptions.GetAllOptionsTypes">
            <summary>
            获取所有实现 IConfigurableOptions 接口的类型
            </summary>
            <returns>类型集合</returns>
        </member>
        <member name="M:SSIC.Infrastructure.ConfigurableOptions.Realization.ConfigurableOptions.GetEntityName``1">
            <summary>
            获取实体名称
            </summary>
            <typeparam name="T">类型参数</typeparam>
            <returns>实体名称</returns>
        </member>
        <member name="M:SSIC.Infrastructure.ConfigurableOptions.Realization.ConfigurableOptions.GetEntityName(System.Type)">
            <summary>
            获取实体名称
            </summary>
            <param name="type">类型</param>
            <returns>实体名称</returns>
        </member>
        <member name="M:SSIC.Infrastructure.ConfigurableOptions.Realization.ConfigurableOptions.GetConfigFilePath(System.String)">
            <summary>
            检索目录下的配置文件路径
            </summary>
            <param name="entityName">实体名称</param>
            <returns>配置文件路径</returns>
        </member>
        <member name="M:SSIC.Infrastructure.ConfigurableOptions.Realization.ConfigurableOptions.GetOptions``1(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            从服务集合中获取配置选项的实例
            </summary>
            <typeparam name="T">配置选项的类型</typeparam>
            <param name="services">服务集合</param>
            <returns>配置选项实例</returns>
        </member>
        <member name="M:SSIC.Infrastructure.ConfigurableOptions.Realization.ConfigurableOptions.GetOptions``1(System.IServiceProvider)">
            <summary>
            从服务提供者中获取配置选项的实例
            </summary>
            <typeparam name="T">配置选项的类型</typeparam>
            <param name="provider">服务提供者</param>
            <returns>配置选项实例</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Dapr.DaprClientProvider.InvokeServiceAsync``2(System.String,System.String,``0)">
            <summary>
            调用指定服务的指定方法
            </summary>
            <typeparam name="TRequest">请求数据的类型</typeparam>
            <typeparam name="TResponse">响应数据的类型</typeparam>
            <param name="appId">目标服务的 App ID</param>
            <param name="methodName">要调用的方法名称</param>
            <param name="data">请求数据</param>
            <returns>目标服务返回的数据</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Dapr.DaprClientProvider.PublishEventAsync``1(System.String,System.String,``0)">
            <summary>
            发布事件(向指定的 pub/sub 组件发布事件)
            </summary>
            <typeparam name="T"></typeparam>
            <param name="pubsubName">Pub/Sub 组件的名称</param>
            <param name="topicName">要发布的主题名称</param>
            <param name="data">要发布的事件数据</param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.Dapr.DaprClientProvider.GetStateAsync``1(System.String,System.String)">
            <summary>
            获取状态(从指定的状态存储中获取状态)
            </summary>
            <typeparam name="T"></typeparam>
            <param name="storeName">状态存储的名称</param>
            <param name="key">要获取的状态的键</param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.Dapr.DaprClientProvider.SaveStateAsync``1(System.String,System.String,``0)">
            <summary>
            保存状态(将状态保存到指定的状态存储中)
            </summary>
            <typeparam name="T"></typeparam>
            <param name="storeName">状态存储的名称</param>
            <param name="key">要保存的状态的键</param>
            <param name="value">要保存的状态数据</param>
            <returns></returns>
        </member>
        <member name="T:SSIC.Infrastructure.DependencyInjection.Extensions.ServiceCollectionExtensions">
            <summary>
            作用域注册服务
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.DependencyInjection.Interface.IScoped">
            <summary>
            作用域接口标记
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.DependencyInjection.Interface.ISingleton">
            <summary>
            单例接口标记
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.DependencyInjection.Interface.ITransient">
            <summary>
            瞬时接口标记
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.Composition.ApproveBasic.check">
            <summary>
            审核(0.提交,1审核中,2.反审核,3.已审核,4.结案，5手动结案)
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.Composition.ApproveBasic.checkid">
            <summary>
            审核人
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.Composition.ApproveBasic.checktime">
            <summary>
            审核时间
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.Composition.DocBase.docno">
            <summary>
            单据号
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.Composition.DocBase.doctype">
            <summary>
            单据类型
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.Composition.DocBase.docdate">
            <summary>
            单据日期
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.Composition.DocBase.pid">
            <summary>
            岗位id
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.Composition.DocBase.staffid">
            <summary>
            员工ID
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.Composition.DocBaseApprove.docno">
            <summary>
            单据号
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.Composition.DocBaseApprove.doctype">
            <summary>
            单据类型
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.Composition.DocBaseApprove.docdate">
            <summary>
            单据日期
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.Composition.DocBaseApprove.pid">
            <summary>
            岗位id
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.Composition.DocBaseApprove.staffid">
            <summary>
            员工ID
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.Composition.DocBaseApprove.check">
            <summary>
            审核(0.提交,1审核中,2.反审核,3.已审核,4.结案，5手动结案)
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.Composition.DocBaseApprove.checkid">
            <summary>
            审核人
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.Composition.DocBaseApprove.checktime">
            <summary>
            审核时间
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Entity.EntityBase.Composition.DocRateBasic">
             <summary>
            单据表常规字段+汇率，税率
             </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.Composition.DocRateBasic.docno">
            <summary>
            单据号
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.Composition.DocRateBasic.doctype">
            <summary>
            单据类型
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.Composition.DocRateBasic.docdate">
            <summary>
            单据日期
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.Composition.DocRateBasic.pid">
            <summary>
            岗位id
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.Composition.DocRateBasic.staffid">
            <summary>
            员工ID
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.Composition.DocRateBasic.deliverydate">
            <summary>
            订单交期
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.Composition.DocRateBasic.startdate">
            <summary>
            下单日期
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.Composition.DocRateBasic.exchangerate">
            <summary>
            汇率
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.Composition.DocRateBasic.taxcode">
            <summary>
            税别代号
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.Composition.DocRateBasic.taxtype">
            <summary>
            税别类型
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.Composition.DocRateBasic.taxrate">
            <summary>
            税率
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Entity.EntityBase.Composition.RateBasic">
            <summary>
            汇率，税率
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.Composition.RateBasic.currencyid">
            <summary>
            币别
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.Composition.RateBasic.exchangerate">
            <summary>
            汇率
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.Composition.RateBasic.taxcode">
            <summary>
            税别代号
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.Composition.RateBasic.taxtype">
            <summary>
            税别类型
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.Composition.RateBasic.taxrate">
            <summary>
            税率
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.EntityApprove.check">
            <summary>
            审核(0.提交,1审核中,2.反审核,3.已审核,4.结案，5手动结案)
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.EntityApprove.checkid">
            <summary>
            审核人
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.EntityApprove.checktime">
            <summary>
            审核时间
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Entity.EntityBase.EntityDoc">
            <summary>
            单据表结构常规操作字段
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.EntityDoc.docno">
            <summary>
            单据号
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.EntityDoc.doctype">
            <summary>
            单据类型
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.IEntityApprove.check">
            <summary>
            审核(0.提交,1审核中,2.反审核,3.已审核,4.结案，5手动结案)
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.IEntityApprove.checkid">
            <summary>
            审核人
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.IEntityApprove.checktime">
            <summary>
            审核时间
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Entity.EntityBase.IEntityDoc">
            <summary>
            单据表结构常规操作字段
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.IEntityDoc.docno">
            <summary>
            单据号
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBase.IEntityDoc.doctype">
            <summary>
            单据类型
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Entity.EntityBasic">
            <summary>
            表结构常规操作字段
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBasic.id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBasic.creator">
            <summary>
            创建人
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBasic.createid">
            <summary>
            创建人ID
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBasic.createtime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBasic.modifier">
            <summary>
            修改人
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBasic.modifyid">
            <summary>
            修改人ID
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBasic.modifytime">
            <summary>
            修改时间
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBasic.deleted">
            <summary>
            删除人
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBasic.deleteid">
            <summary>
            删除人ID
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBasic.deletetime">
            <summary>
            删除时间
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBasic.isdelete">
            <summary>
            是否删除
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBasic.islock">
            <summary>
            是否禁用
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBasic.orgid">
            <summary>
            组织ID
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBasic.tenantid">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBasic.remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBasic.desc">
            <summary>
            描述
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBasic.ordersort">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBasic.code">
            <summary>
            编码管理
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBasic.name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityBasic.childs">
            <summary>
            细表子集
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Entity.EntityRate">
            <summary>
            汇率,税率字段
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityRate.exchangerate">
            <summary>
            汇率
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityRate.taxcode">
            <summary>
            税别代号
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityRate.taxtype">
            <summary>
            税别类型
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.EntityRate.taxrate">
            <summary>
            税率
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Entity.IEntityRate">
            <summary>
            汇率,税率字段
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.IEntityRate.exchangerate">
            <summary>
            汇率
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.IEntityRate.taxcode">
            <summary>
            税别代号
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.IEntityRate.taxtype">
            <summary>
            税别类型
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.IEntityRate.taxrate">
            <summary>
            税率
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Entity.TenantEntity.TenantInfo">
            <summary>
            租户表
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.TenantEntity.TenantInfo.tenantname">
            <summary>
            租户名称
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.TenantEntity.TenantInfo.host">
            <summary>
            租户地址
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.TenantEntity.TenantInfo.dataservice">
            <summary>
            数据库服务器地址
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.TenantEntity.TenantInfo.dataport">
            <summary>
            端口号
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.TenantEntity.TenantInfo.datatype">
            <summary>
            数据库类型
            SqlServer = 1,
            PostgreSQL = 2,
            Oracle = 3,
            Sqlite = 4,
            OdbcOracle = 5,
            OdbcSqlServer = 6,
            OdbcMySql = 7,
            OdbcPostgreSQL = 8,
            Odbc = 9,
            OdbcDamning = 10,
            MsAccess = 11,
            Dameng = 12,
            OdbcKingbaseES = 13,
            ShenTong = 14,
            KingbaseES = 15,
            Firebird = 16
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.TenantEntity.TenantInfo.datauid">
            <summary>
            数据库登陆用户ID
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.TenantEntity.TenantInfo.datapwd">
            <summary>
            数据库登陆密码
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.TenantEntity.TenantInfo.dataname">
            <summary>
            数据库名称
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.TenantEntity.TenantInfo.ismaster">
            <summary>
            是否默认主库
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.TenantEntity.TenantInfo.tenanttype">
            <summary>
            租户模式1.是同库,2是分库
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Entity.TenantEntity.TenantInfo.orgmodel">
            <summary>
            是否启用组织权限
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.ActionPermissionOption.None">
            <summary>
            没有任何权限
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.ActionPermissionOption.Add">
            <summary>
            新增
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.ActionPermissionOption.Delete">
            <summary>
            删除
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.ActionPermissionOption.Update">
            <summary>
            修改
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.ActionPermissionOption.View">
            <summary>
            查看
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.ActionPermissionOption.Search">
            <summary>
            查找
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.ActionPermissionOption.Export">
            <summary>
            导出
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.ActionPermissionOption.Copy">
            <summary>
            复制
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.ActionPermissionOption.Check">
            <summary>
            审核
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.ActionPermissionOption.Upload">
            <summary>
            上传文件
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.ActionPermissionOption.Import">
            <summary>
            导入表数据Excel
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Enums.CheckOption">
            <summary>
            审核配置
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.CheckOption.NotCheck">
            <summary>
            未审核
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.CheckOption.Check">
            <summary>
            已审核
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.CheckOption.ReturnCheck">
            <summary>
            反审核
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Enums.OutCode">
            <summary>
            状态码
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.OutCode.Success">
            <summary>
            返回成功
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.OutCode.Fail">
            <summary>
            返回失败
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.OutCode.NoAuthority">
            <summary>
            未授权
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.OutCode.Unauthorized">
            <summary>
            权限不足
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.OutCode.NoHttp">
            <summary>
            无效地址
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.OutCode.ServerError">
            <summary>
            服务器错误
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Enums.TagType">
            <summary>
            菜单标签类型
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.TagType.primary">
            <summary>
            正常
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.TagType.success">
            <summary>
            成功
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.TagType.warn">
            <summary>
            等待
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.TagType.error">
            <summary>
            错误
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Enums.TenatType">
            <summary>
            租户数据库使用类型
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.TenatType.IdenticalSql">
            <summary>
            同库共用
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Enums.TenatType.DifferentSql">
            <summary>
            不同库
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Extensions.ApplicationBuilderExtensions">
            <summary>
            应用程序构建器扩展方法
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Extensions.ApplicationBuilderExtensions.UseSSICInfrastructure(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            使用SSIC基础设施中间件
            </summary>
            <param name="app">应用程序构建器</param>
            <returns>应用程序构建器</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Extensions.ApplicationBuilderExtensions.UseDynamicRouting(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            使用动态路由
            </summary>
            <param name="app">应用程序构建器</param>
            <returns>应用程序构建器</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Extensions.ApplicationBuilderExtensions.UseModuleHotReload(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            使用模块热加载（仅在开发环境）
            </summary>
            <param name="app">应用程序构建器</param>
            <returns>应用程序构建器</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Extensions.ApplicationBuilderExtensions.UseEndpointRefresh(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            使用端点刷新中间件
            </summary>
            <param name="app">应用程序构建器</param>
            <returns>应用程序构建器</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Extensions.ApplicationBuilderExtensions.InitializeAndRefreshModules(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            初始化并刷新所有模块端点
            </summary>
            <param name="app">应用程序构建器</param>
            <returns>应用程序构建器</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Extensions.ApplicationBuilderExtensions.InitializeDynamicEndpointManager(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            初始化动态端点管理器
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Extensions.ServiceCollectionExtensions">
            <summary>
            服务集合扩展方法
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Extensions.ServiceCollectionExtensions.AddSSICInfrastructure(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            添加SSIC基础设施服务
            </summary>
            <param name="services">服务集合</param>
            <returns>服务集合</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Extensions.ServiceCollectionExtensions.AddModuleManagement(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            添加模块管理服务
            </summary>
            <param name="services">服务集合</param>
            <returns>服务集合</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Extensions.ServiceCollectionExtensions.AddHotReloadServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            添加热加载服务
            </summary>
            <param name="services">服务集合</param>
            <returns>服务集合</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Extensions.ServiceCollectionExtensions.AddDynamicRouting(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            添加动态路由服务
            </summary>
            <param name="services">服务集合</param>
            <returns>服务集合</returns>
        </member>
        <member name="T:SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware">
            <summary>
            动态路由中间件
            支持模块热加载和路由动态更新
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware.InitializeRoutingAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            初始化路由系统
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware.ShouldRefreshRoutingAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            检查是否需要刷新路由
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware.RefreshRoutingAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            刷新路由系统
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware.TryMatchDynamicRouteAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            尝试匹配动态路由
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Middleware.DynamicRoutingMiddlewareExtensions">
            <summary>
            动态路由中间件扩展方法
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Middleware.DynamicRoutingMiddlewareExtensions.UseDynamicRouting(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            添加动态路由中间件
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware">
            <summary>
            模块热加载监控中间件
            监控模块文件变化并自动重新加载
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware.InitializeHotReloadAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            初始化热加载监控
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware.GetModuleDirectories">
            <summary>
            获取模块目录列表
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware.SetupFileWatcher(System.String,System.IServiceProvider)">
            <summary>
            设置文件监控器
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware.OnModuleFileChanged(System.IO.FileSystemEventArgs,System.IServiceProvider)">
            <summary>
            处理模块文件变化事件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware.IsModuleFile(System.String)">
            <summary>
            检查是否是模块文件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware.ReloadModuleAsync(System.String,System.IServiceProvider)">
            <summary>
            重新加载模块
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware.Dispose">
            <summary>
            释放资源
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Middleware.ModuleHotReloadMiddlewareExtensions">
            <summary>
            模块热加载中间件扩展方法
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Middleware.ModuleHotReloadMiddlewareExtensions.UseModuleHotReload(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            添加模块热加载中间件
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer">
            <summary>
            动态模块OpenAPI文档转换器
            用于将动态加载的模块控制器添加到OpenAPI文档中
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer.GetMethodRouteInfo(System.Reflection.MethodInfo,System.String)">
            <summary>
            根据HTTP特性确定方法的路由信息（与RoutePrefixConvention.GetActionRouteInfo相同逻辑）
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer.ExtractHttpMethod(System.String)">
            <summary>
            从特性名称提取HTTP方法（与RoutePrefixConvention中的ExtractHttpMethod相同）
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer.ActionRouteInfo">
            <summary>
            路由信息类（与RoutePrefixConvention中的ActionRouteInfo相同）
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer.ExtractModuleName(System.String)">
            <summary>
            从程序集名称中提取模块名称
            支持新的项目结构：SSIC.Modules.Auth.Controller -> Auth
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer">
            <summary>
            热加载OpenAPI文档转换器，专门处理动态加载的模块控制器
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer.GetModuleName(Microsoft.AspNetCore.Mvc.Controllers.ControllerActionDescriptor)">
            <summary>
            从控制器动作描述符中提取模块名称
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer.GenerateOperationId(Microsoft.AspNetCore.Mvc.Controllers.ControllerActionDescriptor)">
            <summary>
            生成操作ID
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.OpenApi.IHotReloadOpenApiRefreshService">
            <summary>
            热加载OpenAPI刷新服务接口
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.OpenApi.IHotReloadOpenApiRefreshService.RefreshAsync">
            <summary>
            刷新OpenAPI文档
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.OpenApi.IHotReloadOpenApiRefreshService.GetLoadedModulesAsync">
            <summary>
            获取当前加载的模块信息
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.OpenApi.ModuleInfo">
            <summary>
            模块信息
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.OpenApi.HotReloadOpenApiRefreshService">
            <summary>
            热加载OpenAPI刷新服务实现
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.OpenApi.HotReloadOpenApiRefreshService.RefreshAsync">
            <summary>
            刷新OpenAPI文档（简化版本，仅用于内部调用）
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.OpenApi.HotReloadOpenApiRefreshService.GetLoadedModulesAsync">
            <summary>
            获取当前加载的模块信息（使用统一的模块发现服务）
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.OpenApi.OpenApiConfigurationExtensions">
            <summary>
            OpenApi 统一配置扩展
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.OpenApi.OpenApiConfigurationExtensions.AddSSICOpenApi(Microsoft.Extensions.DependencyInjection.IServiceCollection,Microsoft.AspNetCore.Hosting.IWebHostEnvironment)">
            <summary>
            添加 SSIC OpenApi 配置
            </summary>
            <param name="services">服务集合</param>
            <param name="environment">环境信息</param>
            <returns>服务集合</returns>
        </member>
        <member name="M:SSIC.Infrastructure.OpenApi.OpenApiConfigurationExtensions.ExtractModuleName(System.String)">
            <summary>
            从程序集名称中提取模块名称
            支持新的项目结构：SSIC.Modules.Auth.Controller -> Auth
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.OpenApi.OpenApiConfigurationExtensions.GenerateCustomOperationId(Microsoft.AspNetCore.Mvc.Controllers.ControllerActionDescriptor)">
            <summary>
            生成自定义操作ID：方法名称+路由模板
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.OpenApi.OpenApiConfigurationExtensions.ConfigureSecurityScheme(Microsoft.OpenApi.Models.OpenApiDocument)">
            <summary>
            配置安全方案
            </summary>
            <param name="document">OpenApi 文档</param>
        </member>
        <member name="T:SSIC.Infrastructure.OptionsEntity.CorsOptions">
            <summary>
            跨域配置设置
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.CorsOptions.PolicyName">
            <summary>
            策略名称
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.CorsOptions.WithOrigins">
            <summary>
            允许来源域名，没有配置则允许所有来源
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.CorsOptions.IsUse">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.OptionsEntity.DbInfoOptions">
            <summary>
            数据库配置类
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.DbInfoOptions.dataservice">
            <summary>
            数据库地址
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.DbInfoOptions.dataport">
            <summary>
            数据库端口号
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.DbInfoOptions.datatype">
            <summary>
            数据库类型
            SqlServer = 1,
            PostgreSQL = 2,
            Oracle = 3,
            Sqlite = 4,
            OdbcOracle = 5,
            OdbcSqlServer = 6,
            OdbcMySql = 7,
            OdbcPostgreSQL = 8,
            Odbc = 9,
            OdbcDamning = 10,
            MsAccess = 11,
            Dameng = 12,
            OdbcKingbaseES = 13,
            ShenTong = 14,
            KingbaseES = 15,
            Firebird = 16
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.DbInfoOptions.dataname">
            <summary>
            数据库名称
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.DbInfoOptions.datauid">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.DbInfoOptions.datapwd">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.DbInfoOptions.tenantid">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.DbInfoOptions.ComposeUrl">
            <summary>
            数据库地址
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.DbInfoOptions.tenantLists">
             <summary>
            租户详细
             </summary>
        </member>
        <member name="T:SSIC.Infrastructure.OptionsEntity.ModulePathOptions">
            <summary>
            模块路径配置选项
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.ModulePathOptions.Enabled">
            <summary>
            是否启用自定义模块路径功能
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.ModulePathOptions.DevelopmentBasePath">
            <summary>
            开发环境基础路径
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.ModulePathOptions.ProductionBasePath">
            <summary>
            生产环境基础路径
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.ModulePathOptions.ModulePath">
            <summary>
            模块相对路径
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.ModulePathOptions.ScanSubFolders">
            <summary>
            是否扫描子文件夹
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.ModulePathOptions.ModuleDirectoryPrefix">
            <summary>
            模块目录前缀
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.ModulePathOptions.ScanMode">
            <summary>
            扫描模式（0=ByPrefix, 1=ByDirectory, 2=ByPattern, 3=ByWildcardPattern, 4=AllDlls）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.ModulePathOptions.WildcardPatterns">
            <summary>
            通配符模式列表
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.ModulePathOptions.SearchOption">
            <summary>
            搜索选项
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.OptionsEntity.ModulePathOptions.GetBasePath(System.Boolean)">
            <summary>
            获取当前环境的基础路径
            </summary>
            <param name="isDevelopment">是否为开发环境</param>
            <returns>基础路径</returns>
        </member>
        <member name="M:SSIC.Infrastructure.OptionsEntity.ModulePathOptions.GetModuleDirectory(System.Boolean)">
            <summary>
            获取完整的模块目录路径
            </summary>
            <param name="isDevelopment">是否为开发环境</param>
            <returns>模块目录路径</returns>
        </member>
        <member name="M:SSIC.Infrastructure.OptionsEntity.ModulePathOptions.GetScanMode">
            <summary>
            获取扫描模式枚举值
            </summary>
            <returns>扫描模式枚举</returns>
        </member>
        <member name="M:SSIC.Infrastructure.OptionsEntity.ModulePathOptions.GetSearchOption">
            <summary>
            获取搜索选项枚举值
            </summary>
            <returns>搜索选项枚举</returns>
        </member>
        <member name="M:SSIC.Infrastructure.OptionsEntity.ModulePathOptions.GetModuleSubFolders(System.Boolean)">
            <summary>
            获取模块扫描路径列表
            </summary>
            <param name="isDevelopment">是否为开发环境</param>
            <returns>模块扫描路径列表</returns>
        </member>
        <member name="T:SSIC.Infrastructure.OptionsEntity.PageData">
            <summary>
            分页数据
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.PageData.Page">
            <summary>
            页码
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.PageData.Rows">
            <summary>
            行数
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.PageData.Total">
            <summary>
            总条数
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.PageData.TableName">
            <summary>
            表格名称
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.PageData.Sort">
            <summary>
            排序字段
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.PageData.Order">
            <summary>
            排序方式
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.PageData.Wheres">
            <summary>
            条件
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.PageData.Export">
            <summary>
            是否导出excel
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.PageData.Value">
            <summary>
            自定义条件
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.OptionsEntity.SerilogOptions">
            <summary>
            Serilog 配置选项类，用于映射配置文件（如 appsettings.json）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.SerilogOptions.Using">
            <summary>
            Serilog 配置根对象
            </summary>
            <summary>
            使用的 Serilog 插件列表（如 Serilog.Sinks.Console 等）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.SerilogOptions.WriteTo">
            <summary>
            日志写入配置列表（支持写入多个目的地，如 Console、File、Seq 等）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.SerilogOptions.Enrich">
            <summary>
            日志增强器配置（如 FromLogContext，添加更多上下文信息）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.SerilogOptions.Properties">
            <summary>
            附加到日志中的通用属性（如应用名、环境）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.SerilogOptions.MinimumLevel">
            <summary>
            最小日志级别配置（如 Debug、Information、Warning 等）
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.OptionsEntity.Properties">
            <summary>
            日志附加属性，如应用名和环境
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Properties.Application">
            <summary>
            应用程序名称
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Properties.Environment">
            <summary>
            当前运行环境（如 Development、Production）
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.OptionsEntity.Minimumlevel">
            <summary>
            最小日志级别配置
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Minimumlevel.Default">
            <summary>
            默认的最小日志级别
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.OptionsEntity.Writeto">
            <summary>
            日志写入配置
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Writeto.Name">
            <summary>
            Sink 名称（例如 Console、File、Seq 等）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Writeto.Args">
            <summary>
            Sink 参数配置
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.OptionsEntity.Args">
            <summary>
            日志写入参数
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Args.serverUrl">
            <summary>
            日志服务器地址（用于 Seq）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Args.apiKey">
            <summary>
            访问日志服务器所需的 API 密钥（可选）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Args.restrictedToMinimumLevel">
            <summary>
            写入日志的最小级别
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Args.batchPostingLimit">
            <summary>
            批量发送日志的最大条数
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Args.period">
            <summary>
            批量发送的时间间隔（格式如 "00:00:02"）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Args.outputTemplate">
            <summary>
            日志输出模板（控制日志内容格式）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Args.theme">
            <summary>
            控制台日志主题（如 SystemConsoleTheme.Literate）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Args.path">
            <summary>
            文件日志保存路径
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Args.rollingInterval">
            <summary>
            文件滚动间隔（如 "Day"、"Hour"）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Args.formatter">
            <summary>
            自定义日志格式器（如 CompactJsonFormatter）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Args.retainedFileCountLimit">
            <summary>
            保留的最大日志文件数量
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Args.fileSizeLimitBytes">
            <summary>
            单个日志文件的最大大小（字节数）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.Args.rollOnFileSizeLimit">
            <summary>
            超过最大文件大小时是否滚动生成新文件
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.OptionsEntity.ServerOptions">
            <summary>
            动态服务配置  
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.ServerOptions.name">
            <summary>
            Aspire资源名称
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.ServerOptions.slnpath">
            <summary>
            项目名称
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.ServerOptions.slnname">
            <summary>
            项目名称
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.ServerOptions.type">
            <summary>
            项目类型
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.ServerOptions.port">
            <summary>
            port端口
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.ServerOptions.grpcprot">
            <summary>
            gRPC端口
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.OptionsEntity.TenantChange">
            <summary>
            租户设置
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.OptionsEntity.TenantChange.AsyncLocalTenantId">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.OptionsEntity.TenantChange.AsyncLocalTenanttype">
            <summary>
            租户类型1.同库;2.分库
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.OptionsEntity.TenantList">
            <summary>
            租户设置
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.TenantList.Tenantid">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.TenantList.Tenattype">
            <summary>
            租户类型
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.TenantList.Host">
            <summary>
            域名地址
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.OptionsEntity.ZipkinOptions.Endpoint">
             <summary>
            端点
             </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Orm.FreeSqlContexts.SqlCommandLog(IFreeSql)">
            <summary>
            监视数据库语句
            </summary>
            <param name="freeSql"></param>
        </member>
        <member name="M:SSIC.Infrastructure.Orm.FreeSqlContexts.GetTypesByTableAttribute">
            <summary>
            扫描 IEntity类所在程序集，反射得到类上有特性标签为TableAttribute 的所有类，该方法需在实体类上指定了 [Table(Name = "xxx")]特性标签
            </summary>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.Orm.MultiFreeSqlExtensions.Change``1(IFreeSql,``0)">
            <summary>
            切换数据库，填入连接ID
            </summary>
            <typeparam name="TDBKey"></typeparam>
            <param name="fsql"></param>
            <param name="dbkey"></param>
            <returns></returns>
        </member>
        <member name="M:SSIC.Infrastructure.Orm.MultiFreeSqlExtensions.Register``1(IFreeSql,``0,System.Func{IFreeSql})">
            <summary>
            进行连接注册
            </summary>
            <typeparam name="TDBKey"></typeparam>
            <param name="fsql"></param>
            <param name="dbkey"></param>
            <param name="create"></param>
            <returns></returns>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.StartupAttribute">
            <summary>
            项目启动项特性
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Startups.StartupAttribute.Sort">
            <summary>
            排序，执行顺序由小到大
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointDataSource">
            <summary>
            动态端点数据源，用于管理插件的路由端点
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointManager">
            <summary>
            动态端点管理器，用于管理ASP.NET Core的路由端点
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointManager.GetDynamicEndpoints">
            <summary>
            获取动态端点数据源
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointManager.RecordModuleLoaded(System.String)">
            <summary>
            记录已加载的模块名称，并从未加载模块列表中移除
            </summary>
            <param name="moduleName">模块名称</param>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointManager.RecordUnloadedModule(System.String)">
            <summary>
            记录已卸载的模块名称
            </summary>
            <param name="moduleName">模块名称</param>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointManager.ClearUnloadedModules">
            <summary>
            清除已卸载模块记录
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointManager.IsModuleUnloaded(System.String)">
            <summary>
            检查模块是否已被卸载
            </summary>
            <param name="moduleName">模块名称</param>
            <returns>如果模块已被卸载返回true，否则返回false</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointManager.RebuildEndpoints(System.IServiceProvider,System.Collections.Generic.IEnumerable{System.Reflection.Assembly})">
            <summary>
            重建所有路由端点（使用统一的模块管理服务）
            </summary>
            <param name="serviceProvider">服务提供程序</param>
            <param name="loadedPluginAssemblies">已加载的插件程序集</param>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointManager.RefreshEndpoints(System.IServiceProvider,System.Collections.Generic.IEnumerable{System.Reflection.Assembly})">
            <summary>
            刷新路由端点（使用统一的模块管理服务）
            </summary>
            <param name="services">服务提供程序</param>
            <param name="loadedAssemblies">已加载的模块程序集集合（可选）</param>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointSource">
            <summary>
            动态端点源实现，从JSON文件读取端点定义并监控文件变化
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointSource.#ctor(Microsoft.Extensions.FileProviders.IFileProvider,System.String)">
            <summary>
            构造函数
            </summary>
            <param name="fileProvider">文件提供程序</param>
            <param name="endpointsPath">端点定义JSON文件所在目录</param>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointSource.GetEndpointsAsync">
            <summary>
            异步获取所有端点
            </summary>
            <returns>端点集合</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointSource.Watch(System.Action{System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Http.Endpoint}})">
            <summary>
            监控端点定义变化
            </summary>
            <param name="callback">端点变化回调方法</param>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.Endpoints.EndpointDefinition">
            <summary>
            端点定义类
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Startups.Endpoints.EndpointDefinition.RoutePattern">
            <summary>
            路由模式
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Startups.Endpoints.EndpointDefinition.DisplayName">
            <summary>
            显示名称
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Startups.Endpoints.EndpointDefinition.Order">
            <summary>
            顺序（优先级）
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware">
            <summary>
            端点刷新中间件，在第一次请求时强制刷新路由系统
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddlewareExtensions">
            <summary>
            中间件扩展方法
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.Endpoints.FixedEndpointDataSource">
            <summary>
            固定端点数据源，解决热重载问题
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Endpoints.FixedEndpointDataSource.ForceRefresh">
            <summary>
            强制更新数据源并通知监听者
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.Endpoints.IDynamicEndpointSource">
            <summary>
            动态端点源接口，用于获取动态端点定义
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Endpoints.IDynamicEndpointSource.GetEndpointsAsync">
            <summary>
            异步获取所有端点
            </summary>
            <returns>端点集合</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Endpoints.IDynamicEndpointSource.Watch(System.Action{System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Http.Endpoint}})">
            <summary>
            监控端点定义变化
            </summary>
            <param name="callback">端点变化回调方法</param>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.Extensions.ServiceCollectionExtensions">
            <summary>
            注册模块服务
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Extensions.ServiceCollectionExtensions.AddModuleServices(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Reflection.Assembly)">
            <summary>
            添加模块服务
            </summary>
            <param name="services">服务集合</param>
            <param name="moduleAssembly">模块程序集</param>
            <returns>服务集合</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Extensions.ServiceCollectionExtensions.ConfigureModuleServices(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Reflection.Assembly)">
            <summary>
            配置模块服务
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Extensions.ServiceCollectionExtensions.RegisterApplicationPart(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Reflection.Assembly)">
            <summary>
            注册应用程序部件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.Extensions.ServiceCollectionExtensions.NotifyDynamicEndpointManager(System.Reflection.Assembly)">
            <summary>
            通知动态端点管理器
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HostBuilder">
            <summary>
            配置项目启动项
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.RoutePrefixConvention">
            <summary>
            路由前缀约定类 - 支持模块化路由
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.RoutePrefixConvention.GetActionRouteInfo(Microsoft.AspNetCore.Mvc.ApplicationModels.ActionModel)">
            <summary>
            根据HTTP特性确定方法的路由信息
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.RoutePrefixConvention.ConfigureParameterBindingSources(Microsoft.AspNetCore.Mvc.ApplicationModels.ActionModel)">
            <summary>
            配置参数的模型绑定源
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.RoutePrefixConvention.IsComplexType(System.Type)">
            <summary>
            判断是否为复杂类型（需要从JSON绑定的类型）
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.RoutePrefixConvention.ExtractHttpMethod(System.String)">
            <summary>
            从特性名称提取HTTP方法
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.RoutePrefixConvention.ActionRouteInfo">
            <summary>
            路由信息类
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HotReload.IActionDescriptorRefreshService">
            <summary>
            ActionDescriptor刷新服务，用于强制刷新MVC系统的控制器发现机制
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.IActionDescriptorRefreshService.RefreshAsync">
            <summary>
            强制刷新ActionDescriptorCollectionProvider
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService">
            <summary>
            ActionDescriptor刷新服务实现
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService.RefreshAsync">
            <summary>
            强制刷新ActionDescriptorCollectionProvider
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.ActionDescriptorRefreshService.TryForceRefreshInternalState(Microsoft.AspNetCore.Mvc.Infrastructure.IActionDescriptorCollectionProvider)">
            <summary>
            尝试通过反射强制刷新内部状态
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HotReload.PluginLoadContext">
            <summary>
            插件加载上下文，用于隔离插件程序集
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HotReload.IAssemblyLoadContextPluginLoader">
            <summary>
            基于AssemblyLoadContext的插件加载器接口
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.IAssemblyLoadContextPluginLoader.LoadPluginAsync(System.String)">
            <summary>
            加载插件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.IAssemblyLoadContextPluginLoader.UnloadPluginAsync(System.String)">
            <summary>
            卸载插件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.IAssemblyLoadContextPluginLoader.ReloadPluginAsync(System.String)">
            <summary>
            重新加载插件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.IAssemblyLoadContextPluginLoader.GetLoadedPlugins">
            <summary>
            获取已加载的插件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.IAssemblyLoadContextPluginLoader.RefreshMvcSystemAsync">
            <summary>
            强制刷新MVC系统
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader">
            <summary>
            基于AssemblyLoadContext的插件加载器实现
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader.LoadPluginAsync(System.String)">
            <summary>
            加载插件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader.UnloadPluginAsync(System.String)">
            <summary>
            卸载插件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader.ReloadPluginAsync(System.String)">
            <summary>
            重新加载插件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader.GetLoadedPlugins">
            <summary>
            获取已加载的插件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.AssemblyLoadContextPluginLoader.RefreshMvcSystemAsync">
            <summary>
            强制刷新MVC系统
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HotReload.FileChangedEventHandler">
            <summary>
            文件变化事件处理委托
            </summary>
            <param name="filePath">文件路径</param>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HotReload.IFileWatcher">
            <summary>
            文件监控器接口
            </summary>
        </member>
        <member name="E:SSIC.Infrastructure.Startups.HotReload.IFileWatcher.FileChanged">
            <summary>
            文件创建或修改事件
            </summary>
        </member>
        <member name="E:SSIC.Infrastructure.Startups.HotReload.IFileWatcher.FileDeleted">
            <summary>
            文件删除事件
            </summary>
        </member>
        <member name="E:SSIC.Infrastructure.Startups.HotReload.IFileWatcher.FileRenamed">
            <summary>
            文件重命名事件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.IFileWatcher.StartWatching">
            <summary>
            开始监控
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.IFileWatcher.StopWatching">
            <summary>
            停止监控
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HotReload.FileWatcher">
            <summary>
            文件监控器实现
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.FileWatcher.StartWatching">
            <summary>
            开始监控文件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.FileWatcher.StopWatching">
            <summary>
            停止监控文件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.FileWatcher.WaitForFileToBeUnlocked(System.String)">
            <summary>
            等待文件解锁（确保可以访问文件）
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.FileWatcher.Dispose">
            <summary>
            释放资源
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.FileWatcher.Dispose(System.Boolean)">
            <summary>
            释放资源
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions">
            <summary>
            热插拔扩展方法
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.GlobalServiceProvider">
            <summary>
            全局服务提供程序
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.AddHotReload(Microsoft.AspNetCore.Builder.WebApplicationBuilder,System.Action{SSIC.Infrastructure.Startups.HotReload.HotReloadOptions})">
            <summary>
            在WebApplicationBuilder上添加热插拔支持，支持链式调用
            </summary>
            <param name="builder">Web应用程序构建器</param>
            <param name="configureOptions">配置选项</param>
            <returns>Web应用程序构建器</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.AddHotReload(Microsoft.AspNetCore.Builder.WebApplicationBuilder,System.Boolean)">
            <summary>
            在WebApplicationBuilder上添加热插拔支持，使用模块路径配置
            </summary>
            <param name="builder">Web应用程序构建器</param>
            <param name="enabled">是否启用热插拔功能，默认为false</param>
            <returns>Web应用程序构建器</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.AddHotReload(Microsoft.AspNetCore.Builder.WebApplicationBuilder,SSIC.Infrastructure.Startups.HotReload.HotReloadOptions)">
            <summary>
            在WebApplicationBuilder上添加热插拔支持，支持链式调用（重载版本，直接接收选项对象）
            </summary>
            <param name="builder">Web应用程序构建器</param>
            <param name="options">热插拔选项</param>
            <returns>Web应用程序构建器</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.UseHotReload(Microsoft.AspNetCore.Builder.WebApplication)">
            <summary>
            使用热插拔功能，初始化并启动热插拔模块
            </summary>
            <param name="app">Web应用程序</param>
            <returns>Web应用程序</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.UpdateRoutes(Microsoft.AspNetCore.Builder.WebApplication,SSIC.Infrastructure.Startups.HotReload.IPluginManager)">
            <summary>
            更新路由
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.RefreshOpenApiDocumentation(Microsoft.AspNetCore.Builder.WebApplication)">
            <summary>
            刷新OpenAPI文档和Scalar
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HotReload.HotReloadOptions">
            <summary>
            热插拔配置选项
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Startups.HotReload.HotReloadOptions.Enabled">
            <summary>
            是否启用热插拔功能
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Startups.HotReload.HotReloadOptions.WatchExtensions">
            <summary>
            监控的DLL文件扩展名
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Startups.HotReload.HotReloadOptions.WatchIntervalMilliseconds">
            <summary>
            监控间隔（毫秒）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Startups.HotReload.HotReloadOptions.WatchPaths">
            <summary>
            监控的目录路径列表
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Startups.HotReload.HotReloadOptions.RecursiveScan">
            <summary>
            是否递归扫描子目录
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Startups.HotReload.HotReloadOptions.MaxScanDepth">
            <summary>
            最大扫描深度（0表示无限制）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Startups.HotReload.HotReloadOptions.AutoCreateDirectories">
            <summary>
            自动创建监控目录
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Startups.HotReload.HotReloadOptions.ScanMode">
            <summary>
            模块扫描模式
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Startups.HotReload.HotReloadOptions.ModuleNamePrefix">
            <summary>
            模块名称前缀
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Startups.HotReload.HotReloadOptions.ModuleSearchPattern">
            <summary>
            模块搜索模式（用于文件匹配）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Startups.HotReload.HotReloadOptions.ApiRoutePrefix">
            <summary>
            API路由前缀
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Startups.HotReload.HotReloadOptions.MaxRetryCount">
            <summary>
            重试次数
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Startups.HotReload.HotReloadOptions.RetryDelayMilliseconds">
            <summary>
            重试间隔（毫秒）
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.Startups.HotReload.HotReloadOptions.WildcardPatterns">
            <summary>
            通配符模式列表（如"SSIC.MODULES.*.DLL"）
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HotReload.ModuleScanMode">
            <summary>
            模块扫描模式
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Startups.HotReload.ModuleScanMode.ByPrefix">
            <summary>
            按前缀匹配
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Startups.HotReload.ModuleScanMode.ByDirectory">
            <summary>
            按目录结构匹配
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Startups.HotReload.ModuleScanMode.ByPattern">
            <summary>
            按文件匹配模式
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Startups.HotReload.ModuleScanMode.AllDlls">
            <summary>
            所有DLL文件
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Startups.HotReload.ModuleScanMode.ByWildcardPattern">
            <summary>
            通配符模式匹配
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HotReload.HotReloadTrigger">
            <summary>
            热重载触发器，用于手动触发热重载
            通过修改Version属性的值并保存文件来触发热重载
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Startups.HotReload.HotReloadTrigger.Version">
            <summary>
            版本号，修改此值并保存文件可触发热重载
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Startups.HotReload.HotReloadTrigger.LastModified">
            <summary>
            获取当前时间戳，防止编译优化
            </summary>
        </member>
        <member name="F:SSIC.Infrastructure.Startups.HotReload.HotReloadTrigger.UpdateNote">
            <summary>
            更新说明，可选填
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HotReload.IModuleScanner">
            <summary>
            模块扫描器
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.IModuleScanner.ScanModulesAsync">
            <summary>
            扫描模块
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.IModuleScanner.IsValidModuleFile(System.String)">
            <summary>
            检查文件是否为有效模块
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HotReload.ModuleScanner">
            <summary>
            模块扫描器实现
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.ModuleScanner.ScanModulesAsync">
            <summary>
            扫描所有监控路径下的模块
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.ModuleScanner.ScanDirectory(System.String,System.Int32)">
            <summary>
            扫描目录
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.ModuleScanner.IsValidModuleFile(System.String)">
            <summary>
            检查文件是否为有效模块
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.ModuleScanner.WildcardToRegex(System.String)">
            <summary>
            将通配符模式转换为正则表达式
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HotReload.IMvcSystemRefreshService">
            <summary>
            MVC系统刷新服务接口
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.IMvcSystemRefreshService.RefreshAsync">
            <summary>
            强制刷新MVC系统，使其重新扫描控制器
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.IMvcSystemRefreshService.NotifyApplicationPartChangedAsync">
            <summary>
            通知ApplicationPartManager发生变化
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService">
            <summary>
            MVC系统刷新服务实现
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService.RefreshAsync">
            <summary>
            强制刷新MVC系统，使其重新扫描控制器
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService.NotifyApplicationPartChangedAsync">
            <summary>
            通知ApplicationPartManager发生变化
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService.RefreshActionDescriptorCollectionProviderAsync">
            <summary>
            强制刷新ActionDescriptorCollectionProvider
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService.ClearActionDescriptorProviderCacheAsync(Microsoft.AspNetCore.Mvc.Infrastructure.IActionDescriptorCollectionProvider)">
            <summary>
            清除ActionDescriptorProvider的内部缓存
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService.TriggerApplicationPartsChangeNotificationAsync">
            <summary>
            触发ApplicationParts集合的变更通知
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.MvcSystemRefreshService.ClearMvcCachesAsync">
            <summary>
            清除MVC相关缓存
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HotReload.IOpenApiRefreshService">
            <summary>
            OpenAPI文档刷新服务接口
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.IOpenApiRefreshService.RefreshAsync">
            <summary>
            刷新OpenAPI文档
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.IOpenApiRefreshService.ClearCacheAsync">
            <summary>
            清除OpenAPI缓存
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HotReload.OpenApiRefreshService">
            <summary>
            OpenAPI文档刷新服务实现
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.OpenApiRefreshService.RefreshAsync">
            <summary>
            刷新OpenAPI文档（简化版本）
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.OpenApiRefreshService.ClearCacheAsync">
            <summary>
            清除OpenAPI缓存（简化版本）
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HotReload.IPluginManager">
            <summary>
            插件管理器接口
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.IPluginManager.InitializeAsync">
            <summary>
            初始化插件管理器
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.IPluginManager.LoadPluginAsync(System.String)">
            <summary>
            加载插件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.IPluginManager.UnloadPluginAsync(System.String)">
            <summary>
            卸载插件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.IPluginManager.ReloadPluginAsync(System.String)">
            <summary>
            重新加载插件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.IPluginManager.GetLoadedPlugins">
            <summary>
            获取已加载的插件列表
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HotReload.PluginManager">
            <summary>
            插件管理器实现
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.PluginManager.InitializeAsync">
            <summary>
            初始化插件管理器
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.PluginManager.LoadPluginAsync(System.String)">
            <summary>
            加载插件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.PluginManager.LoadAssemblyFromMemoryMappedFileAsync(System.String)">
            <summary>
            使用内存映射文件加载程序集
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.PluginManager.UnloadPluginAsync(System.String)">
            <summary>
            卸载插件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.PluginManager.ReloadPluginAsync(System.String)">
            <summary>
            重新加载插件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.PluginManager.GetLoadedPlugins">
            <summary>
            获取已加载的插件列表
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.PluginManager.ConfigureModuleServicesAsync(System.Reflection.Assembly)">
            <summary>
            配置模块服务
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.PluginManager.CleanupTempFiles(System.Object)">
            <summary>
            定期清理临时文件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.PluginManager.Dispose">
            <summary>
            释放资源
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.PluginManager.Dispose(System.Boolean)">
            <summary>
            释放资源
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HotReload.MemoryMappedAssemblyLoadContext">
            <summary>
            内存映射文件程序集加载上下文
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.MemoryMappedAssemblyLoadContext.LoadFromMemoryMappedFile">
            <summary>
            从内存映射文件加载程序集
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HotReload.ISimpleOpenApiRefreshService">
            <summary>
            简化的OpenAPI文档刷新服务
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.ISimpleOpenApiRefreshService.RefreshAsync">
            <summary>
            刷新OpenAPI相关组件
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService">
            <summary>
            简化的OpenAPI文档刷新服务实现
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService.RefreshAsync">
            <summary>
            刷新OpenAPI相关组件
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService.RefreshActionDescriptors">
            <summary>
            刷新ActionDescriptor集合
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService.NotifyControllerChange">
            <summary>
            通知控制器变更
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.IStartups">
            <summary>
            启动项接口
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService">
            <summary>
            端点管理服务实现
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService.CreateEndpointsFromModule(SSIC.Infrastructure.Startups.ModuleManager.ModuleInfo)">
            <summary>
            从模块信息创建端点
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService.CreateEndpoint(SSIC.Infrastructure.Startups.ModuleManager.EndpointCreationOptions)">
            <summary>
            创建单个端点
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService.CreateMvcEndpoint(SSIC.Infrastructure.Startups.ModuleManager.ModuleInfo,SSIC.Infrastructure.Startups.ModuleManager.ControllerInfo,SSIC.Infrastructure.Startups.ModuleManager.ActionInfo)">
            <summary>
            创建MVC控制器端点
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService.RefreshEndpointsAsync(System.Collections.Generic.IEnumerable{SSIC.Infrastructure.Startups.ModuleManager.ModuleInfo})">
            <summary>
            批量刷新端点
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService.ValidateEndpoint(Microsoft.AspNetCore.Http.Endpoint)">
            <summary>
            验证端点是否有效
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService.CreateDefaultHandler(System.String)">
            <summary>
            创建默认的请求处理器
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService.CreateMvcHandler(Microsoft.AspNetCore.Mvc.Controllers.ControllerActionDescriptor)">
            <summary>
            创建MVC请求处理器
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.ModuleManager.IEndpointManagementService">
            <summary>
            端点管理服务接口
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.IEndpointManagementService.CreateEndpointsFromModule(SSIC.Infrastructure.Startups.ModuleManager.ModuleInfo)">
            <summary>
            从模块信息创建端点
            </summary>
            <param name="moduleInfo">模块信息</param>
            <returns>端点列表</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.IEndpointManagementService.CreateEndpoint(SSIC.Infrastructure.Startups.ModuleManager.EndpointCreationOptions)">
            <summary>
            创建单个端点
            </summary>
            <param name="options">端点创建选项</param>
            <returns>端点</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.IEndpointManagementService.CreateMvcEndpoint(SSIC.Infrastructure.Startups.ModuleManager.ModuleInfo,SSIC.Infrastructure.Startups.ModuleManager.ControllerInfo,SSIC.Infrastructure.Startups.ModuleManager.ActionInfo)">
            <summary>
            创建MVC控制器端点
            </summary>
            <param name="moduleInfo">模块信息</param>
            <param name="controllerInfo">控制器信息</param>
            <param name="actionInfo">动作信息</param>
            <returns>端点</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.IEndpointManagementService.RefreshEndpointsAsync(System.Collections.Generic.IEnumerable{SSIC.Infrastructure.Startups.ModuleManager.ModuleInfo})">
            <summary>
            批量刷新端点
            </summary>
            <param name="modules">模块列表</param>
            <returns>所有端点</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.IEndpointManagementService.ValidateEndpoint(Microsoft.AspNetCore.Http.Endpoint)">
            <summary>
            验证端点是否有效
            </summary>
            <param name="endpoint">端点</param>
            <returns>是否有效</returns>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.ModuleManager.EndpointCreationOptions">
            <summary>
            端点创建选项
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.ModuleManager.IModuleDiscoveryService">
            <summary>
            模块发现服务接口
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.IModuleDiscoveryService.DiscoverLoadedModules">
            <summary>
            发现所有已加载的模块
            </summary>
            <returns>模块信息列表</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.IModuleDiscoveryService.DiscoverControllersInAssembly(System.Reflection.Assembly)">
            <summary>
            从程序集中发现控制器
            </summary>
            <param name="assembly">程序集</param>
            <returns>控制器信息列表</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.IModuleDiscoveryService.DiscoverActionsInController(System.Type)">
            <summary>
            从控制器类型中发现动作方法
            </summary>
            <param name="controllerType">控制器类型</param>
            <returns>动作方法信息列表</returns>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.IModuleDiscoveryService.BuildRouteTemplate(SSIC.Infrastructure.Startups.ModuleManager.ModuleInfo,SSIC.Infrastructure.Startups.ModuleManager.ControllerInfo,SSIC.Infrastructure.Startups.ModuleManager.ActionInfo)">
            <summary>
            构建控制器的路由模板
            </summary>
            <param name="moduleInfo">模块信息</param>
            <param name="controllerInfo">控制器信息</param>
            <param name="actionInfo">动作信息（可选）</param>
            <returns>路由模板</returns>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.ModuleManager.ModuleInfo">
            <summary>
            模块信息
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.ModuleManager.ControllerInfo">
            <summary>
            控制器信息
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.ModuleManager.ActionInfo">
            <summary>
            动作方法信息
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService">
            <summary>
            模块发现服务实现
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService.DiscoverLoadedModules">
            <summary>
            发现所有已加载的模块
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService.DiscoverControllersInAssembly(System.Reflection.Assembly)">
            <summary>
            从程序集中发现控制器
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService.DiscoverActionsInController(System.Type)">
            <summary>
            从控制器类型中发现动作方法
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService.BuildRouteTemplate(SSIC.Infrastructure.Startups.ModuleManager.ModuleInfo,SSIC.Infrastructure.Startups.ModuleManager.ControllerInfo,SSIC.Infrastructure.Startups.ModuleManager.ActionInfo)">
            <summary>
            构建控制器的路由模板
            </summary>
        </member>
        <member name="M:SSIC.Infrastructure.Startups.ProgramStartup.AddStartup(Microsoft.AspNetCore.Builder.WebApplicationBuilder,System.Boolean)">
            <summary>
            添加应用程序启动配置
            </summary>
            <param name="builder">Web应用程序构建器</param>
            <param name="UseServiceDefault">是否使用服务默认配置</param>
            <returns>构建的Web应用程序</returns>
        </member>
        <member name="T:SSIC.Infrastructure.ViewModel.OutMenu">
            <summary>
            菜单实体
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutMenu.name">
            <summary>
            菜单名
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutMenu.icon">
            <summary>
            菜单图标,如果没有，则会尝试使用route.meta.icon
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutMenu.path">
            <summary>
            菜单路径
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutMenu.disabled">
            <summary>
            是否禁用
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutMenu.component">
             <summary>
            
             </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutMenu.redirect">
            <summary>
            重定向
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutMenu.meta">
            <summary>
            菜单属性
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.ViewModel.meta">
            <summary>
            菜单属性
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.meta.title">
            <summary>
            路由title  一般必填
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.meta.ignoreAuth">
            <summary>
            是否忽略权限，只在权限模式为Role的时候有效
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.meta.ignoreKeepAlive">
            roles?: RoleEnum[];
             <summary>
            是否忽略KeepAlive缓存
             </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.meta.affix">
             <summary>
            是否固定标签
             </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.meta.icon">
            <summary>
             图标，也是菜单图标
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.meta.frameSrc">
            <summary>
            内嵌iframe的地址
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.meta.transitionName">
            <summary>
            指定该路由切换的动画名
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.meta.hideBreadcrumb">
            <summary>
            隐藏该路由在面包屑上面的显示
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.meta.carryParam">
            <summary>
            如果该路由会携带参数，且需要在tab页上面显示。则需要设置为true
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.meta.currentActiveMenu">
            <summary>
            当前激活的菜单。用于配置详情页时左侧激活的菜单路径
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.meta.hideTab">
            <summary>
            当前路由不再标签页显示
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.meta.hideMenu">
            <summary>
            当前路由不再菜单显示
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.ViewModel.tag">
            <summary>
            菜单标签设置
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.tag.dot">
            <summary>
            为true则显示小圆点
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.tag.content">
            <summary>
            内容
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.tag.type">
            <summary>
            类型
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.ViewModel.OutPermission">
            <summary>
            权限输出表
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutPermission.name">
            <summary>
            权限名称
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutPermission.code">
            <summary>
            权限代号
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutPermission.icon">
            <summary>
            按钮图标
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutPermission.color">
            <summary>
            按钮颜色
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutPermission.titlekey">
            <summary>
            多语言标签t
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutPermission.type">
            <summary>
            按钮类型0.弹窗,1.接口
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.ViewModel.OutputView">
            <summary>
            输出数据实体
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutputView.c">
            <summary>
            输出代号
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutputView.m">
            <summary>
            输出内容
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutputView.d">
            <summary>
            输出数据
            </summary>
        </member>
        <member name="T:SSIC.Infrastructure.ViewModel.OutTable">
            <summary>
            输出表格数据
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutTable.items">
            <summary>
            数据组
            </summary>
        </member>
        <member name="P:SSIC.Infrastructure.ViewModel.OutTable.total">
            <summary>
            总计
            </summary>
        </member>
    </members>
</doc>
