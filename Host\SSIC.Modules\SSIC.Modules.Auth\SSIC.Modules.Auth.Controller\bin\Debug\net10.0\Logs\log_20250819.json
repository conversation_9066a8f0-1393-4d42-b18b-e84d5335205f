{"@t":"2025-08-19T03:25:39.7729327Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:25:40.3553755Z","@mt":"动态端点管理器初始化完成","SourceContext":"ApplicationBuilderExtensions","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:25:40.3834077Z","@mt":"发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:25:40.3879771Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth.Controller","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:25:40.3898286Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:25:40.3915898Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:25:40.3934502Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:25:40.4733120Z","@mt":"Now listening on: {address}","address":"http://localhost:5041","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:25:40.4742444Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:25:40.4749297Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:25:40.4758648Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.Controller","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:26:25.7949407Z","@mt":"初始化模块热加载监控...","@tr":"d63740d3d3145a651e1f58ddc687eb92","@sp":"88309a0c4cc7e175","SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNEUQ0MKAIS0:********","RequestPath":"/","ConnectionId":"0HNEUQ0MKAIS0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:26:25.7967501Z","@mt":"模块热加载监控初始化完成，监控 {DirectoryCount} 个目录","@tr":"d63740d3d3145a651e1f58ddc687eb92","@sp":"88309a0c4cc7e175","DirectoryCount":0,"SourceContext":"SSIC.Infrastructure.Middleware.ModuleHotReloadMiddleware","RequestId":"0HNEUQ0MKAIS0:********","RequestPath":"/","ConnectionId":"0HNEUQ0MKAIS0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:26:25.7987291Z","@mt":"初始化动态路由系统...","@tr":"d63740d3d3145a651e1f58ddc687eb92","@sp":"88309a0c4cc7e175","SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNEUQ0MKAIS0:********","RequestPath":"/","ConnectionId":"0HNEUQ0MKAIS0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:26:25.8002009Z","@mt":"发现 {Count} 个模块程序集","@tr":"d63740d3d3145a651e1f58ddc687eb92","@sp":"88309a0c4cc7e175","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEUQ0MKAIS0:********","RequestPath":"/","ConnectionId":"0HNEUQ0MKAIS0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:26:25.8018307Z","@mt":"动态路由系统初始化完成，发现 {ModuleCount} 个模块","@tr":"d63740d3d3145a651e1f58ddc687eb92","@sp":"88309a0c4cc7e175","ModuleCount":1,"SourceContext":"SSIC.Infrastructure.Middleware.DynamicRoutingMiddleware","RequestId":"0HNEUQ0MKAIS0:********","RequestPath":"/","ConnectionId":"0HNEUQ0MKAIS0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:26:25.8034772Z","@mt":"发现 {Count} 个模块程序集","@tr":"d63740d3d3145a651e1f58ddc687eb92","@sp":"88309a0c4cc7e175","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEUQ0MKAIS0:********","RequestPath":"/","ConnectionId":"0HNEUQ0MKAIS0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:26:25.8066343Z","@mt":"首次请求触发端点刷新","@tr":"d63740d3d3145a651e1f58ddc687eb92","@sp":"88309a0c4cc7e175","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEUQ0MKAIS0:********","RequestPath":"/","ConnectionId":"0HNEUQ0MKAIS0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:26:25.8079373Z","@mt":"发现 {Count} 个模块程序集","@tr":"d63740d3d3145a651e1f58ddc687eb92","@sp":"88309a0c4cc7e175","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEUQ0MKAIS0:********","RequestPath":"/","ConnectionId":"0HNEUQ0MKAIS0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:26:25.8086395Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"d63740d3d3145a651e1f58ddc687eb92","@sp":"88309a0c4cc7e175","ModuleName":"Auth.Controller","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEUQ0MKAIS0:********","RequestPath":"/","ConnectionId":"0HNEUQ0MKAIS0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:26:25.8094186Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"d63740d3d3145a651e1f58ddc687eb92","@sp":"88309a0c4cc7e175","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEUQ0MKAIS0:********","RequestPath":"/","ConnectionId":"0HNEUQ0MKAIS0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:26:25.8107868Z","@mt":"发现 {Count} 个模块程序集","@tr":"d63740d3d3145a651e1f58ddc687eb92","@sp":"88309a0c4cc7e175","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEUQ0MKAIS0:********","RequestPath":"/","ConnectionId":"0HNEUQ0MKAIS0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:26:25.8114381Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","@tr":"d63740d3d3145a651e1f58ddc687eb92","@sp":"88309a0c4cc7e175","ModuleName":"Auth.Controller","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEUQ0MKAIS0:********","RequestPath":"/","ConnectionId":"0HNEUQ0MKAIS0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:26:25.8120752Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","@tr":"d63740d3d3145a651e1f58ddc687eb92","@sp":"88309a0c4cc7e175","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","RequestId":"0HNEUQ0MKAIS0:********","RequestPath":"/","ConnectionId":"0HNEUQ0MKAIS0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:26:25.8129744Z","@mt":"请求触发的端点刷新完成，发现 {ModuleCount} 个模块，{EndpointCount} 个端点","@tr":"d63740d3d3145a651e1f58ddc687eb92","@sp":"88309a0c4cc7e175","ModuleCount":1,"EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEUQ0MKAIS0:********","RequestPath":"/","ConnectionId":"0HNEUQ0MKAIS0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:26:25.8152701Z","@mt":"Failed to determine the https port for redirect.","@l":"Warning","@tr":"d63740d3d3145a651e1f58ddc687eb92","@sp":"88309a0c4cc7e175","EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNEUQ0MKAIS0:********","RequestPath":"/","ConnectionId":"0HNEUQ0MKAIS0","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:26:30.8757073Z","@mt":"发现 {Count} 个模块程序集","@tr":"1cfd243db61b1d5b087ec77d72b8eadd","@sp":"ee9266a886fc1ca8","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEUQ0MKAIS2:********","RequestPath":"/swagger","ConnectionId":"0HNEUQ0MKAIS2","MachineName":"LAPTOP-F6SGUTN5","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:26:35.1565513Z","@mt":"发现 {Count} 个模块程序集","@tr":"3f2a0e57ed915faf7e05f34c3e93647d","@sp":"82fcb7f8201b89c6","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEUQ0MKAIS4:********","RequestPath":"/scalar/v1","ConnectionId":"0HNEUQ0MKAIS4","MachineName":"LAPTOP-F6SGUTN5","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:26:35.2631541Z","@mt":"发现 {Count} 个模块程序集","@tr":"2ee3a44b5729d84eec6f4da94f755c8b","@sp":"5656c5cbb11e7884","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEUQ0MKAIS6:********","RequestPath":"/scalar/scalar.js","ConnectionId":"0HNEUQ0MKAIS6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:26:35.2631541Z","@mt":"发现 {Count} 个模块程序集","@tr":"f7254fa5181cd8c85e6fe2f62860d9dc","@sp":"a4f847575ff0ec61","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEUQ0MKAIS5:********","RequestPath":"/scalar/scalar.aspnetcore.js","ConnectionId":"0HNEUQ0MKAIS5","MachineName":"LAPTOP-F6SGUTN5","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:26:35.4216512Z","@mt":"发现 {Count} 个模块程序集","@tr":"8e1b798d00e68420fd1ce9d46cd76464","@sp":"52d71ba34339388c","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEUQ0MKAIS6:00000002","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEUQ0MKAIS6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:26:35.4424044Z","@mt":"开始转换OpenAPI文档以包含热加载的模块...","@tr":"8e1b798d00e68420fd1ce9d46cd76464","@sp":"52d71ba34339388c","SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNEUQ0MKAIS6:00000002","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEUQ0MKAIS6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:26:35.4431287Z","@mt":"发现 {Count} 个控制器动作","@tr":"8e1b798d00e68420fd1ce9d46cd76464","@sp":"52d71ba34339388c","Count":0,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNEUQ0MKAIS6:00000002","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEUQ0MKAIS6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:26:35.4438690Z","@mt":"发现 {Count} 个模块","@tr":"8e1b798d00e68420fd1ce9d46cd76464","@sp":"52d71ba34339388c","Count":0,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNEUQ0MKAIS6:00000002","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEUQ0MKAIS6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:26:35.4447346Z","@mt":"OpenAPI文档转换完成，包含 {ModuleCount} 个模块，{ActionCount} 个动作","@tr":"8e1b798d00e68420fd1ce9d46cd76464","@sp":"52d71ba34339388c","ModuleCount":0,"ActionCount":0,"SourceContext":"SSIC.Infrastructure.OpenApi.HotReloadOpenApiDocumentTransformer","RequestId":"0HNEUQ0MKAIS6:00000002","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEUQ0MKAIS6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:26:35.4462199Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"8e1b798d00e68420fd1ce9d46cd76464","@sp":"52d71ba34339388c","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEUQ0MKAIS6:00000002","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEUQ0MKAIS6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:26:35.4481561Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"8e1b798d00e68420fd1ce9d46cd76464","@sp":"52d71ba34339388c","Count":1,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEUQ0MKAIS6:00000002","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEUQ0MKAIS6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:26:35.4510251Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"8e1b798d00e68420fd1ce9d46cd76464","@sp":"52d71ba34339388c","PathCount":0,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEUQ0MKAIS6:00000002","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEUQ0MKAIS6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:26:36.0771355Z","@mt":"发现 {Count} 个模块程序集","@tr":"8431166cfec5c25d4acf4cc142f7ae03","@sp":"12fb555a1ea1753b","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","RequestId":"0HNEUQ0MKAIS6:00000003","RequestPath":"/favicon.ico","ConnectionId":"0HNEUQ0MKAIS6","MachineName":"LAPTOP-F6SGUTN5","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:27:04.6797717Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:27:04.8251721Z","@mt":"动态端点管理器初始化完成","SourceContext":"ApplicationBuilderExtensions","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:27:04.8723366Z","@mt":"发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.ModuleDiscoveryService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:27:04.8763984Z","@mt":"为模块 {ModuleName} 创建了 {EndpointCount} 个端点","ModuleName":"Auth.Controller","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:27:04.8773392Z","@mt":"刷新完成，共创建 {EndpointCount} 个端点","EndpointCount":0,"SourceContext":"SSIC.Infrastructure.Startups.ModuleManager.EndpointManagementService","MachineName":"LAPTOP-F6SGUTN5","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:27:04.8782999Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:27:04.8797155Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:27:05.1028913Z","@mt":"Now listening on: {address}","address":"https://localhost:7188","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:27:05.1051401Z","@mt":"Now listening on: {address}","address":"http://localhost:5041","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:27:05.1365103Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:27:05.1382563Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-08-19T03:27:05.1394095Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.Controller","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"LAPTOP-F6SGUTN5","ThreadId":2,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
