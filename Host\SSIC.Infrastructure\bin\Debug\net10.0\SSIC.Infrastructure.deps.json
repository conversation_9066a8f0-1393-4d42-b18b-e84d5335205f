{"runtimeTarget": {"name": ".NETCoreApp,Version=v10.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v10.0": {"SSIC.Infrastructure/1.0.0": {"dependencies": {"Dapr.AspNetCore": "1.16.0-rc13", "FreeRedis": "1.4.0", "FreeScheduler": "2.0.36", "FreeSql.All": "3.5.213-preview20250815", "FreeSql.Cloud": "2.0.1", "Mapster": "7.4.2-pre02", "Masa.Contrib.Development.DaprStarter.AspNetCore": "1.2.0-preview.10", "Microsoft.AspNetCore.Authentication.JwtBearer": "10.0.0-preview.7.25380.108", "Microsoft.AspNetCore.OpenApi": "9.0.1", "Microsoft.Data.SqlClient": "6.1.1", "Microsoft.Extensions.Configuration": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.Json": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.DependencyInjection": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Hosting": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Http.Resilience": "9.8.0", "Microsoft.Extensions.ServiceDiscovery": "9.4.1", "Microsoft.OpenApi": "1.6.24", "MiniProfiler.AspNetCore": "4.5.4", "MiniProfiler.AspNetCore.Mvc": "4.5.4", "Npgsql": "9.0.3", "OpenTelemetry": "1.12.0", "OpenTelemetry.Exporter.OpenTelemetryProtocol": "1.12.0", "OpenTelemetry.Exporter.Zipkin": "1.12.0", "OpenTelemetry.Extensions.Hosting": "1.12.0", "OpenTelemetry.Instrumentation.AspNetCore": "1.12.0", "OpenTelemetry.Instrumentation.GrpcCore": "1.0.0-beta.6", "OpenTelemetry.Instrumentation.GrpcNetClient": "1.12.0-beta.1", "OpenTelemetry.Instrumentation.Http": "1.12.0", "OpenTelemetry.Instrumentation.Process": "1.12.0-beta.1", "OpenTelemetry.Instrumentation.Runtime": "1.12.0", "SSIC.Entity": "1.0.0", "Scalar.AspNetCore": "2.6.9", "Serilog": "4.3.1-dev-02373", "Serilog.AspNetCore": "9.0.0", "Serilog.Enrichers.Environment": "3.0.1", "Serilog.Enrichers.Thread": "4.0.0", "Serilog.Expressions": "5.1.0-dev-02301", "Serilog.Extensions.Hosting": "9.0.1-dev-02307", "Serilog.Extensions.Logging": "9.0.3-dev-02320", "Serilog.Settings.Configuration": "9.0.1-dev-02317", "Serilog.Sinks.Console": "6.0.1-dev-00953", "Serilog.Sinks.OpenTelemetry": "4.2.1-dev-02306", "Serilog.Sinks.Seq": "9.0.0", "SixLabors.ImageSharp": "3.1.11", "Swashbuckle.AspNetCore": "9.0.3"}, "runtime": {"SSIC.Infrastructure.dll": {}}}, "Azure.Core/1.47.1": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "System.ClientModel": "1.5.1", "System.Memory.Data": "8.0.1"}, "runtime": {"lib/net8.0/Azure.Core.dll": {"assemblyVersion": "********", "fileVersion": "1.4700.125.36505"}}}, "Azure.Identity/1.14.2": {"dependencies": {"Azure.Core": "1.47.1", "Microsoft.Identity.Client": "4.73.1", "Microsoft.Identity.Client.Extensions.Msal": "4.73.1"}, "runtime": {"lib/net8.0/Azure.Identity.dll": {"assemblyVersion": "********", "fileVersion": "1.1400.225.36004"}}}, "BouncyCastle.Cryptography/2.3.1": {"runtime": {"lib/net6.0/BouncyCastle.Cryptography.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.3.1.17862"}}}, "Dapr.AspNetCore/1.16.0-rc13": {"dependencies": {"Dapr.Client": "1.16.0-rc13", "Dapr.Common": "1.16.0-rc13", "Google.Api.CommonProtos": "2.17.0", "Google.Protobuf": "3.31.1", "Grpc.Net.Client": "2.71.0", "Microsoft.Extensions.Configuration": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.DependencyInjection": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net9.0/Dapr.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "1.16.0.0"}}}, "Dapr.Client/1.16.0-rc13": {"dependencies": {"Dapr.Common": "1.16.0-rc13", "Dapr.Protos": "1.16.0-rc13", "Google.Api.CommonProtos": "2.17.0", "Google.Protobuf": "3.31.1", "Grpc.Net.Client": "2.71.0", "Microsoft.Extensions.Configuration": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.DependencyInjection": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net9.0/Dapr.Client.dll": {"assemblyVersion": "*******", "fileVersion": "1.16.0.0"}}}, "Dapr.Common/1.16.0-rc13": {"dependencies": {"Dapr.Protos": "1.16.0-rc13", "Google.Api.CommonProtos": "2.17.0", "Google.Protobuf": "3.31.1", "Grpc.Net.Client": "2.71.0", "Microsoft.Extensions.Configuration": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.DependencyInjection": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net9.0/Dapr.Common.dll": {"assemblyVersion": "*******", "fileVersion": "1.16.0.0"}}}, "Dapr.Protos/1.16.0-rc13": {"dependencies": {"Google.Api.CommonProtos": "2.17.0", "Google.Protobuf": "3.31.1", "Grpc.Net.Client": "2.71.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net9.0/Dapr.Protos.dll": {"assemblyVersion": "*******", "fileVersion": "1.16.0.0"}}}, "DM.DmProvider/8.3.1.28188": {"runtime": {"lib/net8.0/DM.DmProvider.dll": {"assemblyVersion": "8.3.1.28188", "fileVersion": "8.3.1.28188"}}, "resources": {"lib/net8.0/en/DM.DmProvider.resources.dll": {"locale": "en"}, "lib/net8.0/zh-CN/DM.DmProvider.resources.dll": {"locale": "zh-CN"}, "lib/net8.0/zh-HK/DM.DmProvider.resources.dll": {"locale": "zh-HK"}, "lib/net8.0/zh-TW/DM.DmProvider.resources.dll": {"locale": "zh-TW"}}}, "FreeRedis/1.4.0": {"runtime": {"lib/netstandard2.0/FreeRedis.dll": {"assemblyVersion": "1.4.0.0", "fileVersion": "1.4.0.0"}}}, "FreeScheduler/2.0.36": {"dependencies": {"FreeRedis": "1.4.0", "FreeSql.DbContext": "3.5.213-preview20250815", "IdleBus": "1.5.3", "Microsoft.Extensions.FileProviders.Physical": "10.0.0-preview.7.25380.108", "Newtonsoft.Json": "13.0.1", "WorkQueue": "1.3.0"}, "runtime": {"lib/net8.0/FreeScheduler.dll": {"assemblyVersion": "2.0.36.0", "fileVersion": "2.0.36.0"}}}, "FreeSql/3.5.213-preview20250815": {"runtime": {"lib/netstandard2.1/FreeSql.dll": {"assemblyVersion": "3.5.213.0", "fileVersion": "3.5.213.0"}}}, "FreeSql.All/3.5.213-preview20250815": {"dependencies": {"FreeSql.Provider.Dameng": "3.5.213-preview20250815", "FreeSql.Provider.MsAccess": "3.5.213-preview20250815", "FreeSql.Provider.MySql": "3.5.213-preview20250815", "FreeSql.Provider.Odbc": "3.5.213-preview20250815", "FreeSql.Provider.Oracle": "3.5.213-preview20250815", "FreeSql.Provider.PostgreSQL": "3.5.213-preview20250815", "FreeSql.Provider.SqlServer": "3.5.213-preview20250815", "FreeSql.Provider.Sqlite": "3.5.213-preview20250815", "FreeSql.Repository": "3.5.213-preview20250815"}, "runtime": {"lib/netstandard2.1/FreeSql.All.dll": {"assemblyVersion": "3.5.213.0", "fileVersion": "3.5.213.0"}}}, "FreeSql.Cloud/2.0.1": {"dependencies": {"FreeScheduler": "2.0.36", "FreeSql.DbContext": "3.5.213-preview20250815", "IdleBus": "1.5.3", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/FreeSql.Cloud.dll": {"assemblyVersion": "2.0.1.0", "fileVersion": "2.0.1.0"}}}, "FreeSql.DbContext/3.5.213-preview20250815": {"dependencies": {"FreeSql": "3.5.213-preview20250815", "Microsoft.Extensions.DependencyInjection": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net9.0/FreeSql.DbContext.dll": {"assemblyVersion": "3.5.213.0", "fileVersion": "3.5.213.0"}}}, "FreeSql.Provider.Dameng/3.5.213-preview20250815": {"dependencies": {"DM.DmProvider": "8.3.1.28188", "FreeSql": "3.5.213-preview20250815"}, "runtime": {"lib/net6.0/FreeSql.Provider.Dameng.dll": {"assemblyVersion": "3.5.213.0", "fileVersion": "3.5.213.0"}}}, "FreeSql.Provider.MsAccess/3.5.213-preview20250815": {"dependencies": {"FreeSql": "3.5.213-preview20250815", "System.Data.OleDb": "6.0.0"}, "runtime": {"lib/netstandard2.0/FreeSql.Provider.MsAccess.dll": {"assemblyVersion": "3.5.213.0", "fileVersion": "3.5.213.0"}}}, "FreeSql.Provider.MySql/3.5.213-preview20250815": {"dependencies": {"FreeSql": "3.5.213-preview20250815", "MySql.Data": "9.1.0"}, "runtime": {"lib/net9.0/FreeSql.Provider.MySql.dll": {"assemblyVersion": "3.5.213.0", "fileVersion": "3.5.213.0"}}}, "FreeSql.Provider.Odbc/3.5.213-preview20250815": {"dependencies": {"FreeSql": "3.5.213-preview20250815", "System.Data.Odbc": "8.0.0"}, "runtime": {"lib/netstandard2.0/FreeSql.Provider.Odbc.dll": {"assemblyVersion": "3.5.213.0", "fileVersion": "3.5.213.0"}}}, "FreeSql.Provider.Oracle/3.5.213-preview20250815": {"dependencies": {"FreeSql": "3.5.213-preview20250815", "Oracle.ManagedDataAccess.Core": "23.6.1"}, "runtime": {"lib/net9.0/FreeSql.Provider.Oracle.dll": {"assemblyVersion": "3.5.213.0", "fileVersion": "3.5.213.0"}}}, "FreeSql.Provider.PostgreSQL/3.5.213-preview20250815": {"dependencies": {"FreeSql": "3.5.213-preview20250815", "Newtonsoft.Json": "13.0.1", "Npgsql.LegacyPostgis": "5.0.18", "Npgsql.NetTopologySuite": "5.0.18"}, "runtime": {"lib/net9.0/FreeSql.Provider.PostgreSQL.dll": {"assemblyVersion": "3.5.213.0", "fileVersion": "3.5.213.0"}}}, "FreeSql.Provider.Sqlite/3.5.213-preview20250815": {"dependencies": {"FreeSql": "3.5.213-preview20250815", "System.Data.SQLite.Core": "1.0.119"}, "runtime": {"lib/netstandard2.0/FreeSql.Provider.Sqlite.dll": {"assemblyVersion": "3.5.213.0", "fileVersion": "3.5.213.0"}}}, "FreeSql.Provider.SqlServer/3.5.213-preview20250815": {"dependencies": {"FreeSql": "3.5.213-preview20250815", "Microsoft.Data.SqlClient": "6.1.1"}, "runtime": {"lib/net9.0/FreeSql.Provider.SqlServer.dll": {"assemblyVersion": "3.5.213.0", "fileVersion": "3.5.213.0"}}}, "FreeSql.Repository/3.5.213-preview20250815": {"dependencies": {"FreeSql.DbContext": "3.5.213-preview20250815"}, "runtime": {"lib/net9.0/FreeSql.Repository.dll": {"assemblyVersion": "3.5.213.0", "fileVersion": "3.5.213.0"}}}, "Google.Api.CommonProtos/2.17.0": {"dependencies": {"Google.Protobuf": "3.31.1"}, "runtime": {"lib/netstandard2.0/Google.Api.CommonProtos.dll": {"assemblyVersion": "2.17.0.0", "fileVersion": "2.17.0.0"}}}, "Google.Protobuf/3.31.1": {"runtime": {"lib/net5.0/Google.Protobuf.dll": {"assemblyVersion": "3.31.1.0", "fileVersion": "3.31.1.0"}}}, "Grpc.Core.Api/2.71.0": {"runtime": {"lib/netstandard2.1/Grpc.Core.Api.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.71.0.0"}}}, "Grpc.Net.Client/2.71.0": {"dependencies": {"Grpc.Net.Common": "2.71.0", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net8.0/Grpc.Net.Client.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.71.0.0"}}}, "Grpc.Net.Common/2.71.0": {"dependencies": {"Grpc.Core.Api": "2.71.0"}, "runtime": {"lib/net8.0/Grpc.Net.Common.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.71.0.0"}}}, "IdleBus/1.5.3": {"runtime": {"lib/netstandard2.0/IdleBus.dll": {"assemblyVersion": "1.5.3.0", "fileVersion": "1.5.3.0"}}}, "K4os.Compression.LZ4/1.3.8": {"runtime": {"lib/net6.0/K4os.Compression.LZ4.dll": {"assemblyVersion": "1.3.8.0", "fileVersion": "1.3.8.0"}}}, "K4os.Compression.LZ4.Streams/1.3.8": {"dependencies": {"K4os.Compression.LZ4": "1.3.8", "K4os.Hash.xxHash": "1.0.8"}, "runtime": {"lib/net6.0/K4os.Compression.LZ4.Streams.dll": {"assemblyVersion": "1.3.8.0", "fileVersion": "1.3.8.0"}}}, "K4os.Hash.xxHash/1.0.8": {"runtime": {"lib/net6.0/K4os.Hash.xxHash.dll": {"assemblyVersion": "1.0.8.0", "fileVersion": "1.0.8.0"}}}, "Mapster/7.4.2-pre02": {"dependencies": {"Mapster.Core": "1.2.3-pre02"}, "runtime": {"lib/net9.0/Mapster.dll": {"assemblyVersion": "7.4.2.0", "fileVersion": "7.4.2.0"}}}, "Mapster.Core/1.2.3-pre02": {"runtime": {"lib/net9.0/Mapster.Core.dll": {"assemblyVersion": "1.2.3.0", "fileVersion": "1.2.3.0"}}}, "Masa.BuildingBlocks.Configuration/1.2.0-preview.10": {"dependencies": {"Microsoft.Extensions.Configuration": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.DependencyInjection": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Options": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Options.ConfigurationExtensions": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net10.0/Masa.BuildingBlocks.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Masa.BuildingBlocks.Data/1.2.0-preview.10": {"dependencies": {"Masa.BuildingBlocks.Data.Contracts": "1.2.0-preview.10", "Masa.Utils.Caching.Memory": "1.2.0-preview.10", "Masa.Utils.Extensions.DotNet": "1.2.0-preview.10", "Microsoft.Extensions.DependencyInjection": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net10.0/Masa.BuildingBlocks.Data.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Masa.BuildingBlocks.Data.Contracts/1.2.0-preview.10": {"dependencies": {"Masa.Utils.Models.Config": "1.2.0-preview.10", "Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Options": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net10.0/Masa.BuildingBlocks.Data.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Masa.BuildingBlocks.Development.DaprStarter/1.2.0-preview.10": {"dependencies": {"Masa.BuildingBlocks.Data": "1.2.0-preview.10", "Masa.BuildingBlocks.Exceptions": "1.2.0-preview.10"}, "runtime": {"lib/net10.0/Masa.BuildingBlocks.Development.DaprStarter.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Masa.BuildingBlocks.Exceptions/1.2.0-preview.10": {"dependencies": {"Masa.BuildingBlocks.Data": "1.2.0-preview.10", "Masa.BuildingBlocks.Globalization.I18n": "1.2.0-preview.10", "Masa.Utils.Extensions.DotNet": "1.2.0-preview.10", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net10.0/Masa.BuildingBlocks.Exceptions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Masa.BuildingBlocks.Globalization.I18n/1.2.0-preview.10": {"dependencies": {"Masa.BuildingBlocks.Data": "1.2.0-preview.10"}, "runtime": {"lib/net10.0/Masa.BuildingBlocks.Globalization.I18n.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Masa.Contrib.Development.DaprStarter/1.2.0-preview.10": {"dependencies": {"Masa.BuildingBlocks.Configuration": "1.2.0-preview.10", "Masa.BuildingBlocks.Development.DaprStarter": "1.2.0-preview.10", "Masa.Utils.Extensions.DotNet": "1.2.0-preview.10", "Microsoft.Extensions.Configuration": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.DependencyInjection": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Options": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Options.ConfigurationExtensions": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net10.0/Masa.Contrib.Development.DaprStarter.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Masa.Contrib.Development.DaprStarter.AspNetCore/1.2.0-preview.10": {"dependencies": {"Masa.BuildingBlocks.Exceptions": "1.2.0-preview.10", "Masa.Contrib.Development.DaprStarter": "1.2.0-preview.10"}, "runtime": {"lib/net10.0/Masa.Contrib.Development.DaprStarter.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Masa.Utils.Caching.Memory/1.2.0-preview.10": {"runtime": {"lib/net10.0/Masa.Utils.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Masa.Utils.Extensions.DotNet/1.2.0-preview.10": {"runtime": {"lib/net10.0/Masa.Utils.Extensions.DotNet.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Masa.Utils.Models.Config/1.2.0-preview.10": {"runtime": {"lib/net10.0/Masa.Utils.Models.Config.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/10.0.0-preview.7.25380.108": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.0.1"}, "runtime": {"lib/net10.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.38108"}}}, "Microsoft.AspNetCore.OpenApi/9.0.1": {"dependencies": {"Microsoft.OpenApi": "1.6.24"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.OpenApi.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61009"}}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Bcl.Cryptography/9.0.4": {"runtime": {"lib/net9.0/Microsoft.Bcl.Cryptography.dll": {"assemblyVersion": "9.0.0.4", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Data.SqlClient/6.1.1": {"dependencies": {"Azure.Core": "1.47.1", "Azure.Identity": "1.14.2", "Microsoft.Bcl.Cryptography": "9.0.4", "Microsoft.Data.SqlClient.SNI.runtime": "6.0.2", "Microsoft.IdentityModel.JsonWebTokens": "8.0.1", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.0.1", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.11.25226.3"}}, "resources": {"lib/net9.0/cs/Microsoft.Data.SqlClient.resources.dll": {"locale": "cs"}, "lib/net9.0/de/Microsoft.Data.SqlClient.resources.dll": {"locale": "de"}, "lib/net9.0/es/Microsoft.Data.SqlClient.resources.dll": {"locale": "es"}, "lib/net9.0/fr/Microsoft.Data.SqlClient.resources.dll": {"locale": "fr"}, "lib/net9.0/it/Microsoft.Data.SqlClient.resources.dll": {"locale": "it"}, "lib/net9.0/ja/Microsoft.Data.SqlClient.resources.dll": {"locale": "ja"}, "lib/net9.0/ko/Microsoft.Data.SqlClient.resources.dll": {"locale": "ko"}, "lib/net9.0/pl/Microsoft.Data.SqlClient.resources.dll": {"locale": "pl"}, "lib/net9.0/pt-BR/Microsoft.Data.SqlClient.resources.dll": {"locale": "pt-BR"}, "lib/net9.0/ru/Microsoft.Data.SqlClient.resources.dll": {"locale": "ru"}, "lib/net9.0/tr/Microsoft.Data.SqlClient.resources.dll": {"locale": "tr"}, "lib/net9.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Hans"}, "lib/net9.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Han<PERSON>"}}, "runtimeTargets": {"runtimes/unix/lib/net9.0/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.11.25226.3"}, "runtimes/win/lib/net9.0/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.11.25226.3"}}}, "Microsoft.Data.SqlClient.SNI.runtime/6.0.2": {"runtimeTargets": {"runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*******"}}}, "Microsoft.Extensions.AmbientMetadata.Application/9.8.0": {"dependencies": {"Microsoft.Extensions.Configuration": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Hosting.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Options.ConfigurationExtensions": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net9.0/Microsoft.Extensions.AmbientMetadata.Application.dll": {"assemblyVersion": "*******", "fileVersion": "9.800.25.41206"}}}, "Microsoft.Extensions.Compliance.Abstractions/9.8.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Compliance.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.800.25.41206"}}}, "Microsoft.Extensions.Configuration/10.0.0-preview.7.25380.108": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Primitives": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.38108"}}}, "Microsoft.Extensions.Configuration.Abstractions/10.0.0-preview.7.25380.108": {"dependencies": {"Microsoft.Extensions.Primitives": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.38108"}}}, "Microsoft.Extensions.Configuration.Binder/10.0.0-preview.7.25380.108": {"dependencies": {"Microsoft.Extensions.Configuration": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.38108"}}}, "Microsoft.Extensions.Configuration.CommandLine/10.0.0-preview.7.25380.108": {"dependencies": {"Microsoft.Extensions.Configuration": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.38108"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/10.0.0-preview.7.25380.108": {"dependencies": {"Microsoft.Extensions.Configuration": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.38108"}}}, "Microsoft.Extensions.Configuration.FileExtensions/10.0.0-preview.7.25380.108": {"dependencies": {"Microsoft.Extensions.Configuration": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.FileProviders.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.FileProviders.Physical": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Primitives": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.38108"}}}, "Microsoft.Extensions.Configuration.Json/10.0.0-preview.7.25380.108": {"dependencies": {"Microsoft.Extensions.Configuration": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.FileExtensions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.FileProviders.Abstractions": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.38108"}}}, "Microsoft.Extensions.Configuration.UserSecrets/10.0.0-preview.7.25380.108": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.Json": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.FileProviders.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.FileProviders.Physical": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.38108"}}}, "Microsoft.Extensions.DependencyInjection/10.0.0-preview.7.25380.108": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net10.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.38108"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/10.0.0-preview.7.25380.108": {"runtime": {"lib/net10.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.38108"}}}, "Microsoft.Extensions.DependencyInjection.AutoActivation/9.8.0": {"dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.AutoActivation.dll": {"assemblyVersion": "*******", "fileVersion": "9.800.25.41206"}}}, "Microsoft.Extensions.DependencyModel/9.0.0": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Diagnostics/10.0.0-preview.7.25380.108": {"dependencies": {"Microsoft.Extensions.Configuration": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Diagnostics.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Options.ConfigurationExtensions": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.38108"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/10.0.0-preview.7.25380.108": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Options": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.38108"}}}, "Microsoft.Extensions.Diagnostics.ExceptionSummarization/9.8.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.ExceptionSummarization.dll": {"assemblyVersion": "*******", "fileVersion": "9.800.25.41206"}}}, "Microsoft.Extensions.FileProviders.Abstractions/10.0.0-preview.7.25380.108": {"dependencies": {"Microsoft.Extensions.Primitives": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net10.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.38108"}}}, "Microsoft.Extensions.FileProviders.Physical/10.0.0-preview.7.25380.108": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.FileSystemGlobbing": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Primitives": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net10.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.38108"}}}, "Microsoft.Extensions.FileSystemGlobbing/10.0.0-preview.7.25380.108": {"runtime": {"lib/net10.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.38108"}}}, "Microsoft.Extensions.Hosting/10.0.0-preview.7.25380.108": {"dependencies": {"Microsoft.Extensions.Configuration": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.Binder": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.CommandLine": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.EnvironmentVariables": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.FileExtensions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.Json": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.UserSecrets": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.DependencyInjection": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Diagnostics": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.FileProviders.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.FileProviders.Physical": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Hosting.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.Configuration": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.Console": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.Debug": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.EventLog": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.EventSource": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Options": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Hosting.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.38108"}}}, "Microsoft.Extensions.Hosting.Abstractions/10.0.0-preview.7.25380.108": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Diagnostics.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.FileProviders.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.38108"}}}, "Microsoft.Extensions.Http.Diagnostics/9.8.0": {"dependencies": {"Microsoft.Extensions.Telemetry": "9.8.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "9.800.25.41206"}}}, "Microsoft.Extensions.Http.Resilience/9.8.0": {"dependencies": {"Microsoft.Extensions.Http.Diagnostics": "9.8.0", "Microsoft.Extensions.Resilience": "9.8.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.Resilience.dll": {"assemblyVersion": "*******", "fileVersion": "9.800.25.41206"}}}, "Microsoft.Extensions.Logging/10.0.0-preview.7.25380.108": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Options": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.38108"}}}, "Microsoft.Extensions.Logging.Abstractions/10.0.0-preview.7.25380.108": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.38108"}}}, "Microsoft.Extensions.Logging.Configuration/10.0.0-preview.7.25380.108": {"dependencies": {"Microsoft.Extensions.Configuration": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.Binder": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Options": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Options.ConfigurationExtensions": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.38108"}}}, "Microsoft.Extensions.Logging.Console/10.0.0-preview.7.25380.108": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.Configuration": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Options": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Logging.Console.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.38108"}}}, "Microsoft.Extensions.Logging.Debug/10.0.0-preview.7.25380.108": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Logging.Debug.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.38108"}}}, "Microsoft.Extensions.Logging.EventLog/10.0.0-preview.7.25380.108": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Options": "10.0.0-preview.7.25380.108", "System.Diagnostics.EventLog": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Logging.EventLog.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.38108"}}}, "Microsoft.Extensions.Logging.EventSource/10.0.0-preview.7.25380.108": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Options": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Primitives": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Logging.EventSource.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.38108"}}}, "Microsoft.Extensions.Options/10.0.0-preview.7.25380.108": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Primitives": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.38108"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/10.0.0-preview.7.25380.108": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.Binder": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Options": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Primitives": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.38108"}}}, "Microsoft.Extensions.Primitives/10.0.0-preview.7.25380.108": {"runtime": {"lib/net10.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.38108"}}}, "Microsoft.Extensions.Resilience/9.8.0": {"dependencies": {"Microsoft.Extensions.Diagnostics": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Diagnostics.ExceptionSummarization": "9.8.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Telemetry.Abstractions": "9.8.0", "Polly.Extensions": "8.4.2", "Polly.RateLimiting": "8.4.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Resilience.dll": {"assemblyVersion": "*******", "fileVersion": "9.800.25.41206"}}}, "Microsoft.Extensions.ServiceDiscovery/9.4.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.Binder": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Options": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Primitives": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.ServiceDiscovery.Abstractions": "9.4.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.ServiceDiscovery.dll": {"assemblyVersion": "9.4.1.0", "fileVersion": "9.400.125.40804"}}}, "Microsoft.Extensions.ServiceDiscovery.Abstractions/9.4.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Configuration.Binder": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Options": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Primitives": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net8.0/Microsoft.Extensions.ServiceDiscovery.Abstractions.dll": {"assemblyVersion": "9.4.1.0", "fileVersion": "9.400.125.40804"}}}, "Microsoft.Extensions.Telemetry/9.8.0": {"dependencies": {"Microsoft.Extensions.AmbientMetadata.Application": "9.8.0", "Microsoft.Extensions.DependencyInjection.AutoActivation": "9.8.0", "Microsoft.Extensions.Logging.Configuration": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Telemetry.Abstractions": "9.8.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Telemetry.dll": {"assemblyVersion": "*******", "fileVersion": "9.800.25.41206"}}}, "Microsoft.Extensions.Telemetry.Abstractions/9.8.0": {"dependencies": {"Microsoft.Extensions.Compliance.Abstractions": "9.8.0", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Options": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Telemetry.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.800.25.41206"}}}, "Microsoft.Identity.Client/4.73.1": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.0.1"}, "runtime": {"lib/net8.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.73.1.0", "fileVersion": "4.73.1.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.73.1": {"dependencies": {"Microsoft.Identity.Client": "4.73.1", "System.Security.Cryptography.ProtectedData": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "4.73.1.0", "fileVersion": "4.73.1.0"}}}, "Microsoft.IdentityModel.Abstractions/8.0.1": {"runtime": {"lib/net9.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.JsonWebTokens/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Logging/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Protocols/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Protocols": "8.0.1", "System.IdentityModel.Tokens.Jwt": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Tokens/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Logging": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "Microsoft.OpenApi/1.6.24": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.SqlServer.Server/1.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MiniProfiler.AspNetCore/4.5.4": {"dependencies": {"MiniProfiler.Shared": "4.5.4"}, "runtime": {"lib/net8.0/MiniProfiler.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "4.5.4.47516"}}}, "MiniProfiler.AspNetCore.Mvc/4.5.4": {"dependencies": {"MiniProfiler.AspNetCore": "4.5.4"}, "runtime": {"lib/net8.0/MiniProfiler.AspNetCore.Mvc.dll": {"assemblyVersion": "*******", "fileVersion": "4.5.4.47516"}}}, "MiniProfiler.Shared/4.5.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net8.0/MiniProfiler.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "4.5.4.47516"}}}, "MySql.Data/9.1.0": {"dependencies": {"BouncyCastle.Cryptography": "2.3.1", "Google.Protobuf": "3.31.1", "K4os.Compression.LZ4.Streams": "1.3.8", "System.Configuration.ConfigurationManager": "9.0.4", "System.Security.Permissions": "8.0.0", "ZstdSharp.Port": "0.8.0"}, "runtime": {"lib/net8.0/MySql.Data.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "runtimeTargets": {"runtimes/win-x64/native/comerr64.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/gssapi64.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/k5sprt64.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/krb5_64.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/krbcc64.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}}}, "NetTopologySuite/2.0.0": {"runtime": {"lib/netstandard2.0/NetTopologySuite.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.0.0"}}}, "NetTopologySuite.IO.PostGis/2.1.0": {"dependencies": {"NetTopologySuite": "2.0.0"}, "runtime": {"lib/netstandard2.1/NetTopologySuite.IO.PostGis.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.0.0"}}}, "Newtonsoft.Json/13.0.1": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "1*******", "fileVersion": "13.0.1.25517"}}}, "Npgsql/9.0.3": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.7.25380.108"}, "runtime": {"lib/net8.0/Npgsql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Npgsql.LegacyPostgis/5.0.18": {"dependencies": {"Npgsql": "9.0.3"}, "runtime": {"lib/netstandard2.0/Npgsql.LegacyPostgis.dll": {"assemblyVersion": "5.0.18.0", "fileVersion": "5.0.18.0"}}}, "Npgsql.NetTopologySuite/5.0.18": {"dependencies": {"NetTopologySuite.IO.PostGis": "2.1.0", "Npgsql": "9.0.3"}, "runtime": {"lib/netstandard2.0/Npgsql.NetTopologySuite.dll": {"assemblyVersion": "5.0.18.0", "fileVersion": "5.0.18.0"}}}, "OpenTelemetry/1.12.0": {"dependencies": {"Microsoft.Extensions.Diagnostics.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.Configuration": "10.0.0-preview.7.25380.108", "OpenTelemetry.Api.ProviderBuilderExtensions": "1.12.0"}, "runtime": {"lib/net9.0/OpenTelemetry.dll": {"assemblyVersion": "*******", "fileVersion": "1.12.0.1644"}}}, "OpenTelemetry.Api/1.12.0": {"runtime": {"lib/net9.0/OpenTelemetry.Api.dll": {"assemblyVersion": "*******", "fileVersion": "1.12.0.1644"}}}, "OpenTelemetry.Api.ProviderBuilderExtensions/1.12.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108", "OpenTelemetry.Api": "1.12.0"}, "runtime": {"lib/net9.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "1.12.0.1644"}}}, "OpenTelemetry.Exporter.OpenTelemetryProtocol/1.12.0": {"dependencies": {"OpenTelemetry": "1.12.0"}, "runtime": {"lib/net9.0/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll": {"assemblyVersion": "*******", "fileVersion": "1.12.0.1644"}}}, "OpenTelemetry.Exporter.Zipkin/1.12.0": {"dependencies": {"OpenTelemetry": "1.12.0"}, "runtime": {"lib/net9.0/OpenTelemetry.Exporter.Zipkin.dll": {"assemblyVersion": "*******", "fileVersion": "1.12.0.1644"}}}, "OpenTelemetry.Extensions.Hosting/1.12.0": {"dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "10.0.0-preview.7.25380.108", "OpenTelemetry": "1.12.0"}, "runtime": {"lib/net9.0/OpenTelemetry.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "1.12.0.1644"}}}, "OpenTelemetry.Instrumentation.AspNetCore/1.12.0": {"dependencies": {"OpenTelemetry.Api.ProviderBuilderExtensions": "1.12.0"}, "runtime": {"lib/net8.0/OpenTelemetry.Instrumentation.AspNetCore.dll": {"assemblyVersion": "1.12.0.490", "fileVersion": "1.12.0.490"}}}, "OpenTelemetry.Instrumentation.GrpcCore/1.0.0-beta.6": {"dependencies": {"Google.Protobuf": "3.31.1", "Grpc.Core.Api": "2.71.0", "OpenTelemetry.Api": "1.12.0"}, "runtime": {"lib/netstandard2.0/OpenTelemetry.Instrumentation.GrpcCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "OpenTelemetry.Instrumentation.GrpcNetClient/1.12.0-beta.1": {"dependencies": {"OpenTelemetry": "1.12.0"}, "runtime": {"lib/net8.0/OpenTelemetry.Instrumentation.GrpcNetClient.dll": {"assemblyVersion": "1.12.0.492", "fileVersion": "1.12.0.492"}}}, "OpenTelemetry.Instrumentation.Http/1.12.0": {"dependencies": {"Microsoft.Extensions.Configuration": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Options": "10.0.0-preview.7.25380.108", "OpenTelemetry.Api.ProviderBuilderExtensions": "1.12.0"}, "runtime": {"lib/net8.0/OpenTelemetry.Instrumentation.Http.dll": {"assemblyVersion": "1.12.0.493", "fileVersion": "1.12.0.493"}}}, "OpenTelemetry.Instrumentation.Process/1.12.0-beta.1": {"dependencies": {"OpenTelemetry.Api": "1.12.0"}, "runtime": {"lib/netstandard2.0/OpenTelemetry.Instrumentation.Process.dll": {"assemblyVersion": "1.12.0.494", "fileVersion": "1.12.0.494"}}}, "OpenTelemetry.Instrumentation.Runtime/1.12.0": {"dependencies": {"OpenTelemetry.Api": "1.12.0"}, "runtime": {"lib/net8.0/OpenTelemetry.Instrumentation.Runtime.dll": {"assemblyVersion": "1.12.0.496", "fileVersion": "1.12.0.496"}}}, "Oracle.ManagedDataAccess.Core/23.6.1": {"dependencies": {"System.Diagnostics.PerformanceCounter": "8.0.0", "System.DirectoryServices.Protocols": "8.0.0"}, "runtime": {"lib/net8.0/Oracle.ManagedDataAccess.dll": {"assemblyVersion": "23.1.0.0", "fileVersion": "23.1.0.0"}}}, "Polly.Core/8.4.2": {"runtime": {"lib/net8.0/Polly.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.2.3950"}}}, "Polly.Extensions/8.4.2": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Options": "10.0.0-preview.7.25380.108", "Polly.Core": "8.4.2"}, "runtime": {"lib/net8.0/Polly.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.2.3950"}}}, "Polly.RateLimiting/8.4.2": {"dependencies": {"Polly.Core": "8.4.2"}, "runtime": {"lib/net8.0/Polly.RateLimiting.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.2.3950"}}}, "Scalar.AspNetCore/2.6.9": {"runtime": {"lib/net9.0/Scalar.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog/4.3.1-dev-02373": {"runtime": {"lib/net9.0/Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.AspNetCore/9.0.0": {"dependencies": {"Serilog": "4.3.1-dev-02373", "Serilog.Extensions.Hosting": "9.0.1-dev-02307", "Serilog.Formatting.Compact": "3.0.0", "Serilog.Settings.Configuration": "9.0.1-dev-02317", "Serilog.Sinks.Console": "6.0.1-dev-00953", "Serilog.Sinks.Debug": "3.0.0", "Serilog.Sinks.File": "6.0.0"}, "runtime": {"lib/net9.0/Serilog.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Enrichers.Environment/3.0.1": {"dependencies": {"Serilog": "4.3.1-dev-02373"}, "runtime": {"lib/net8.0/Serilog.Enrichers.Environment.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Enrichers.Thread/4.0.0": {"dependencies": {"Serilog": "4.3.1-dev-02373"}, "runtime": {"lib/net8.0/Serilog.Enrichers.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Expressions/5.1.0-dev-02301": {"dependencies": {"Serilog": "4.3.1-dev-02373"}, "runtime": {"lib/net9.0/Serilog.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Hosting/9.0.1-dev-02307": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Hosting.Abstractions": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.7.25380.108", "Serilog": "4.3.1-dev-02373", "Serilog.Extensions.Logging": "9.0.3-dev-02320"}, "runtime": {"lib/net9.0/Serilog.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Logging/9.0.3-dev-02320": {"dependencies": {"Microsoft.Extensions.Logging": "10.0.0-preview.7.25380.108", "Serilog": "4.3.1-dev-02373"}, "runtime": {"lib/net9.0/Serilog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Formatting.Compact/3.0.0": {"dependencies": {"Serilog": "4.3.1-dev-02373"}, "runtime": {"lib/net8.0/Serilog.Formatting.Compact.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Settings.Configuration/9.0.1-dev-02317": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "10.0.0-preview.7.25380.108", "Microsoft.Extensions.DependencyModel": "9.0.0", "Serilog": "4.3.1-dev-02373"}, "runtime": {"lib/net9.0/Serilog.Settings.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Console/6.0.1-dev-00953": {"dependencies": {"Serilog": "4.3.1-dev-02373"}, "runtime": {"lib/net8.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "6.0.1.0", "fileVersion": "6.0.1.0"}}}, "Serilog.Sinks.Debug/3.0.0": {"dependencies": {"Serilog": "4.3.1-dev-02373"}, "runtime": {"lib/net8.0/Serilog.Sinks.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.File/6.0.0": {"dependencies": {"Serilog": "4.3.1-dev-02373"}, "runtime": {"lib/net8.0/Serilog.Sinks.File.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.0.0"}}}, "Serilog.Sinks.OpenTelemetry/4.2.1-dev-02306": {"dependencies": {"Google.Protobuf": "3.31.1", "Grpc.Net.Client": "2.71.0", "Serilog": "4.3.1-dev-02373"}, "runtime": {"lib/net9.0/Serilog.Sinks.OpenTelemetry.dll": {"assemblyVersion": "4.2.1.0", "fileVersion": "4.2.1.0"}}}, "Serilog.Sinks.Seq/9.0.0": {"dependencies": {"Serilog": "4.3.1-dev-02373", "Serilog.Sinks.File": "6.0.0"}, "runtime": {"lib/net6.0/Serilog.Sinks.Seq.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SixLabors.ImageSharp/3.1.11": {"runtime": {"lib/net6.0/SixLabors.ImageSharp.dll": {"assemblyVersion": "*******", "fileVersion": "3.1.11.0"}}}, "Stub.System.Data.SQLite.Core.NetStandard/1.0.119": {"runtime": {"lib/netstandard2.1/System.Data.SQLite.dll": {"assemblyVersion": "1.0.119.0", "fileVersion": "1.0.119.0"}}, "runtimeTargets": {"runtimes/linux-x64/native/SQLite.Interop.dll": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/SQLite.Interop.dll": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/SQLite.Interop.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.0.119.0"}, "runtimes/win-x86/native/SQLite.Interop.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.0.119.0"}}}, "Swashbuckle.AspNetCore/9.0.3": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "9.0.3", "Swashbuckle.AspNetCore.SwaggerGen": "9.0.3", "Swashbuckle.AspNetCore.SwaggerUI": "9.0.3"}}, "Swashbuckle.AspNetCore.Swagger/9.0.3": {"dependencies": {"Microsoft.OpenApi": "1.6.24"}, "runtime": {"lib/net9.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.3.1613"}}}, "Swashbuckle.AspNetCore.SwaggerGen/9.0.3": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "9.0.3"}, "runtime": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.3.1613"}}}, "Swashbuckle.AspNetCore.SwaggerUI/9.0.3": {"runtime": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.3.1613"}}}, "System.ClientModel/1.5.1": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.7.25380.108", "System.Memory.Data": "8.0.1"}, "runtime": {"lib/net8.0/System.ClientModel.dll": {"assemblyVersion": "1.5.1.0", "fileVersion": "1.500.125.36405"}}}, "System.Configuration.ConfigurationManager/9.0.4": {"dependencies": {"System.Diagnostics.EventLog": "10.0.0-preview.7.25380.108", "System.Security.Cryptography.ProtectedData": "9.0.4"}, "runtime": {"lib/net9.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "System.Data.Odbc/8.0.0": {"runtime": {"lib/net8.0/System.Data.Odbc.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/freebsd/lib/net8.0/System.Data.Odbc.dll": {"rid": "freebsd", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "runtimes/illumos/lib/net8.0/System.Data.Odbc.dll": {"rid": "illumos", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "runtimes/ios/lib/net8.0/System.Data.Odbc.dll": {"rid": "ios", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "runtimes/linux/lib/net8.0/System.Data.Odbc.dll": {"rid": "linux", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "runtimes/osx/lib/net8.0/System.Data.Odbc.dll": {"rid": "osx", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "runtimes/solaris/lib/net8.0/System.Data.Odbc.dll": {"rid": "solaris", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "runtimes/tvos/lib/net8.0/System.Data.Odbc.dll": {"rid": "tvos", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "runtimes/win/lib/net8.0/System.Data.Odbc.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Data.OleDb/6.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.4", "System.Diagnostics.PerformanceCounter": "8.0.0"}, "runtime": {"lib/net6.0/System.Data.OleDb.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Data.OleDb.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "System.Data.SQLite.Core/1.0.119": {"dependencies": {"Stub.System.Data.SQLite.Core.NetStandard": "1.0.119"}}, "System.Diagnostics.EventLog/10.0.0-preview.7.25380.108": {"runtime": {"lib/net10.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.38108"}}, "runtimeTargets": {"runtimes/win/lib/net10.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.38108"}}}, "System.Diagnostics.PerformanceCounter/8.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.4"}, "runtime": {"lib/net8.0/System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.PerformanceCounter.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.DirectoryServices.Protocols/8.0.0": {"runtime": {"lib/net8.0/System.DirectoryServices.Protocols.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/linux/lib/net8.0/System.DirectoryServices.Protocols.dll": {"rid": "linux", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "runtimes/osx/lib/net8.0/System.DirectoryServices.Protocols.dll": {"rid": "osx", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "runtimes/win/lib/net8.0/System.DirectoryServices.Protocols.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.IdentityModel.Tokens.Jwt/8.0.1": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.0.1", "Microsoft.IdentityModel.Tokens": "8.0.1"}, "runtime": {"lib/net9.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "System.Memory.Data/8.0.1": {"runtime": {"lib/net8.0/System.Memory.Data.dll": {"assemblyVersion": "8.0.0.1", "fileVersion": "8.0.1024.46610"}}}, "System.Security.Cryptography.ProtectedData/9.0.4": {"runtime": {"lib/net9.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "System.Security.Permissions/8.0.0": {"dependencies": {"System.Windows.Extensions": "8.0.0"}, "runtime": {"lib/net8.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Windows.Extensions/8.0.0": {"runtime": {"lib/net8.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "WorkQueue/1.3.0": {"runtime": {"lib/netstandard2.0/WorkQueue.dll": {"assemblyVersion": "1.3.0.0", "fileVersion": "1.3.0.0"}}}, "ZstdSharp.Port/0.8.0": {"runtime": {"lib/net8.0/ZstdSharp.dll": {"assemblyVersion": "0.8.0.0", "fileVersion": "0.8.0.0"}}}, "SSIC.Entity/1.0.0": {"dependencies": {"FreeSql": "3.5.213-preview20250815"}, "runtime": {"SSIC.Entity.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"SSIC.Infrastructure/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Azure.Core/1.47.1": {"type": "package", "serviceable": true, "sha512": "sha512-oPcncSsDHuxB8SC522z47xbp2+ttkcKv2YZ90KXhRKN0YQd2+7l1UURT9EBzUNEXtkLZUOAB5xbByMTrYRh3yA==", "path": "azure.core/1.47.1", "hashPath": "azure.core.1.47.1.nupkg.sha512"}, "Azure.Identity/1.14.2": {"type": "package", "serviceable": true, "sha512": "sha512-YhNMwOTwT+I2wIcJKSdP0ADyB2aK+JaYWZxO8LSRDm5w77LFr0ykR9xmt2ZV5T1gaI7xU6iNFIh/yW1dAlpddQ==", "path": "azure.identity/1.14.2", "hashPath": "azure.identity.1.14.2.nupkg.sha512"}, "BouncyCastle.Cryptography/2.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-buwoISwecYke3CmgG1AQSg+sNZjJeIb93vTAtJiHZX35hP/teYMxsfg0NDXGUKjGx6BKBTNKc77O2M3vKvlXZQ==", "path": "bouncycastle.cryptography/2.3.1", "hashPath": "bouncycastle.cryptography.2.3.1.nupkg.sha512"}, "Dapr.AspNetCore/1.16.0-rc13": {"type": "package", "serviceable": true, "sha512": "sha512-BaHIUgBlwwSDYoTgDg+i0forhUaqIUgcqAvVFK0UiD7IRqnbSW9Fb7At81Q2K3cEjX+xdPUG62rdTLDKOQU5bA==", "path": "dapr.aspnetcore/1.16.0-rc13", "hashPath": "dapr.aspnetcore.1.16.0-rc13.nupkg.sha512"}, "Dapr.Client/1.16.0-rc13": {"type": "package", "serviceable": true, "sha512": "sha512-B/SepgLIhnvbb1VNyrLr6aaLbtgVmVCCcvOJj6CYaEAhG7u4+VHN6YQ6b8Skxtmx+wZwtu8OUodn2L8XWz0XKg==", "path": "dapr.client/1.16.0-rc13", "hashPath": "dapr.client.1.16.0-rc13.nupkg.sha512"}, "Dapr.Common/1.16.0-rc13": {"type": "package", "serviceable": true, "sha512": "sha512-hcvop0/wCNJzkcJyoMngplKx0htlf6ZwZLBWZB/XE3glkFq5mDB0a1XLt+ARifhH0r4CqwyBJBiGL5ahIDbK6Q==", "path": "dapr.common/1.16.0-rc13", "hashPath": "dapr.common.1.16.0-rc13.nupkg.sha512"}, "Dapr.Protos/1.16.0-rc13": {"type": "package", "serviceable": true, "sha512": "sha512-jIbSQLuQoJhwVsM6jzHtj7DFsuj+jUOXRU4MYg/Qu5GF2eGdoVnBZQI88NhJTk4yCFqOGTjKoCKGKyHFhKn0aQ==", "path": "dapr.protos/1.16.0-rc13", "hashPath": "dapr.protos.1.16.0-rc13.nupkg.sha512"}, "DM.DmProvider/8.3.1.28188": {"type": "package", "serviceable": true, "sha512": "sha512-yd8bw6ClaoP5vAAPQ6BHiRqFqB41lKXyvzgKu05YfFQkiDzK/RQQBj7jmgWkeTFt1cxJvebHv//xBGfnHa2gnw==", "path": "dm.d<PERSON><PERSON><PERSON>/8.3.1.28188", "hashPath": "dm.dmprovider.8.3.1.28188.nupkg.sha512"}, "FreeRedis/1.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-E74KyJwkyjh+T8HgbvP5wlRFXRW11bn7BXms5eeh5l6HRGhRP0+Vitl8NtSsqjYSTgylPow7PeRP7rIpcDHplQ==", "path": "freeredis/1.4.0", "hashPath": "freeredis.1.4.0.nupkg.sha512"}, "FreeScheduler/2.0.36": {"type": "package", "serviceable": true, "sha512": "sha512-nGbiHwA2EwRiB9xmwxiHZROyTyqEh9SNkdlNZWKpoOKrPXu6jkie6/2c6Rn2xQckIt489ODoQwHukmBYKUJzjg==", "path": "freescheduler/2.0.36", "hashPath": "freescheduler.2.0.36.nupkg.sha512"}, "FreeSql/3.5.213-preview20250815": {"type": "package", "serviceable": true, "sha512": "sha512-3w+ngNJypO1smTww/JIDtC2qlEcdLJ/nYGwvsCtR5hYonTuektKQCms9uXPcYt7to2B0zPbubKqicYsclyzUtA==", "path": "freesql/3.5.213-preview20250815", "hashPath": "freesql.3.5.213-preview20250815.nupkg.sha512"}, "FreeSql.All/3.5.213-preview20250815": {"type": "package", "serviceable": true, "sha512": "sha512-bIKNKwyqmZCBN+7Gen7NU0AWcn04vt3OYmNQS5J0EFpTgbTP5tkSrlOTSqdaSDkc3jOfffIo53xzNVJc2T0t4A==", "path": "freesql.all/3.5.213-preview20250815", "hashPath": "freesql.all.3.5.213-preview20250815.nupkg.sha512"}, "FreeSql.Cloud/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Mof1EcdfQLPGnerhbDaDiWQ1maRBSXC3Pm0DvGxsPvqpMm6Zxeh1Lgvh8+ubBjFzXLZATgqgb1c5S+VX1QOjBA==", "path": "freesql.cloud/2.0.1", "hashPath": "freesql.cloud.2.0.1.nupkg.sha512"}, "FreeSql.DbContext/3.5.213-preview20250815": {"type": "package", "serviceable": true, "sha512": "sha512-lLJqnMpjfknY0pbDCs4XUL/xHIdQTtdtbNoGH84lMd472im0EjsG0ehAQmIk2LiRefWwMp9U8zk0buv7YgcnEQ==", "path": "freesql.dbcontext/3.5.213-preview20250815", "hashPath": "freesql.dbcontext.3.5.213-preview20250815.nupkg.sha512"}, "FreeSql.Provider.Dameng/3.5.213-preview20250815": {"type": "package", "serviceable": true, "sha512": "sha512-CcYGxHoBdsw7u5X4x377TKngFK6YulRcyWMTwAcW9Tv+whhQcVaHEIZKJJ6zazUCDs5GtKHKiQATvALpIXOkkw==", "path": "freesql.provider.dameng/3.5.213-preview20250815", "hashPath": "freesql.provider.dameng.3.5.213-preview20250815.nupkg.sha512"}, "FreeSql.Provider.MsAccess/3.5.213-preview20250815": {"type": "package", "serviceable": true, "sha512": "sha512-7W40a2CQ75h2a86G3J4J6koiZQd8i21FBNj9LHO4U3A+U+c91jjAA6U3vIGFto48DvihCvucESPGHjI5umYQxg==", "path": "freesql.provider.msaccess/3.5.213-preview20250815", "hashPath": "freesql.provider.msaccess.3.5.213-preview20250815.nupkg.sha512"}, "FreeSql.Provider.MySql/3.5.213-preview20250815": {"type": "package", "serviceable": true, "sha512": "sha512-H2erqJ3XkZHk2cfPhWi3rGg0HMJpM8Cw3tmGwz+KICjMfO8BnvGTAgcZEDOQPlTvkvuN8miM/EQ7yOl/m0iqMw==", "path": "freesql.provider.mysql/3.5.213-preview20250815", "hashPath": "freesql.provider.mysql.3.5.213-preview20250815.nupkg.sha512"}, "FreeSql.Provider.Odbc/3.5.213-preview20250815": {"type": "package", "serviceable": true, "sha512": "sha512-eKoK7D4Z3btug70g+lp8i93i1m05IHKu9Yvl9Ay/Vd+td/d/LlNDhThdvv4tTyFcJUPfOUnblTS3Hdaj8D9aXg==", "path": "freesql.provider.odbc/3.5.213-preview20250815", "hashPath": "freesql.provider.odbc.3.5.213-preview20250815.nupkg.sha512"}, "FreeSql.Provider.Oracle/3.5.213-preview20250815": {"type": "package", "serviceable": true, "sha512": "sha512-iflMJRaUcUuoV1r4Uj0gfnKFX86R5yCHlXpt/ynOKFtTQ1hOz4QZyiDr1DX0k3Tk3BwxWG5A2/ZG3FZCLCpKrA==", "path": "freesql.provider.oracle/3.5.213-preview20250815", "hashPath": "freesql.provider.oracle.3.5.213-preview20250815.nupkg.sha512"}, "FreeSql.Provider.PostgreSQL/3.5.213-preview20250815": {"type": "package", "serviceable": true, "sha512": "sha512-XFKhZcW10lCRCwArtlPRJqKOtefdtQHUOmOXS+QuL0cPfzIXbPXaSMjNTEz7DGvqFRATa/dVEuzr8fYkTNPYHA==", "path": "freesql.provider.postgresql/3.5.213-preview20250815", "hashPath": "freesql.provider.postgresql.3.5.213-preview20250815.nupkg.sha512"}, "FreeSql.Provider.Sqlite/3.5.213-preview20250815": {"type": "package", "serviceable": true, "sha512": "sha512-SvJOGiswHlnyRFtlQ7HI1K9NJOuKb1oBq13RB74a0BuwXcYp3ufhkqdRLuel9lAj1PHRu4/EGuJZvy353VbgKQ==", "path": "freesql.provider.sqlite/3.5.213-preview20250815", "hashPath": "freesql.provider.sqlite.3.5.213-preview20250815.nupkg.sha512"}, "FreeSql.Provider.SqlServer/3.5.213-preview20250815": {"type": "package", "serviceable": true, "sha512": "sha512-ZIGHM6wBqsh81lw0h3wI8W/nvEnv66IuAB/5zjR3TM1YroOmjJHBVDHPahSBKeXzqlkiR8umYUIOoAHueXpUZg==", "path": "freesql.provider.sqlserver/3.5.213-preview20250815", "hashPath": "freesql.provider.sqlserver.3.5.213-preview20250815.nupkg.sha512"}, "FreeSql.Repository/3.5.213-preview20250815": {"type": "package", "serviceable": true, "sha512": "sha512-k4+u1taPKgZfoWPLyJbMsWXqziBQu2mlizMT5h7uXBKhZ0HQA7QviJolS+2BX52lsEy2aX1HfjyMjPYd1BpAYA==", "path": "freesql.repository/3.5.213-preview20250815", "hashPath": "freesql.repository.3.5.213-preview20250815.nupkg.sha512"}, "Google.Api.CommonProtos/2.17.0": {"type": "package", "serviceable": true, "sha512": "sha512-elfQPknFr495hm7vdy6ZlgyQh6yzZq9TU7sS35L/Fj/fqjM/mUGau9gVJLhvQEtUlPjtR80hpn/m9HvBMyCXIw==", "path": "google.api.commonprotos/2.17.0", "hashPath": "google.api.commonprotos.2.17.0.nupkg.sha512"}, "Google.Protobuf/3.31.1": {"type": "package", "serviceable": true, "sha512": "sha512-gSnJbUmGiOTdWddPhqzrEscHq9Ls6sqRDPB9WptckyjTUyx70JOOAaDLkFff8gManZNN3hllQ4aQInnQyq/Z/A==", "path": "google.protobuf/3.31.1", "hashPath": "google.protobuf.3.31.1.nupkg.sha512"}, "Grpc.Core.Api/2.71.0": {"type": "package", "serviceable": true, "sha512": "sha512-QquqUC37yxsDzd1QaDRsH2+uuznWPTS8CVE2Yzwl3CvU4geTNkolQXoVN812M2IwT6zpv3jsZRc9ExJFNFslTg==", "path": "grpc.core.api/2.71.0", "hashPath": "grpc.core.api.2.71.0.nupkg.sha512"}, "Grpc.Net.Client/2.71.0": {"type": "package", "serviceable": true, "sha512": "sha512-U1vr20r5ngoT9nlb7wejF28EKN+taMhJsV9XtK9MkiepTZwnKxxiarriiMfCHuDAfPUm9XUjFMn/RIuJ4YY61w==", "path": "grpc.net.client/2.71.0", "hashPath": "grpc.net.client.2.71.0.nupkg.sha512"}, "Grpc.Net.Common/2.71.0": {"type": "package", "serviceable": true, "sha512": "sha512-v0c8R97TwRYwNXlC8GyRXwYTCNufpDfUtj9la+wUrZFzVWkFJuNAltU+c0yI3zu0jl54k7en6u2WKgZgd57r2Q==", "path": "grpc.net.common/2.71.0", "hashPath": "grpc.net.common.2.71.0.nupkg.sha512"}, "IdleBus/1.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-jMXWcNXGsUrES2QYyrygKx4YFNsGX0NrYHcMVK5lNH4L8UB8RltLb/SXMPLjVzh+usXXRXcEJTqLNZgy+rq/xw==", "path": "idlebus/1.5.3", "hashPath": "idlebus.1.5.3.nupkg.sha512"}, "K4os.Compression.LZ4/1.3.8": {"type": "package", "serviceable": true, "sha512": "sha512-LhwlPa7c1zs1OV2XadMtAWdImjLIsqFJPoRcIWAadSRn0Ri1DepK65UbWLPmt4riLqx2d40xjXRk0ogpqNtK7g==", "path": "k4os.compression.lz4/1.3.8", "hashPath": "k4os.compression.lz4.1.3.8.nupkg.sha512"}, "K4os.Compression.LZ4.Streams/1.3.8": {"type": "package", "serviceable": true, "sha512": "sha512-P15qr8dZAeo9GvYbUIPEYFQ0MEJ0i5iqr37wsYeRC3la2uCldOoeCa6to0CZ1taiwxIV+Mk8NGuZi+4iWivK9w==", "path": "k4os.compression.lz4.streams/1.3.8", "hashPath": "k4os.compression.lz4.streams.1.3.8.nupkg.sha512"}, "K4os.Hash.xxHash/1.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-Wp2F7BamQ2Q/7Hk834nV9vRQapgcr8kgv9Jvfm8J3D0IhDqZMMl+a2yxUq5ltJitvXvQfB8W6K4F4fCbw/P6YQ==", "path": "k4os.hash.xxhash/1.0.8", "hashPath": "k4os.hash.xxhash.1.0.8.nupkg.sha512"}, "Mapster/7.4.2-pre02": {"type": "package", "serviceable": true, "sha512": "sha512-ry6YPGImX4aL0+Jj0rn46vqYOxzeyKOdlr6LUPvby1NLc68E5T2gRhDVWxvFexgZGOIW/U8NM87kygE4xl7Qvw==", "path": "mapster/7.4.2-pre02", "hashPath": "mapster.7.4.2-pre02.nupkg.sha512"}, "Mapster.Core/1.2.3-pre02": {"type": "package", "serviceable": true, "sha512": "sha512-FDVPfGvCz1RRb1PYeoddiJftxo+ANAifiaqkZbH2kijZT2GcoR1w6byLQ8Czc9YBuNZqHHV4LKeXZA+eoFuCTw==", "path": "mapster.core/1.2.3-pre02", "hashPath": "mapster.core.1.2.3-pre02.nupkg.sha512"}, "Masa.BuildingBlocks.Configuration/1.2.0-preview.10": {"type": "package", "serviceable": true, "sha512": "sha512-v88mphyr/htxFUkT0vW2vgj1/sLGnXt1kl/K2cWVPEC5NswVHM3l7Jq9cWpzloeVTg/8GjjNHzb8IpuOzedHvg==", "path": "masa.buildingblocks.configuration/1.2.0-preview.10", "hashPath": "masa.buildingblocks.configuration.1.2.0-preview.10.nupkg.sha512"}, "Masa.BuildingBlocks.Data/1.2.0-preview.10": {"type": "package", "serviceable": true, "sha512": "sha512-tWug8fTJotWKttvha1g1ZEmy/FcLmWDKDLuHMTMktjgISJRw/EOHjLRRzvKwK0FIbzo7fQIBTchdMHh++nK25w==", "path": "masa.buildingblocks.data/1.2.0-preview.10", "hashPath": "masa.buildingblocks.data.1.2.0-preview.10.nupkg.sha512"}, "Masa.BuildingBlocks.Data.Contracts/1.2.0-preview.10": {"type": "package", "serviceable": true, "sha512": "sha512-m7tI1FwV073FfL5H5Ee6l2+Wq5errGriSI3y03JexVF9t3ecYLXeiw3vigcwofSv/jFJJb021nLqz86C6XL/uw==", "path": "masa.buildingblocks.data.contracts/1.2.0-preview.10", "hashPath": "masa.buildingblocks.data.contracts.1.2.0-preview.10.nupkg.sha512"}, "Masa.BuildingBlocks.Development.DaprStarter/1.2.0-preview.10": {"type": "package", "serviceable": true, "sha512": "sha512-I2Ibpft/LYI85jwXDjopNF4xufsGqO8fcV9Mh3ap8Zq/TBsqJfiO+2oHLhwv0H5AabsAH4coKcHk/WgGPKyakg==", "path": "masa.buildingblocks.development.daprstarter/1.2.0-preview.10", "hashPath": "masa.buildingblocks.development.daprstarter.1.2.0-preview.10.nupkg.sha512"}, "Masa.BuildingBlocks.Exceptions/1.2.0-preview.10": {"type": "package", "serviceable": true, "sha512": "sha512-RUuLq9BToJcEHAm6pzgMrGT2sS/E07J12v9RBTSGBzuKuJW+5ClbMLKIQ6uhn9szrcZEOemuGZOeuAQroKVtsw==", "path": "masa.buildingblocks.exceptions/1.2.0-preview.10", "hashPath": "masa.buildingblocks.exceptions.1.2.0-preview.10.nupkg.sha512"}, "Masa.BuildingBlocks.Globalization.I18n/1.2.0-preview.10": {"type": "package", "serviceable": true, "sha512": "sha512-4pe+6v0kT8ajAiilDizteiO8XiUwFpn5CrP9nP6QPFxhKv+R2WXZjFcqXhlVGBg+Dh9sAUchYdPX/6PCtdNsng==", "path": "masa.buildingblocks.globalization.i18n/1.2.0-preview.10", "hashPath": "masa.buildingblocks.globalization.i18n.1.2.0-preview.10.nupkg.sha512"}, "Masa.Contrib.Development.DaprStarter/1.2.0-preview.10": {"type": "package", "serviceable": true, "sha512": "sha512-Zb8g9m9V04FJFxdgoj4zg1irVhnKyDjAQqU+nikf1e66Tomcx9voqKpVofJ7sRy7RvZLz7G1O18FcTXu/UcvfQ==", "path": "masa.contrib.development.daprstarter/1.2.0-preview.10", "hashPath": "masa.contrib.development.daprstarter.1.2.0-preview.10.nupkg.sha512"}, "Masa.Contrib.Development.DaprStarter.AspNetCore/1.2.0-preview.10": {"type": "package", "serviceable": true, "sha512": "sha512-qq3N9nTH+/GtCcXmmMGhSvdKbPSC6p1LJc0TxS0vgSNXULcSw+znxIwjzemIkQRmK+6uXpsuOo4ZGClmIz0cRg==", "path": "masa.contrib.development.daprstarter.aspnetcore/1.2.0-preview.10", "hashPath": "masa.contrib.development.daprstarter.aspnetcore.1.2.0-preview.10.nupkg.sha512"}, "Masa.Utils.Caching.Memory/1.2.0-preview.10": {"type": "package", "serviceable": true, "sha512": "sha512-zpE6V1CzNgzXzHpJVjJowTNs5A7bF5E3RJ+JsZ4/YDLYj52D+h1G8Qz41z8pK/sDdJANI2hiXCAjqaLWHlwX1Q==", "path": "masa.utils.caching.memory/1.2.0-preview.10", "hashPath": "masa.utils.caching.memory.1.2.0-preview.10.nupkg.sha512"}, "Masa.Utils.Extensions.DotNet/1.2.0-preview.10": {"type": "package", "serviceable": true, "sha512": "sha512-8H6g8V8oHWQwGjDgi5CpM5ObI4xBfBzziQEvK4cgBq81us4oqP1QqKktAF4YUCr9Ld7aA0W4cwys49t/TUJy7w==", "path": "masa.utils.extensions.dotnet/1.2.0-preview.10", "hashPath": "masa.utils.extensions.dotnet.1.2.0-preview.10.nupkg.sha512"}, "Masa.Utils.Models.Config/1.2.0-preview.10": {"type": "package", "serviceable": true, "sha512": "sha512-MIjltbUH6sBov06Y3+WWBeYKjBZSPiiWjeaVOKyYBn5uWeEhSEYJenSHUKnSPZYTI7d3HDWRjJ9AD0aagbj4bg==", "path": "masa.utils.models.config/1.2.0-preview.10", "hashPath": "masa.utils.models.config.1.2.0-preview.10.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/10.0.0-preview.7.25380.108": {"type": "package", "serviceable": true, "sha512": "sha512-pSUTOnT72Xm2bFk/C+9zESkIySQabWdXZ9v5gmao4bh+fjcqqmnuNC/6sNimYtvR1rbEBML8z4TFjeW5ve7k1w==", "path": "microsoft.aspnetcore.authentication.jwtbearer/10.0.0-preview.7.25380.108", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.10.0.0-preview.7.25380.108.nupkg.sha512"}, "Microsoft.AspNetCore.OpenApi/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-xRJe8UrLnOGs6hOBrT/4r74q97626H0mABb/DV0smlReIx6uQCENAe+TUqF6hD3NtT4sB+qrvWhAej6kxPxgew==", "path": "microsoft.aspnetcore.openapi/9.0.1", "hashPath": "microsoft.aspnetcore.openapi.9.0.1.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3WA9q9yVqJp222P3x1wYIGDAkpjAku0TMUaaQV22g6L67AI0LdOIrVS7Ht2vJfLHGSPVuqN94vIr15qn+HEkHw==", "path": "microsoft.bcl.asyncinterfaces/8.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512"}, "Microsoft.Bcl.Cryptography/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-YgZYAWzyNuPVtPq6WNm0bqOWNjYaWgl5mBWTGZyNoXitYBUYSp6iUB9AwK0V1mo793qRJUXz2t6UZrWITZSvuQ==", "path": "microsoft.bcl.cryptography/9.0.4", "hashPath": "microsoft.bcl.cryptography.9.0.4.nupkg.sha512"}, "Microsoft.Data.SqlClient/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-syGQmIUPAYYHAHyTD8FCkTNThpQWvoA7crnIQRMfp8dyB5A2cWU3fQexlRTFkVmV7S0TjVmthi0LJEFVjHo8AQ==", "path": "microsoft.data.sqlclient/6.1.1", "hashPath": "microsoft.data.sqlclient.6.1.1.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-f+pRODTWX7Y67jXO3T5S2dIPZ9qMJNySjlZT/TKmWVNWe19N8jcWmHaqHnnchaq3gxEKv1SWVY5EFzOD06l41w==", "path": "microsoft.data.sqlclient.sni.runtime/6.0.2", "hashPath": "microsoft.data.sqlclient.sni.runtime.6.0.2.nupkg.sha512"}, "Microsoft.Extensions.AmbientMetadata.Application/9.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-NEzH1h6sNqBMZiy3JCsPbAOCKN9dOQ65aAhXK2CuqmfM8pcsYQH8jrwSJjvGf8G8m0FY6B31AgkI6/cE8Xzvvg==", "path": "microsoft.extensions.ambientmetadata.application/9.8.0", "hashPath": "microsoft.extensions.ambientmetadata.application.9.8.0.nupkg.sha512"}, "Microsoft.Extensions.Compliance.Abstractions/9.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-PNmBfkdM4GzZRpq40UIO0xPLHIgfZAQ246NGu1S5hOCtIZHBjCREqYpygM6PIGJ+I81QXm4Be058AWym4SmXTQ==", "path": "microsoft.extensions.compliance.abstractions/9.8.0", "hashPath": "microsoft.extensions.compliance.abstractions.9.8.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/10.0.0-preview.7.25380.108": {"type": "package", "serviceable": true, "sha512": "sha512-bqeb/og5BMVpJ0cAfVgHhyNPfQa/uF/t6MhoQ9Z0kAUuOvk+hCfnSKlx2CZUzrkrWN6zK/jRXZW8RsbIxtzexw==", "path": "microsoft.extensions.configuration/10.0.0-preview.7.25380.108", "hashPath": "microsoft.extensions.configuration.10.0.0-preview.7.25380.108.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/10.0.0-preview.7.25380.108": {"type": "package", "serviceable": true, "sha512": "sha512-Tos2R09p02UbSg97w7QNr+dNTKuBtmsWKy4+awTnn2d34CXDpsKkAdDis6gLmhuCjz4c0HyB5S0l2ahaqu+u7A==", "path": "microsoft.extensions.configuration.abstractions/10.0.0-preview.7.25380.108", "hashPath": "microsoft.extensions.configuration.abstractions.10.0.0-preview.7.25380.108.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/10.0.0-preview.7.25380.108": {"type": "package", "serviceable": true, "sha512": "sha512-BAcZtIevY+MgiTX9t9iuz8CoYmGPoL1QeAlhNav8EVJmNlhKR/H7nESjxJifejcCRuwQ+dcXlFtHo1xrCMaZWA==", "path": "microsoft.extensions.configuration.binder/10.0.0-preview.7.25380.108", "hashPath": "microsoft.extensions.configuration.binder.10.0.0-preview.7.25380.108.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/10.0.0-preview.7.25380.108": {"type": "package", "serviceable": true, "sha512": "sha512-wrRFfwx7avg204vRHD1C7//zo9axtSLqNydbPXVN6r1tpQUsTz2Mbd6QsYMo+zoNUSvFD7fPFZPsG8pkmbX50A==", "path": "microsoft.extensions.configuration.commandline/10.0.0-preview.7.25380.108", "hashPath": "microsoft.extensions.configuration.commandline.10.0.0-preview.7.25380.108.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/10.0.0-preview.7.25380.108": {"type": "package", "serviceable": true, "sha512": "sha512-bIh84M0Clp94aGE827+cw3Ld3E+LN68Goqk1oyHTAkOMftp3YbkGB/yEHGdydsNEi3kfiLw5lZdu3Nh1Agt9gg==", "path": "microsoft.extensions.configuration.environmentvariables/10.0.0-preview.7.25380.108", "hashPath": "microsoft.extensions.configuration.environmentvariables.10.0.0-preview.7.25380.108.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/10.0.0-preview.7.25380.108": {"type": "package", "serviceable": true, "sha512": "sha512-eGgKM6UgrXTTGNDkPsXE4gofRMK+LPv2PIzMRUOVFLB2iAjoVhSgTTDBiQgVSXZW279FCaoILD8wC7zz5+sZBA==", "path": "microsoft.extensions.configuration.fileextensions/10.0.0-preview.7.25380.108", "hashPath": "microsoft.extensions.configuration.fileextensions.10.0.0-preview.7.25380.108.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/10.0.0-preview.7.25380.108": {"type": "package", "serviceable": true, "sha512": "sha512-vMUtXxczM+wt2rZT3AtDHRG4m0Wzn4R+cTFSr4RDq3VPy1z+qeX+xa+a9Ft73R5ODy/T0N5F1fXxYN6h1fvh6w==", "path": "microsoft.extensions.configuration.json/10.0.0-preview.7.25380.108", "hashPath": "microsoft.extensions.configuration.json.10.0.0-preview.7.25380.108.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/10.0.0-preview.7.25380.108": {"type": "package", "serviceable": true, "sha512": "sha512-X6M5ARSre9pHhQcrdIoPGCrGe2Xh6iM4AYwJGXRrgG6+blFTc04Iau7tvnjOhCDaEFr2g2cxMJI1wiLlJFOECg==", "path": "microsoft.extensions.configuration.usersecrets/10.0.0-preview.7.25380.108", "hashPath": "microsoft.extensions.configuration.usersecrets.10.0.0-preview.7.25380.108.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/10.0.0-preview.7.25380.108": {"type": "package", "serviceable": true, "sha512": "sha512-UVMEx1ZOOugXCNxXARPsmtu7B3RzYShFoeGvmO4wA1OmcW8cOSXr7QNTiCTp3uXNrx14daefmDX/BlsExlZyVg==", "path": "microsoft.extensions.dependencyinjection/10.0.0-preview.7.25380.108", "hashPath": "microsoft.extensions.dependencyinjection.10.0.0-preview.7.25380.108.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/10.0.0-preview.7.25380.108": {"type": "package", "serviceable": true, "sha512": "sha512-0oSQ8o2O8eMxaInqR1GykEzzlerBTN3xQMsEtaWA4zbf1LmrqV7H9ctTTjK4oMeWMCTb9mfYoN9fsVWbAhkTXA==", "path": "microsoft.extensions.dependencyinjection.abstractions/10.0.0-preview.7.25380.108", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.10.0.0-preview.7.25380.108.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.AutoActivation/9.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-aFPPydGE83MCtxUdjE5iBU7UNLSioB1m/4gWMO9Plh8mXu5umQLPKXwq/Bzd+tSeoKmkzJgqecwRn6C1aXzwGA==", "path": "microsoft.extensions.dependencyinjection.autoactivation/9.8.0", "hashPath": "microsoft.extensions.dependencyinjection.autoactivation.9.8.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-saxr2XzwgDU77LaQfYFXmddEDRUKHF4DaGMZkNB3qjdVSZlax3//dGJagJkKrGMIPNZs2jVFXITyCCR6UHJNdA==", "path": "microsoft.extensions.dependencymodel/9.0.0", "hashPath": "microsoft.extensions.dependencymodel.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/10.0.0-preview.7.25380.108": {"type": "package", "serviceable": true, "sha512": "sha512-hbUpAah1f1gSx7NT7GKnmnzdQiP6POB7Mo8HNGZQ5qdcsZm6c3e6toK92u4kMkgJ9Cp0mRgL2wIyDfT6ixS/qw==", "path": "microsoft.extensions.diagnostics/10.0.0-preview.7.25380.108", "hashPath": "microsoft.extensions.diagnostics.10.0.0-preview.7.25380.108.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/10.0.0-preview.7.25380.108": {"type": "package", "serviceable": true, "sha512": "sha512-4KCRTDM1TUA1+zwUBefsM9vjCRzAjkhLOsITu5X/z8GxdwxvhVRQL3svELII4AwcWG2PUvAqWHIGnjmKlYJmIQ==", "path": "microsoft.extensions.diagnostics.abstractions/10.0.0-preview.7.25380.108", "hashPath": "microsoft.extensions.diagnostics.abstractions.10.0.0-preview.7.25380.108.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.ExceptionSummarization/9.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-uOFTYKABSr+H5C9V5mxTf0hluApAP+p1JNfELARilqq7kujQtsJpW/wUmFhqRYUuOmh1O7IbWllVXNV1qMtDOQ==", "path": "microsoft.extensions.diagnostics.exceptionsummarization/9.8.0", "hashPath": "microsoft.extensions.diagnostics.exceptionsummarization.9.8.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/10.0.0-preview.7.25380.108": {"type": "package", "serviceable": true, "sha512": "sha512-lvXcu7o7ETZ0SRPTOMhMuzyABFSiTwQdye1ATqdQH4u0n46s0QtTMN8u8zrHdIDtU5zv8hv3PqnOmmQt6lMzEQ==", "path": "microsoft.extensions.fileproviders.abstractions/10.0.0-preview.7.25380.108", "hashPath": "microsoft.extensions.fileproviders.abstractions.10.0.0-preview.7.25380.108.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/10.0.0-preview.7.25380.108": {"type": "package", "serviceable": true, "sha512": "sha512-CCWJM/f0hEldsKdB+QXJpcyELI0VRHbIuWWejCk9pNQBTuPOJHX5Lxsn4Lt8d30DGosTfkAQWLpy/wh+TGT6xA==", "path": "microsoft.extensions.fileproviders.physical/10.0.0-preview.7.25380.108", "hashPath": "microsoft.extensions.fileproviders.physical.10.0.0-preview.7.25380.108.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/10.0.0-preview.7.25380.108": {"type": "package", "serviceable": true, "sha512": "sha512-HsDTy1yAhOyznpgtbaUpiHojeMDrmkwAI/iW6G9PksLfhJAFXT1PffKoWMG2+fS4rWo6yoaro2cmJH0RYBKJ0Q==", "path": "microsoft.extensions.filesystemglobbing/10.0.0-preview.7.25380.108", "hashPath": "microsoft.extensions.filesystemglobbing.10.0.0-preview.7.25380.108.nupkg.sha512"}, "Microsoft.Extensions.Hosting/10.0.0-preview.7.25380.108": {"type": "package", "serviceable": true, "sha512": "sha512-VcqnTGY8bMnFxZQb3cjcXnXy4x7dJgp85UlujErNh34xh4ZislQ2XhX7ak0gUnRh8c9ZTI3xj4groyqBl/DNHA==", "path": "microsoft.extensions.hosting/10.0.0-preview.7.25380.108", "hashPath": "microsoft.extensions.hosting.10.0.0-preview.7.25380.108.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/10.0.0-preview.7.25380.108": {"type": "package", "serviceable": true, "sha512": "sha512-J6zRebs9tVxWat6q3Z8v1fknqYhUbyUVoZYiGqhu7g+ChVYeCdp/YL2qSvC/Ap/KThXu6+C+R40gdDPCjUq5EQ==", "path": "microsoft.extensions.hosting.abstractions/10.0.0-preview.7.25380.108", "hashPath": "microsoft.extensions.hosting.abstractions.10.0.0-preview.7.25380.108.nupkg.sha512"}, "Microsoft.Extensions.Http.Diagnostics/9.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-uRb8lu0tNmE8OO6Zq/SfQg+pv9q67g8l2kadMEqpXTy2qw9+6+rBpgE8MU/rTgPd7iOOlQThY65CABD/7f3dow==", "path": "microsoft.extensions.http.diagnostics/9.8.0", "hashPath": "microsoft.extensions.http.diagnostics.9.8.0.nupkg.sha512"}, "Microsoft.Extensions.Http.Resilience/9.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-ruZA0njkKRk2W6V0OqfC+rgKFe0pT/fl7MOYaGoSVAZgjW8r0wrrsEp2c+YqOXGhQfAuyyvZq1Wg1EiL3+0ncQ==", "path": "microsoft.extensions.http.resilience/9.8.0", "hashPath": "microsoft.extensions.http.resilience.9.8.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/10.0.0-preview.7.25380.108": {"type": "package", "serviceable": true, "sha512": "sha512-vktcXpKfaF3TEC5OoAv2ySdImeBErcbFwUduuFIGvKOdYGw1B1N8ZpG5GLxApMXFgygdMcAFrkotDWzOF1npUA==", "path": "microsoft.extensions.logging/10.0.0-preview.7.25380.108", "hashPath": "microsoft.extensions.logging.10.0.0-preview.7.25380.108.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/10.0.0-preview.7.25380.108": {"type": "package", "serviceable": true, "sha512": "sha512-qfx62EG7wvjym/GusNX+kzqPByIVYiXyqBYaObQlIch5YukiVd4ovUJHALYt11jpcxpv9nmjgmqahIluf2j5xA==", "path": "microsoft.extensions.logging.abstractions/10.0.0-preview.7.25380.108", "hashPath": "microsoft.extensions.logging.abstractions.10.0.0-preview.7.25380.108.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/10.0.0-preview.7.25380.108": {"type": "package", "serviceable": true, "sha512": "sha512-nfivpgjltKnYWCT62WtNgaRyyTv92nRYkqJabEQIvmtlQrjuYNb/kiECy74+jd4t4AQPx3hlPn9hWiLGvIEHhg==", "path": "microsoft.extensions.logging.configuration/10.0.0-preview.7.25380.108", "hashPath": "microsoft.extensions.logging.configuration.10.0.0-preview.7.25380.108.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/10.0.0-preview.7.25380.108": {"type": "package", "serviceable": true, "sha512": "sha512-VfOeXETfctJamiTwovg1SmyfEboi8klawzpE6hv45f8KPajn/PPTuYRCQjaQWiP0praYdAJWrV4TjvDF0+xxeg==", "path": "microsoft.extensions.logging.console/10.0.0-preview.7.25380.108", "hashPath": "microsoft.extensions.logging.console.10.0.0-preview.7.25380.108.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/10.0.0-preview.7.25380.108": {"type": "package", "serviceable": true, "sha512": "sha512-R1XFfHifpXKXtIGDt/QrvwnqPEOMEMdDppBMVjb5UpI3RSBKriTWdKeaJIcc1gx6e56aVO2xOT3EtfnK6Xb3Ig==", "path": "microsoft.extensions.logging.debug/10.0.0-preview.7.25380.108", "hashPath": "microsoft.extensions.logging.debug.10.0.0-preview.7.25380.108.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/10.0.0-preview.7.25380.108": {"type": "package", "serviceable": true, "sha512": "sha512-pa6ggb9nqfU+DPLI2NXRaUDmkrw3mvmXcnh4NQrgcvzNQPB1PW8HySNm+KvzX/nK/UmPtJEa3NbOhmpDBdrbMA==", "path": "microsoft.extensions.logging.eventlog/10.0.0-preview.7.25380.108", "hashPath": "microsoft.extensions.logging.eventlog.10.0.0-preview.7.25380.108.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/10.0.0-preview.7.25380.108": {"type": "package", "serviceable": true, "sha512": "sha512-K0Qydvu3fnotw1kynMz6kDt8FX+VnKxOjX1lVFjKKkyHroHitpz8YUOuA+TlbXQFZQyv3bO7nDimKVkcvPFcVQ==", "path": "microsoft.extensions.logging.eventsource/10.0.0-preview.7.25380.108", "hashPath": "microsoft.extensions.logging.eventsource.10.0.0-preview.7.25380.108.nupkg.sha512"}, "Microsoft.Extensions.Options/10.0.0-preview.7.25380.108": {"type": "package", "serviceable": true, "sha512": "sha512-kG3XdCpBiMSxPsAeMCy8YhHpe3sUlSoBGuZQfTcC/VaWKrvpQ5OrbhBCfb/SOPLzWexijSsDwtgjYenRvqE91Q==", "path": "microsoft.extensions.options/10.0.0-preview.7.25380.108", "hashPath": "microsoft.extensions.options.10.0.0-preview.7.25380.108.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/10.0.0-preview.7.25380.108": {"type": "package", "serviceable": true, "sha512": "sha512-z05jUKh7+6FTFE+Obdg6wEQqvdxNf+ty5YUZ9VjNyzYcoN9hZMbX7RucTifl4CZCJXadlv79C7ZCSmHIK4x0pw==", "path": "microsoft.extensions.options.configurationextensions/10.0.0-preview.7.25380.108", "hashPath": "microsoft.extensions.options.configurationextensions.10.0.0-preview.7.25380.108.nupkg.sha512"}, "Microsoft.Extensions.Primitives/10.0.0-preview.7.25380.108": {"type": "package", "serviceable": true, "sha512": "sha512-0oC37fX0irgi29vN8JQdvXn1jZI6upCIs8Q8J+loetDrWrB3fwYBqbUdz/mMS41G+kkjCSaGRfZM8dBiZqqMpw==", "path": "microsoft.extensions.primitives/10.0.0-preview.7.25380.108", "hashPath": "microsoft.extensions.primitives.10.0.0-preview.7.25380.108.nupkg.sha512"}, "Microsoft.Extensions.Resilience/9.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-GLgn4TR+9QaDquaLR94tgsvSftXOJaBiepqFsWQV9VBH6CFK2hY6oTSudyIDYub+qAszMdgDxW9FwFj1tkXyxA==", "path": "microsoft.extensions.resilience/9.8.0", "hashPath": "microsoft.extensions.resilience.9.8.0.nupkg.sha512"}, "Microsoft.Extensions.ServiceDiscovery/9.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-+pWd7AwksTkDSvWBlfFzC2z9giDMYpRhV6ymoPcOgjNbMPV9ceqQ1Lt+vJ2OYB/g7n4qNfUk527XBew9EnRXKA==", "path": "microsoft.extensions.servicediscovery/9.4.1", "hashPath": "microsoft.extensions.servicediscovery.9.4.1.nupkg.sha512"}, "Microsoft.Extensions.ServiceDiscovery.Abstractions/9.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-FLoauVK9R8BWAqFIIUsQF+31TwNqG/h7TSUJR17ZebjmK3Ee1FNrHYYERO853NwLSYG6iEr5V+HZd6b9oj8pIw==", "path": "microsoft.extensions.servicediscovery.abstractions/9.4.1", "hashPath": "microsoft.extensions.servicediscovery.abstractions.9.4.1.nupkg.sha512"}, "Microsoft.Extensions.Telemetry/9.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-OAQw8rUlx5UdN0tpT0n0QwtwYb3tI605hoMx0TrXJowuj7xnvvxnnTiBQS8IZUldd9tqb73I0fAKk5yopTYU3w==", "path": "microsoft.extensions.telemetry/9.8.0", "hashPath": "microsoft.extensions.telemetry.9.8.0.nupkg.sha512"}, "Microsoft.Extensions.Telemetry.Abstractions/9.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-kO2q2nzlaKZLi3eYa1nRPsbWms56RjQCFQalvT92Zt9j89PPxsyhdT++9s0aLKWDe3eVZpjY1329ZERXvW/1Dg==", "path": "microsoft.extensions.telemetry.abstractions/9.8.0", "hashPath": "microsoft.extensions.telemetry.abstractions.9.8.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.73.1": {"type": "package", "serviceable": true, "sha512": "sha512-NnDLS8QwYqO5ZZecL2oioi1LUqjh5Ewk4bMLzbgiXJbQmZhDLtKwLxL3DpGMlQAJ2G4KgEnvGPKa+OOgffeJbw==", "path": "microsoft.identity.client/4.73.1", "hashPath": "microsoft.identity.client.4.73.1.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/4.73.1": {"type": "package", "serviceable": true, "sha512": "sha512-xDztAiV2F0wI0W8FLKv5cbaBefyLD6JVaAsvgSN7bjWNCzGYzHbcOEIP5s4TJXUpQzMfUyBsFl1mC6Zmgpz0PQ==", "path": "microsoft.identity.client.extensions.msal/4.73.1", "hashPath": "microsoft.identity.client.extensions.msal.4.73.1.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-OtlIWcyX01olfdevPKZdIPfBEvbcioDyBiE/Z2lHsopsMD7twcKtlN9kMevHmI5IIPhFpfwCIiR6qHQz1WHUIw==", "path": "microsoft.identitymodel.abstractions/8.0.1", "hashPath": "microsoft.identitymodel.abstractions.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-s6++gF9x0rQApQzOBbSyp4jUaAlwm+DroKfL8gdOHxs83k8SJfUXhuc46rDB3rNXBQ1MVRxqKUrqFhO/M0E97g==", "path": "microsoft.identitymodel.jsonwebtokens/8.0.1", "hashPath": "microsoft.identitymodel.jsonwebtokens.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-UCPF2exZqBXe7v/6sGNiM6zCQOUXXQ9+v5VTb9gPB8ZSUPnX53BxlN78v2jsbIvK9Dq4GovQxo23x8JgWvm/Qg==", "path": "microsoft.identitymodel.logging/8.0.1", "hashPath": "microsoft.identitymodel.logging.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-uA2vpKqU3I2mBBEaeJAWPTjT9v1TZrGWKdgK6G5qJd03CLx83kdiqO9cmiK8/n1erkHzFBwU/RphP83aAe3i3g==", "path": "microsoft.identitymodel.protocols/8.0.1", "hashPath": "microsoft.identitymodel.protocols.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-AQDbfpL+yzuuGhO/mQhKNsp44pm5Jv8/BI4KiFXR7beVGZoSH35zMV3PrmcfvSTsyI6qrcR898NzUauD6SRigg==", "path": "microsoft.identitymodel.protocols.openidconnect/8.0.1", "hashPath": "microsoft.identitymodel.protocols.openidconnect.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-kDimB6Dkd3nkW2oZPDkMkVHfQt3IDqO5gL0oa8WVy3OP4uE8Ij+8TXnqg9TOd9ufjsY3IDiGz7pCUbnfL18tjg==", "path": "microsoft.identitymodel.tokens/8.0.1", "hashPath": "microsoft.identitymodel.tokens.8.0.1.nupkg.sha512"}, "Microsoft.OpenApi/1.6.24": {"type": "package", "serviceable": true, "sha512": "sha512-fZhncz3BmI2pJk589qHvvR3gGYMol5u98Yk9zRQiDjZac8Uq5NG/dYEFJbW/DBvJf5e8utXt5fN/58XCgjNqyQ==", "path": "microsoft.openapi/1.6.24", "hashPath": "microsoft.openapi.1.6.24.nupkg.sha512"}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "path": "microsoft.sqlserver.server/1.0.0", "hashPath": "microsoft.sqlserver.server.1.0.0.nupkg.sha512"}, "MiniProfiler.AspNetCore/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-meedJsjpYOeHPhE8H6t+dGQ9zLxcCQVpi4DXzmxmYAXywmTzlo6jv2IASUv5QijTU0CxsROln3FHd8RsTO8Z8A==", "path": "miniprofiler.aspnetcore/4.5.4", "hashPath": "miniprofiler.aspnetcore.4.5.4.nupkg.sha512"}, "MiniProfiler.AspNetCore.Mvc/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-+NqXyCy9aNdroPm6leW5+cpngtCnkCdoyOlJzvVN62uucSx+MYkx8jmKbgAt+aCP6aghADfHBExwrTIldHxapg==", "path": "miniprofiler.aspnetcore.mvc/4.5.4", "hashPath": "miniprofiler.aspnetcore.mvc.4.5.4.nupkg.sha512"}, "MiniProfiler.Shared/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-f8ckFm/xTS8C2Bn4BdVc94dNvg+tRfk0e4XFaETOqRi6r0PUOyn3Z9jTQCVpB3R1pP5WiRsEIrqqxux95BVpTA==", "path": "miniprofiler.shared/4.5.4", "hashPath": "miniprofiler.shared.4.5.4.nupkg.sha512"}, "MySql.Data/9.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-E4t/IQzcXg4nYGqrGkoGwwSWA1V2L+LKzVddPABAPcj2i6RESP2fcZQ4XFC0Wv+Cq4DlgR3DYhX/fGaZ3VxCPQ==", "path": "mysql.data/9.1.0", "hashPath": "mysql.data.9.1.0.nupkg.sha512"}, "NetTopologySuite/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3ajBClEI9wx2/DjjGmV52sHW1m52vLg8sdz1pJbTf5ySj1X90qehQs3v1DRwGo0F8UKj/Z2SjNhRN/6LroAkqg==", "path": "nettopologysuite/2.0.0", "hashPath": "nettopologysuite.2.0.0.nupkg.sha512"}, "NetTopologySuite.IO.PostGis/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-3W8XTFz8iP6GQ5jDXK1/LANHiU+988k1kmmuPWNKcJLpmSg6CvFpbTpz+s4+LBzkAp64wHGOldSlkSuzYfrIKA==", "path": "nettopologysuite.io.postgis/2.1.0", "hashPath": "nettopologysuite.io.postgis.2.1.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "path": "newtonsoft.json/13.0.1", "hashPath": "newtonsoft.json.13.0.1.nupkg.sha512"}, "Npgsql/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-tPvY61CxOAWxNsKLEBg+oR646X4Bc8UmyQ/tJszL/7mEmIXQnnBhVJZrZEEUv0Bstu0mEsHZD5At3EO8zQRAYw==", "path": "npgsql/9.0.3", "hashPath": "npgsql.9.0.3.nupkg.sha512"}, "Npgsql.LegacyPostgis/5.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-eVWTnaY8SbFBMpugVhQt9nQMFKLGM4JcgNzc3zVDiO+PJMFEa1AYF20V7tiOwduWhH0T4mVP0zPAl2QvUjcDnA==", "path": "npgsql.legacypostgis/5.0.18", "hashPath": "npgsql.legacypostgis.5.0.18.nupkg.sha512"}, "Npgsql.NetTopologySuite/5.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-tgqYADHGD6uwjibPe6m1CHVE2DJvMFv55hbtdEjwPIIjUKNWP2YUMQUNSeRpZNipjRcUOoAFEZvwAXizNKM8IA==", "path": "npgsql.nettopologysuite/5.0.18", "hashPath": "npgsql.nettopologysuite.5.0.18.nupkg.sha512"}, "OpenTelemetry/1.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-aIEu2O3xFOdwIVH0AJsIHPIMH1YuX18nzu7BHyaDNQ6NWSk4Zyrs9Pp6y8SATuSbvdtmvue4mj/QZ3838srbwA==", "path": "opentelemetry/1.12.0", "hashPath": "opentelemetry.1.12.0.nupkg.sha512"}, "OpenTelemetry.Api/1.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-Xt0qldi+iE2szGrM3jAqzEMEJd48YBtqI6mge0+ArXTZg3aTpRmyhL6CKKl3bLioaFSSVbBpEbPin8u6Z46Yrw==", "path": "opentelemetry.api/1.12.0", "hashPath": "opentelemetry.api.1.12.0.nupkg.sha512"}, "OpenTelemetry.Api.ProviderBuilderExtensions/1.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-t6Vk1143BfiisCWYbRcyzkAuN6Aq5RkYtfOSMoqCIRMvtN9p1e1xzc0nWQ+fccNGOVgHn3aMK5xFn2+iWMcr8A==", "path": "opentelemetry.api.providerbuilderextensions/1.12.0", "hashPath": "opentelemetry.api.providerbuilderextensions.1.12.0.nupkg.sha512"}, "OpenTelemetry.Exporter.OpenTelemetryProtocol/1.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-7LzQSPhz5pNaL4xZgT3wkZODA1NLrEq3bet8KDHgtaJ9q+VNP7wmiZky8gQfMkB4FXuI/pevT8ZurL4p5997WA==", "path": "opentelemetry.exporter.opentelemetryprotocol/1.12.0", "hashPath": "opentelemetry.exporter.opentelemetryprotocol.1.12.0.nupkg.sha512"}, "OpenTelemetry.Exporter.Zipkin/1.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-Mc+k/bxib7ZWe2S6Au/p47yyan1J0sfVplZRIMpUrCwLrDK8J3DSRAqf1CwWdqKN/3tLI03AK4GSujXq5R4Idg==", "path": "opentelemetry.exporter.zipkin/1.12.0", "hashPath": "opentelemetry.exporter.zipkin.1.12.0.nupkg.sha512"}, "OpenTelemetry.Extensions.Hosting/1.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-6/8O6rsJRwslg5/Fm3bscBelw4Yh9T9CN24p7cAsuEFkrmmeSO9gkYUCK02Qi+CmPM2KHYTLjKi0lJaCsDMWQA==", "path": "opentelemetry.extensions.hosting/1.12.0", "hashPath": "opentelemetry.extensions.hosting.1.12.0.nupkg.sha512"}, "OpenTelemetry.Instrumentation.AspNetCore/1.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-r+Mzggd2P4N0Y34QIO6kakVPBOKFYSHnLkTrXXM+r37ABp+iaUvVUe+u/uxszsi5f7P5mrG0uYYaJ1QGHvzo3A==", "path": "opentelemetry.instrumentation.aspnetcore/1.12.0", "hashPath": "opentelemetry.instrumentation.aspnetcore.1.12.0.nupkg.sha512"}, "OpenTelemetry.Instrumentation.GrpcCore/1.0.0-beta.6": {"type": "package", "serviceable": true, "sha512": "sha512-Dy2W1dAI/qXYAkJ2r2lTTRERNhxf88TVid9xcwP5+wl0f8thn2HD7kP5TFmq+2PcJv3bIDi6phKRM6Frih2c8Q==", "path": "opentelemetry.instrumentation.grpccore/1.0.0-beta.6", "hashPath": "opentelemetry.instrumentation.grpccore.1.0.0-beta.6.nupkg.sha512"}, "OpenTelemetry.Instrumentation.GrpcNetClient/1.12.0-beta.1": {"type": "package", "serviceable": true, "sha512": "sha512-mi3Njei+Y5bn7oJYwIy/XWON+TK/sDUZy1m8UPw4ihtaCVjBTUdPWCjOW1sv3mZoctnJAT6PjNXD9222rqO65w==", "path": "opentelemetry.instrumentation.grpcnetclient/1.12.0-beta.1", "hashPath": "opentelemetry.instrumentation.grpcnetclient.1.12.0-beta.1.nupkg.sha512"}, "OpenTelemetry.Instrumentation.Http/1.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-0rW+MbHgUQAdbvBtRxPYoQBosbNdWegL7cYkRlxq+KQ/VFyU8itt4pWTccmu1/FWmTgqJyT3LaujyDZoRrm8Yg==", "path": "opentelemetry.instrumentation.http/1.12.0", "hashPath": "opentelemetry.instrumentation.http.1.12.0.nupkg.sha512"}, "OpenTelemetry.Instrumentation.Process/1.12.0-beta.1": {"type": "package", "serviceable": true, "sha512": "sha512-GrjqeNzH94WOpUpKO7xO13NjBujk0wxvxzGBFuvU+kIAzMB22a4PzOGPrvQivx27RIKaFH+psQP6dTPUmv/8sA==", "path": "opentelemetry.instrumentation.process/1.12.0-beta.1", "hashPath": "opentelemetry.instrumentation.process.1.12.0-beta.1.nupkg.sha512"}, "OpenTelemetry.Instrumentation.Runtime/1.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-xmd0TAm2x+T3ztdf5BolIwLPh+Uy6osaBeIQtCXv611PN7h/Pnhsjg5lU2hkAWj7M7ns74U5wtVpS8DXmJ+94w==", "path": "opentelemetry.instrumentation.runtime/1.12.0", "hashPath": "opentelemetry.instrumentation.runtime.1.12.0.nupkg.sha512"}, "Oracle.ManagedDataAccess.Core/23.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-Oc8AX7xme05xrp4/aCxKBH4+bpWgMCFafXI7LbLO/7OBMJLZRXhMtejDgIb8aYvIVyV7vSdAy3LkCYcJorxn1A==", "path": "oracle.manageddataaccess.core/23.6.1", "hashPath": "oracle.manageddataaccess.core.23.6.1.nupkg.sha512"}, "Polly.Core/8.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-BpE2I6HBYYA5tF0Vn4eoQOGYTYIK1BlF5EXVgkWGn3mqUUjbXAr13J6fZVbp7Q3epRR8yshacBMlsHMhpOiV3g==", "path": "polly.core/8.4.2", "hashPath": "polly.core.8.4.2.nupkg.sha512"}, "Polly.Extensions/8.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-GZ9vRVmR0jV2JtZavt+pGUsQ1O1cuRKG7R7VOZI6ZDy9y6RNPvRvXK1tuS4ffUrv8L0FTea59oEuQzgS0R7zSA==", "path": "polly.extensions/8.4.2", "hashPath": "polly.extensions.8.4.2.nupkg.sha512"}, "Polly.RateLimiting/8.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-ehTImQ/eUyO07VYW2WvwSmU9rRH200SKJ/3jku9rOkyWE0A2JxNFmAVms8dSn49QLSjmjFRRSgfNyOgr/2PSmA==", "path": "polly.ratelimiting/8.4.2", "hashPath": "polly.ratelimiting.8.4.2.nupkg.sha512"}, "Scalar.AspNetCore/2.6.9": {"type": "package", "serviceable": true, "sha512": "sha512-wIejm5++cWnu2jrkNiku+AvIClEEgp8c7GMQmTuT5LZeamEhHS1tvGEDJhfnPCZ4DPIMVX6e9b4tpzRJ2Qqy/w==", "path": "scalar.aspnetcore/2.6.9", "hashPath": "scalar.aspnetcore.2.6.9.nupkg.sha512"}, "Serilog/4.3.1-dev-02373": {"type": "package", "serviceable": true, "sha512": "sha512-f0HNPaleOox5++8zqxAdi9afnUUkoznLOmd0ur/UnLbzl8bIaPyyBGOpbIJmC1kz2vHfkQ0fXh6KXMdlz7bcLQ==", "path": "serilog/4.3.1-dev-02373", "hashPath": "serilog.4.3.1-dev-02373.nupkg.sha512"}, "Serilog.AspNetCore/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JslDajPlBsn3Pww1554flJFTqROvK9zz9jONNQgn0D8Lx2Trw8L0A8/n6zEQK1DAZWXrJwiVLw8cnTR3YFuYsg==", "path": "serilog.aspnetcore/9.0.0", "hashPath": "serilog.aspnetcore.9.0.0.nupkg.sha512"}, "Serilog.Enrichers.Environment/3.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-9BqCE4C9FF+/rJb/CsQwe7oVf44xqkOvMwX//CUxvUR25lFL4tSS6iuxE5eW07quby1BAyAEP+vM6TWsnT3iqw==", "path": "serilog.enrichers.environment/3.0.1", "hashPath": "serilog.enrichers.environment.3.0.1.nupkg.sha512"}, "Serilog.Enrichers.Thread/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-C7BK25a1rhUyr+Tp+1BYcVlBJq7M2VCHlIgnwoIUVJcicM9jYcvQK18+OeHiXw7uLPSjqWxJIp1EfaZ/RGmEwA==", "path": "serilog.enrichers.thread/4.0.0", "hashPath": "serilog.enrichers.thread.4.0.0.nupkg.sha512"}, "Serilog.Expressions/5.1.0-dev-02301": {"type": "package", "serviceable": true, "sha512": "sha512-hW7OKCr8m/5h84oTFeXSnnfqFozn05h9BfU+1kMLLFvSmJPjRhaxqVqR/zs2+g9kD8s0d9CSRCwhtZAshbyvUg==", "path": "serilog.expressions/5.1.0-dev-02301", "hashPath": "serilog.expressions.5.1.0-dev-02301.nupkg.sha512"}, "Serilog.Extensions.Hosting/9.0.1-dev-02307": {"type": "package", "serviceable": true, "sha512": "sha512-bBx2sEozyzXTv+nysstxgUxHOWbAx/viJ/SmM4ELLhjEHjj+KfGOpn2c0hn0Wb6x+9OZ3bVESsZmhtPLr4gLKw==", "path": "serilog.extensions.hosting/9.0.1-dev-02307", "hashPath": "serilog.extensions.hosting.9.0.1-dev-02307.nupkg.sha512"}, "Serilog.Extensions.Logging/9.0.3-dev-02320": {"type": "package", "serviceable": true, "sha512": "sha512-cfARu+vsHJHNyTVmo1U+AQY00q1W7w3onNSHqUHyd/GtoCSkH2VFFpy7C2z+ATVCcDk7e6EUU49dn6svvW3uMQ==", "path": "serilog.extensions.logging/9.0.3-dev-02320", "hashPath": "serilog.extensions.logging.9.0.3-dev-02320.nupkg.sha512"}, "Serilog.Formatting.Compact/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-wQsv14w9cqlfB5FX2MZpNsTawckN4a8dryuNGbebB/3Nh1pXnROHZov3swtu3Nj5oNG7Ba+xdu7Et/ulAUPanQ==", "path": "serilog.formatting.compact/3.0.0", "hashPath": "serilog.formatting.compact.3.0.0.nupkg.sha512"}, "Serilog.Settings.Configuration/9.0.1-dev-02317": {"type": "package", "serviceable": true, "sha512": "sha512-1/PPRG1VvYCuFJL8Dc7lkpHNFRZq6n0cwy976CgK21qRwmAIR2GgEkzIc9LZw8TVlvSmoUhZRyeBoU7bB9TjIw==", "path": "serilog.settings.configuration/9.0.1-dev-02317", "hashPath": "serilog.settings.configuration.9.0.1-dev-02317.nupkg.sha512"}, "Serilog.Sinks.Console/6.0.1-dev-00953": {"type": "package", "serviceable": true, "sha512": "sha512-Goi2B0Je0X0NvWYUi0SiU9MJNF2957Kfjmc6VPZ2hNl6Lmj9he6laxmDuQU/c0fBdAFnNiEUPPcHd/NJVyfbkA==", "path": "serilog.sinks.console/6.0.1-dev-00953", "hashPath": "serilog.sinks.console.6.0.1-dev-00953.nupkg.sha512"}, "Serilog.Sinks.Debug/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4BzXcdrgRX7wde9PmHuYd9U6YqycCC28hhpKonK7hx0wb19eiuRj16fPcPSVp0o/Y1ipJuNLYQ00R3q2Zs8FDA==", "path": "serilog.sinks.debug/3.0.0", "hashPath": "serilog.sinks.debug.3.0.0.nupkg.sha512"}, "Serilog.Sinks.File/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lxjg89Y8gJMmFxVkbZ+qDgjl+T4yC5F7WSLTvA+5q0R04tfKVLRL/EHpYoJ/MEQd2EeCKDuylBIVnAYMotmh2A==", "path": "serilog.sinks.file/6.0.0", "hashPath": "serilog.sinks.file.6.0.0.nupkg.sha512"}, "Serilog.Sinks.OpenTelemetry/4.2.1-dev-02306": {"type": "package", "serviceable": true, "sha512": "sha512-9SeG+JzjsMSDF7S1LJtGicZKFGfOWp2hLPZVtyvNh7uXRQqHGrcgxC9k1WPZYXrc7AcFJDbYbfgE0/7SSekH5Q==", "path": "serilog.sinks.opentelemetry/4.2.1-dev-02306", "hashPath": "serilog.sinks.opentelemetry.4.2.1-dev-02306.nupkg.sha512"}, "Serilog.Sinks.Seq/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-aNU8A0K322q7+voPNmp1/qNPH+9QK8xvM1p72sMmCG0wGlshFzmtDW9QnVSoSYCj0MgQKcMOlgooovtBhRlNHw==", "path": "serilog.sinks.seq/9.0.0", "hashPath": "serilog.sinks.seq.9.0.0.nupkg.sha512"}, "SixLabors.ImageSharp/3.1.11": {"type": "package", "serviceable": true, "sha512": "sha512-JfPLyigLthuE50yi6tMt7Amrenr/fA31t2CvJyhy/kQmfulIBAqo5T/YFUSRHtuYPXRSaUHygFeh6Qd933EoSw==", "path": "sixlabors.imagesharp/3.1.11", "hashPath": "sixlabors.imagesharp.3.1.11.nupkg.sha512"}, "Stub.System.Data.SQLite.Core.NetStandard/1.0.119": {"type": "package", "serviceable": true, "sha512": "sha512-dI7ngiCNgdm+n00nQvFTa+LbHvE9MIQXwMSLRzJI/KAJ7G1WmCachsvfE1CD6xvb3OXJvYYEfv3+S/LHyhN0Rg==", "path": "stub.system.data.sqlite.core.netstandard/1.0.119", "hashPath": "stub.system.data.sqlite.core.netstandard.1.0.119.nupkg.sha512"}, "Swashbuckle.AspNetCore/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-Akk4oFgy0ST8Q8pZTfPbrt045tWNyMMiKhlbYjG3qnjQZLz645IL5vhQm7NLicc2sAAQ+vftArIlsYWFevmb2g==", "path": "swashbuckle.aspnetcore/9.0.3", "hashPath": "swashbuckle.aspnetcore.9.0.3.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-CGpkZDWj1g/yH/0wYkxUtBhiFo5TY/Esq2fS0vlBvLOs1UL2Jzef9tdtYmTdd3zBPtnMyXQcsXjMt9yCxz4VaA==", "path": "swashbuckle.aspnetcore.swagger/9.0.3", "hashPath": "swashbuckle.aspnetcore.swagger.9.0.3.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-STqjhw1TZiEGmIRgE6jcJUOcgU/Fjquc6dP4GqbuwBzqWZAWr/9T7FZOGWYEwKnmkMplzlUNepGHwnUrfTP0fw==", "path": "swashbuckle.aspnetcore.swaggergen/9.0.3", "hashPath": "swashbuckle.aspnetcore.swaggergen.9.0.3.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-DgJKJASz5OAygeKv2+N0FCZVhQylESqLXrtrRAqIT0vKpX7t5ImJ1FL6+6OqxKiamGkL0jchRXR8OgpMSsMh8w==", "path": "swashbuckle.aspnetcore.swaggerui/9.0.3", "hashPath": "swashbuckle.aspnetcore.swaggerui.9.0.3.nupkg.sha512"}, "System.ClientModel/1.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-k2jKSO0X45IqhVOT9iQB4xralNN9foRQsRvXBTyRpAVxyzCJlG895T9qYrQWbcJ6OQXxOouJQ37x5nZH5XKK+A==", "path": "system.clientmodel/1.5.1", "hashPath": "system.clientmodel.1.5.1.nupkg.sha512"}, "System.Configuration.ConfigurationManager/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-dvjqKp+2LpGid6phzrdrS/2mmEPxFl3jE1+L7614q4ZChKbLJCpHXg6sBILlCCED1t//EE+un/UdAetzIMpqnw==", "path": "system.configuration.configurationmanager/9.0.4", "hashPath": "system.configuration.configurationmanager.9.0.4.nupkg.sha512"}, "System.Data.Odbc/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-c+GfnZt2/HyU+voKw2fctLZClcNjPZPWS+mnIhGvDknRMqL/fwWlREWPgA4csbp9ZkQIgB4qkufgdh/oh5Ubow==", "path": "system.data.odbc/8.0.0", "hashPath": "system.data.odbc.8.0.0.nupkg.sha512"}, "System.Data.OleDb/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LQ8PjTIF1LtrrlGiyiTVjAkQtTWKm9GSNnygIlWjhN9y88s7xhy6DUNDDkmQQ9f6ex7mA4k0Tl97lz/CklaiLg==", "path": "system.data.oledb/6.0.0", "hashPath": "system.data.oledb.6.0.0.nupkg.sha512"}, "System.Data.SQLite.Core/1.0.119": {"type": "package", "serviceable": true, "sha512": "sha512-bhQB8HVtRA+OOYw8UTD1F1kU+nGJ0/OZvH1JmlVUI4bGvgVEWeX1NcHjA765NvUoRVuCPlt8PrEpZ1thSsk1jg==", "path": "system.data.sqlite.core/1.0.119", "hashPath": "system.data.sqlite.core.1.0.119.nupkg.sha512"}, "System.Diagnostics.EventLog/10.0.0-preview.7.25380.108": {"type": "package", "serviceable": true, "sha512": "sha512-kKFLata6tLJ/1+jJTbsE9YZu/zeOGO+9ZeukQ3uni5flEjcPpjqGxtmqOx/IFUecMQjNGheNEVyC1KaMl+aZMg==", "path": "system.diagnostics.eventlog/10.0.0-preview.7.25380.108", "hashPath": "system.diagnostics.eventlog.10.0.0-preview.7.25380.108.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lX6DXxtJqVGWw7N/QmVoiCyVQ+Q/Xp+jVXPr3gLK1jJExSn1qmAjJQeb8gnOYeeBTG3E3PmG1nu92eYj/TEjpg==", "path": "system.diagnostics.performancecounter/8.0.0", "hashPath": "system.diagnostics.performancecounter.8.0.0.nupkg.sha512"}, "System.DirectoryServices.Protocols/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-puwJxURHDrYLGTQdsHyeMS72ClTqYa4lDYz6LHSbkZEk5hq8H8JfsO4MyYhB5BMMxg93jsQzLUwrnCumj11UIg==", "path": "system.directoryservices.protocols/8.0.0", "hashPath": "system.directoryservices.protocols.8.0.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-GJw3bYkWpOgvN3tJo5X4lYUeIFA2HD293FPUhKmp7qxS+g5ywAb34Dnd3cDAFLkcMohy5XTpoaZ4uAHuw0uSPQ==", "path": "system.identitymodel.tokens.jwt/8.0.1", "hashPath": "system.identitymodel.tokens.jwt.8.0.1.nupkg.sha512"}, "System.Memory.Data/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-B<PERSON><PERSON>uec3jV23EMRDeR7Dr1/qhx7369dZzJ9IWy2xylvb4YfXsrUxspWc4UWYid/tj4zZK58uGZqn2WQiaDMhmAg==", "path": "system.memory.data/8.0.1", "hashPath": "system.memory.data.8.0.1.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-o94k2RKuAce3GeDMlUvIXlhVa1kWpJw95E6C9LwW0KlG0nj5+SgCiIxJ2Eroqb9sLtG1mEMbFttZIBZ13EJPvQ==", "path": "system.security.cryptography.protecteddata/9.0.4", "hashPath": "system.security.cryptography.protecteddata.9.0.4.nupkg.sha512"}, "System.Security.Permissions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-v/BBylw7XevuAsHXoX9dDUUfmBIcUf7Lkz8K3ZXIKz3YRKpw8YftpSir4n4e/jDTKFoaK37AsC3xnk+GNFI1Ow==", "path": "system.security.permissions/8.0.0", "hashPath": "system.security.permissions.8.0.0.nupkg.sha512"}, "System.Windows.Extensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Obg3a90MkOw9mYKxrardLpY2u0axDMrSmy4JCdq2cYbelM2cUwmUir5Bomvd1yxmPL9h5LVHU1tuKBZpUjfASg==", "path": "system.windows.extensions/8.0.0", "hashPath": "system.windows.extensions.8.0.0.nupkg.sha512"}, "WorkQueue/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Gs01Z2+uwtU23WJN67uMdo1819YcFdT1LGeRnONXb0BV79lxbMwEdsieTQbTFpGDvRSXDB7SBMDO68zTnB9mMQ==", "path": "workqueue/1.3.0", "hashPath": "workqueue.1.3.0.nupkg.sha512"}, "ZstdSharp.Port/0.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-Z62eNBIu8E8YtbqlMy57tK3dV1+m2b9NhPeaYovB5exmLKvrGCqOhJTzrEUH5VyUWU6vwX3c1XHJGhW5HVs8dA==", "path": "zstdsharp.port/0.8.0", "hashPath": "zstdsharp.port.0.8.0.nupkg.sha512"}, "SSIC.Entity/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}