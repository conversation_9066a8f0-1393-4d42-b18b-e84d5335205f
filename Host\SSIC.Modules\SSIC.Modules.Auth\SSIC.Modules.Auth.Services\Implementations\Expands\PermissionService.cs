/*
*代码由框架生成,任何更改都可能导致被代码生成器覆盖
*Service提供业务逻辑操作，如果要增加业务逻辑请在当前目录下Expands文件夹PermissionService编写代码
*/
using SSIC.Modules.Auth.Services.Interfaces;
using SSIC.Infrastructure.BaseProvider;
using SSIC.Infrastructure.DependencyInjection;
using SSIC.Entity.Auth;
using FreeSql;
using Microsoft.Extensions.Logging;

namespace SSIC.Modules.Auth.Services.Implementations
{
    /// <summary>
    /// Permission服务实现扩展类
    /// 在此处实现自定义的业务逻辑方法
    /// </summary>
    public partial class PermissionService
    {
        // 在此处添加自定义业务方法实现
        // 例如：
        // /// <summary>
        // /// 根据名称查询Permission
        // /// </summary>
        // /// <param name="name">名称</param>
        // /// <returns></returns>
        // public async Task<List<Permission>> GetByNameAsync(string name)
        // {
        //     return await Repository.Where(x => x.Name == name).ToListAsync();
        // }
    }
}
