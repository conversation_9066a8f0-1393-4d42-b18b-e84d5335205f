﻿using Aspire.Hosting;
using CommunityToolkit.Aspire.Hosting.Dapr;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using SSIC.Infrastructure.ConfigurableOptions.Realization;
using SSIC.Infrastructure.Dapr;
using SSIC.Infrastructure.OptionsEntity;
using SSIC.Infrastructure.Startups;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json;
var builder = DistributedApplication.CreateBuilder(args);
var builder2 = WebApplication.CreateBuilder(args);
// Add service defaults & Aspire components.
// Add service defaults & Aspire components.
var app = builder2.AddStartup(false);
// 添加 Dapr 客户端提供程序
var redis = builder.AddRedis("redis").WithRedisInsight();


var stateStore = builder.AddDaprStateStore("statestore");

var pubSub = builder.AddDaprPubSub("pubsub")
                    .WithMetadata("redisHost", "localhost:6379")
                    .WaitFor(redis);
//builder.AddProject<Projects.SSIC_DevTools>("ssic-devtools").WithDaprSidecar();
//builder.AddProject<Projects.SSIC_HostServer>("ssic-hostserver").WithDaprSidec#ar();
// 监听配置文件变化
var daprProjects =app.Services.GetOptions<List<ServerOptions>>();



foreach (var project in daprProjects)
{
    string projectPath = $"{project.slnpath}/{project.slnname}.csproj";
    string resourceName = project.name.Replace("_", "-").ToLower(); // 资源名称 Aspire 需要小写


    builder.AddProject(resourceName, projectPath).WithReference(pubSub).WithReference(stateStore).WithDaprSidecar(p => {

        p.WithOptions(new DaprSidecarOptions
        {
            AppId = resourceName,
            AppPort = project.port,
            DaprGrpcPort = project.grpcprot
        });
    }).WaitFor(redis);
           //.WithDaprSidecar(p =>
           //{
           //    p.WithOptions(new DaprSidecarOptions
           //    {
           //        AppId = resourceName,
           //        AppPort = project.port,
           //        DaprGrpcPort = project.grpcprot
           //    });
           //});
}
builder.Build().Run();

