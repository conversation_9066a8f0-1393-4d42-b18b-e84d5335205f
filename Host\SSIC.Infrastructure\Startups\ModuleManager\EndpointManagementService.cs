using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.Infrastructure;
using Microsoft.AspNetCore.Routing;
using Microsoft.AspNetCore.Routing.Patterns;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

namespace SSIC.Infrastructure.Startups.ModuleManager
{
    /// <summary>
    /// 端点管理服务实现
    /// </summary>
    public class EndpointManagementService : IEndpointManagementService
    {
        private readonly ILogger<EndpointManagementService> _logger;
        private readonly IModuleDiscoveryService _moduleDiscoveryService;

        public EndpointManagementService(
            ILogger<EndpointManagementService> logger,
            IModuleDiscoveryService moduleDiscoveryService)
        {
            _logger = logger;
            _moduleDiscoveryService = moduleDiscoveryService;
        }

        /// <summary>
        /// 从模块信息创建端点
        /// </summary>
        public IEnumerable<Endpoint> CreateEndpointsFromModule(ModuleInfo moduleInfo)
        {
            try
            {
                var endpoints = new List<Endpoint>();

                foreach (var controllerInfo in moduleInfo.Controllers)
                {
                    foreach (var actionInfo in controllerInfo.Actions)
                    {
                        try
                        {
                            var endpoint = CreateMvcEndpoint(moduleInfo, controllerInfo, actionInfo);
                            if (ValidateEndpoint(endpoint))
                            {
                                endpoints.Add(endpoint);
                                _logger.LogDebug("为模块 {ModuleName} 创建端点: {DisplayName}", 
                                    moduleInfo.Name, endpoint.DisplayName);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "为控制器 {ControllerName}.{ActionName} 创建端点失败", 
                                controllerInfo.Name, actionInfo.Name);
                        }
                    }
                }

                _logger.LogInformation("为模块 {ModuleName} 创建了 {EndpointCount} 个端点", 
                    moduleInfo.Name, endpoints.Count);

                return endpoints;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "为模块 {ModuleName} 创建端点时发生错误", moduleInfo.Name);
                return Enumerable.Empty<Endpoint>();
            }
        }

        /// <summary>
        /// 创建单个端点
        /// </summary>
        public Endpoint CreateEndpoint(EndpointCreationOptions options)
        {
            try
            {
                var routePattern = RoutePatternFactory.Parse(options.RouteTemplate);
                
                // 创建元数据集合
                var metadataItems = new List<object>();
                foreach (var metadataItem in options.Metadata)
                {
                    metadataItems.Add(metadataItem.Value);
                }
                var endpointMetadata = new EndpointMetadataCollection(metadataItems);

                // 使用提供的处理器或默认处理器
                var handler = options.Handler ?? CreateDefaultHandler(options.DisplayName);

                var endpoint = new RouteEndpoint(
                    handler,
                    routePattern,
                    options.Order,
                    endpointMetadata,
                    options.DisplayName
                );

                return endpoint;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建端点失败: {RouteTemplate}", options.RouteTemplate);
                throw;
            }
        }

        /// <summary>
        /// 创建MVC控制器端点
        /// </summary>
        public Endpoint CreateMvcEndpoint(ModuleInfo moduleInfo, ControllerInfo controllerInfo, ActionInfo actionInfo)
        {
            try
            {
                // 构建路由模板
                var routeTemplate = _moduleDiscoveryService.BuildRouteTemplate(moduleInfo, controllerInfo, actionInfo);
                var routePattern = RoutePatternFactory.Parse(routeTemplate);

                // 创建ControllerActionDescriptor
                var actionDescriptor = new ControllerActionDescriptor
                {
                    ControllerName = controllerInfo.Name,
                    ActionName = actionInfo.Name,
                    ControllerTypeInfo = controllerInfo.Type.GetTypeInfo(),
                    MethodInfo = actionInfo.Method,
                    DisplayName = $"{controllerInfo.FullName}.{actionInfo.Name}"
                };

                // 创建元数据集合
                var metadata = new EndpointMetadataCollection(new[] { actionDescriptor });

                // 创建MVC请求处理委托
                var handler = CreateMvcHandler(actionDescriptor);

                var endpoint = new RouteEndpoint(
                    handler,
                    routePattern,
                    0,
                    metadata,
                    actionDescriptor.DisplayName
                );

                return endpoint;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建MVC端点失败: {ControllerName}.{ActionName}", 
                    controllerInfo.Name, actionInfo.Name);
                throw;
            }
        }

        /// <summary>
        /// 批量刷新端点
        /// </summary>
        public async Task<IEnumerable<Endpoint>> RefreshEndpointsAsync(IEnumerable<ModuleInfo> modules)
        {
            try
            {
                var allEndpoints = new List<Endpoint>();

                await Task.Run(() =>
                {
                    foreach (var module in modules)
                    {
                        var moduleEndpoints = CreateEndpointsFromModule(module);
                        allEndpoints.AddRange(moduleEndpoints);
                    }
                });

                _logger.LogInformation("刷新完成，共创建 {EndpointCount} 个端点", allEndpoints.Count);
                return allEndpoints;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量刷新端点时发生错误");
                return Enumerable.Empty<Endpoint>();
            }
        }

        /// <summary>
        /// 验证端点是否有效
        /// </summary>
        public bool ValidateEndpoint(Endpoint endpoint)
        {
            try
            {
                if (endpoint == null)
                    return false;

                if (string.IsNullOrEmpty(endpoint.DisplayName))
                    return false;

                // 检查路由模式
                if (endpoint is RouteEndpoint routeEndpoint)
                {
                    if (routeEndpoint.RoutePattern == null)
                        return false;

                    if (string.IsNullOrEmpty(routeEndpoint.RoutePattern.RawText))
                        return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "验证端点时发生错误");
                return false;
            }
        }

        #region 私有辅助方法

        /// <summary>
        /// 创建默认的请求处理器
        /// </summary>
        private RequestDelegate CreateDefaultHandler(string displayName)
        {
            return async context =>
            {
                try
                {
                    context.Response.StatusCode = 200;
                    context.Response.ContentType = "application/json";
                    await context.Response.WriteAsync($"{{\"message\": \"Endpoint {displayName} is available\"}}");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "默认处理器执行失败: {DisplayName}", displayName);
                    context.Response.StatusCode = 500;
                    await context.Response.WriteAsync("{\"error\": \"Internal server error\"}");
                }
            };
        }

        /// <summary>
        /// 创建MVC请求处理器
        /// </summary>
        private RequestDelegate CreateMvcHandler(ControllerActionDescriptor actionDescriptor)
        {
            return async context =>
            {
                try
                {
                    // 设置路由数据
                    var routeData = context.GetRouteData();
                    routeData.Values["controller"] = actionDescriptor.ControllerName;
                    routeData.Values["action"] = actionDescriptor.ActionName;

                    // 创建ActionContext
                    var actionContext = new ActionContext(context, routeData, actionDescriptor);

                    // 获取ActionInvoker并执行
                    var actionInvokerFactory = context.RequestServices.GetRequiredService<IActionInvokerFactory>();
                    var actionInvoker = actionInvokerFactory.CreateInvoker(actionContext);

                    if (actionInvoker != null)
                    {
                        await actionInvoker.InvokeAsync();
                    }
                    else
                    {
                        _logger.LogError("无法创建ActionInvoker: {DisplayName}", actionDescriptor.DisplayName);
                        context.Response.StatusCode = 500;
                        await context.Response.WriteAsync("无法创建ActionInvoker");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "执行MVC控制器时发生错误: {DisplayName}", actionDescriptor.DisplayName);
                    context.Response.StatusCode = 500;
                    await context.Response.WriteAsync($"服务器内部错误: {ex.Message}");
                }
            };
        }

        #endregion
    }
}
