﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net10.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <OutputPath>..\..\..\..\Host\SSIC.HostServer\Modules\SSIC.Modules.Auth.Controller\</OutputPath>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\SSIC.Entity\SSIC.Entity.csproj" />
    <ProjectReference Include="..\..\..\SSIC.Infrastructure\SSIC.Infrastructure.csproj" />
    <ProjectReference Include="..\SSIC.Modules.Auth.Services\SSIC.Modules.Auth.Services.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Controllers\Expands\" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="..\..\..\SSIC.Infrastructure\appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="..\..\..\SSIC.Infrastructure\Corssettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="..\..\..\SSIC.Infrastructure\DbInfosettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="..\..\..\SSIC.Infrastructure\modulesettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Aspire.Seq" Version="9.2.0" />
  </ItemGroup>

</Project>