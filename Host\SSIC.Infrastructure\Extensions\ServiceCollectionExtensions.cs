using Microsoft.Extensions.DependencyInjection;
using SSIC.Infrastructure.Middleware;
using SSIC.Infrastructure.Startups.OpenAPI;
using SSIC.Infrastructure.Startups.ModuleManager;
using System;

namespace SSIC.Infrastructure.Extensions
{
    /// <summary>
    /// 服务集合扩展方法
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// 添加SSIC基础设施服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddSSICInfrastructure(this IServiceCollection services)
        {
            // 注册模块管理服务
            services.AddModuleManagement();

            // 注册热加载服务
            services.AddHotReloadServices();

            return services;
        }

        /// <summary>
        /// 添加模块管理服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddModuleManagement(this IServiceCollection services)
        {
            // 注册模块发现服务
            services.AddSingleton<IModuleDiscoveryService, ModuleDiscoveryService>();

            // 注册端点管理服务
            services.AddSingleton<IEndpointManagementService, EndpointManagementService>();

            return services;
        }

        /// <summary>
        /// 添加热加载服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddHotReloadServices(this IServiceCollection services)
        {
            // 统一的OpenAPI刷新服务已在OpenApiConfigurationExtensions中注册
            // 这里不再重复注册以避免冲突

            return services;
        }

        /// <summary>
        /// 添加动态路由服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddDynamicRouting(this IServiceCollection services)
        {
            // 这里可以添加动态路由相关的服务注册
            // 例如：路由缓存、路由匹配器等

            return services;
        }
    }
}
