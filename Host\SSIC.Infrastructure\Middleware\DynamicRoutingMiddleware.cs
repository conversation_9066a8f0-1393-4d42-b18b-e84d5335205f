using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SSIC.Infrastructure.Startups.ModuleManager;
using System;
using System.Collections.Concurrent;
using System.Linq;
using System.Threading.Tasks;

namespace SSIC.Infrastructure.Middleware
{
    /// <summary>
    /// 动态路由中间件
    /// 支持模块热加载和路由动态更新
    /// </summary>
    public class DynamicRoutingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<DynamicRoutingMiddleware> _logger;
        private static readonly ConcurrentDictionary<string, DateTime> _moduleLoadTimes = new();
        private static bool _initialized = false;
        private static readonly object _initLock = new();

        public DynamicRoutingMiddleware(RequestDelegate next, ILogger<DynamicRoutingMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                // 确保只初始化一次
                if (!_initialized)
                {
                    await InitializeRoutingAsync(context);
                }

                // 检查是否需要刷新路由
                if (await ShouldRefreshRoutingAsync(context))
                {
                    await RefreshRoutingAsync(context);
                }

                // 尝试匹配动态路由
                if (await TryMatchDynamicRouteAsync(context))
                {
                    return; // 已处理请求
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "动态路由中间件处理请求时发生错误");
            }

            // 继续到下一个中间件
            await _next(context);
        }

        /// <summary>
        /// 初始化路由系统
        /// </summary>
        private async Task InitializeRoutingAsync(HttpContext context)
        {
            if (_initialized)
                return;

            lock (_initLock)
            {
                if (_initialized)
                    return;

                try
                {
                    _logger.LogInformation("初始化动态路由系统...");

                    var moduleDiscoveryService = context.RequestServices.GetService<IModuleDiscoveryService>();
                    var endpointManagementService = context.RequestServices.GetService<IEndpointManagementService>();

                    if (moduleDiscoveryService != null && endpointManagementService != null)
                    {
                        // 发现并注册所有模块
                        var modules = moduleDiscoveryService.DiscoverLoadedModules().ToList();
                        
                        foreach (var module in modules)
                        {
                            _moduleLoadTimes[module.AssemblyName] = module.LoadTime;
                        }

                        _logger.LogInformation("动态路由系统初始化完成，发现 {ModuleCount} 个模块", modules.Count);
                    }
                    else
                    {
                        _logger.LogWarning("无法获取模块发现服务或端点管理服务");
                    }

                    _initialized = true;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "初始化动态路由系统时发生错误");
                }
            }
        }

        /// <summary>
        /// 检查是否需要刷新路由
        /// </summary>
        private async Task<bool> ShouldRefreshRoutingAsync(HttpContext context)
        {
            try
            {
                var moduleDiscoveryService = context.RequestServices.GetService<IModuleDiscoveryService>();
                if (moduleDiscoveryService == null)
                    return false;

                var currentModules = moduleDiscoveryService.DiscoverLoadedModules().ToList();
                
                // 检查是否有新模块或模块更新
                foreach (var module in currentModules)
                {
                    if (!_moduleLoadTimes.ContainsKey(module.AssemblyName) ||
                        _moduleLoadTimes[module.AssemblyName] < module.LoadTime)
                    {
                        _logger.LogInformation("检测到模块变化: {ModuleName}", module.Name);
                        return true;
                    }
                }

                // 检查是否有模块被卸载
                var currentModuleNames = currentModules.Select(m => m.AssemblyName).ToHashSet();
                var removedModules = _moduleLoadTimes.Keys.Except(currentModuleNames).ToList();
                
                if (removedModules.Any())
                {
                    _logger.LogInformation("检测到模块卸载: {ModuleNames}", string.Join(", ", removedModules));
                    
                    // 清理已卸载模块的记录
                    foreach (var removedModule in removedModules)
                    {
                        _moduleLoadTimes.TryRemove(removedModule, out _);
                    }
                    
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查路由刷新需求时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 刷新路由系统
        /// </summary>
        private async Task RefreshRoutingAsync(HttpContext context)
        {
            try
            {
                _logger.LogInformation("开始刷新动态路由...");

                var moduleDiscoveryService = context.RequestServices.GetService<IModuleDiscoveryService>();
                var endpointManagementService = context.RequestServices.GetService<IEndpointManagementService>();

                if (moduleDiscoveryService == null || endpointManagementService == null)
                {
                    _logger.LogWarning("无法获取必要的服务，跳过路由刷新");
                    return;
                }

                // 重新发现模块
                var modules = moduleDiscoveryService.DiscoverLoadedModules().ToList();
                
                // 更新模块加载时间记录
                _moduleLoadTimes.Clear();
                foreach (var module in modules)
                {
                    _moduleLoadTimes[module.AssemblyName] = module.LoadTime;
                }

                // 刷新端点
                var endpoints = await endpointManagementService.RefreshEndpointsAsync(modules);
                
                _logger.LogInformation("动态路由刷新完成，共 {EndpointCount} 个端点", endpoints.Count());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新动态路由时发生错误");
            }
        }

        /// <summary>
        /// 尝试匹配动态路由
        /// </summary>
        private async Task<bool> TryMatchDynamicRouteAsync(HttpContext context)
        {
            try
            {
                // 获取路径信息
                var path = context.Request.Path.Value?.TrimStart('/') ?? string.Empty;
                
                // 检查是否是API路径
                if (!path.StartsWith("api/", StringComparison.OrdinalIgnoreCase))
                {
                    return false;
                }

                // 解析路径段
                var segments = path.Split('/', StringSplitOptions.RemoveEmptyEntries);
                if (segments.Length < 3) // api/module/controller 至少需要3段
                {
                    return false;
                }

                var moduleName = segments[1];
                var controllerName = segments[2];

                _logger.LogDebug("尝试匹配动态路由: {Path} -> 模块: {Module}, 控制器: {Controller}", 
                    path, moduleName, controllerName);

                // 检查模块是否存在
                var moduleAssemblyName = $"SSIC.Modules.{moduleName}";
                if (!_moduleLoadTimes.ContainsKey(moduleAssemblyName))
                {
                    _logger.LogDebug("模块 {ModuleName} 未找到", moduleName);
                    return false;
                }

                // 这里可以添加更复杂的路由匹配逻辑
                // 目前返回false，让其他中间件处理
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "匹配动态路由时发生错误");
                return false;
            }
        }
    }

    /// <summary>
    /// 动态路由中间件扩展方法
    /// </summary>
    public static class DynamicRoutingMiddlewareExtensions
    {
        /// <summary>
        /// 添加动态路由中间件
        /// </summary>
        public static IApplicationBuilder UseDynamicRouting(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<DynamicRoutingMiddleware>();
        }
    }
}
