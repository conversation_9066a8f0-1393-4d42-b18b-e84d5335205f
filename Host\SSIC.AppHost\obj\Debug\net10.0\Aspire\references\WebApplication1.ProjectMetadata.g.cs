// <auto-generated/>

namespace Projects;

[global::System.CodeDom.Compiler.GeneratedCode("Aspire.Hosting", null)]
[global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage(Justification = "Generated code.")]
[global::System.Diagnostics.DebuggerDisplay("Type = {GetType().Name,nq}, ProjectPath = {ProjectPath}")]
public class WebApplication1 : global::Aspire.Hosting.IProjectMetadata
{
    public string ProjectPath => """F:\SSIC\WebApplication1\WebApplication1.csproj""";
}
