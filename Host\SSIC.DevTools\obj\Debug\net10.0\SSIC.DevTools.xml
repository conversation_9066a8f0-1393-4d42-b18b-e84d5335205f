<?xml version="1.0"?>
<doc>
    <assembly>
        <name>SSIC.DevTools</name>
    </assembly>
    <members>
        <member name="T:SSIC.DevTools.BuilderTools.CreateModuleProject">
            <summary>
            模块化项目代码生成器
            生成符合SSIC.Modules.{ModuleName}结构的代码
            </summary>
        </member>
        <member name="M:SSIC.DevTools.BuilderTools.CreateModuleProject.CreateModuleFiles(SSIC.DevTools.Controllers.EntityInfo[],System.String[],System.String[])">
            <summary>
            生成模块化项目文件（支持实体信息）
            </summary>
            <param name="entities">要生成的实体信息数组，包含实体名称和中文描述</param>
            <param name="entityNames">要生成的实体名称数组，为空则生成所有实体对应的服务和控制器（向后兼容）</param>
            <param name="controllerNames">要生成的控制器名称数组，为空则生成所有实体对应的控制器（已弃用）</param>
            <returns></returns>
        </member>
        <member name="M:SSIC.DevTools.BuilderTools.CreateModuleProject.CreateModuleFiles(System.String[],System.String[])">
            <summary>
            生成模块化项目文件（原方法，向后兼容）
            </summary>
            <param name="entityNames">要生成的实体名称数组，为空则生成所有实体对应的服务和控制器</param>
            <param name="controllerNames">要生成的控制器名称数组，为空则生成所有实体对应的控制器（已弃用，使用entityNames参数）</param>
            <returns></returns>
        </member>
        <member name="M:SSIC.DevTools.BuilderTools.CreateModuleProject.CreateModuleFiles(System.String,SSIC.DevTools.Controllers.EntityInfo[],System.String[],System.String[])">
            <summary>
            生成指定模块的项目文件（支持实体信息）
            </summary>
            <param name="moduleName">模块名称</param>
            <param name="entities">要生成的实体信息数组，包含实体名称和中文描述</param>
            <param name="entityNames">要生成的实体名称数组，为空则生成所有实体对应的服务和控制器（向后兼容）</param>
            <param name="controllerNames">要生成的控制器名称数组（已弃用）</param>
            <returns></returns>
        </member>
        <member name="M:SSIC.DevTools.BuilderTools.CreateModuleProject.CreateModuleFiles(System.String,System.String[],System.String[])">
            <summary>
            生成指定模块的项目文件（原方法，向后兼容）
            </summary>
            <param name="moduleName">模块名称</param>
            <param name="entityNames">要生成的实体名称数组，为空则生成所有实体对应的服务和控制器</param>
            <param name="controllerNames">要生成的控制器名称数组（已弃用，使用entityNames参数）</param>
            <returns></returns>
        </member>
        <member name="M:SSIC.DevTools.BuilderTools.CreateModuleProject.CreateModuleStructure(System.String,System.String,System.String,System.IO.DirectoryInfo,SSIC.DevTools.Controllers.EntityInfo[],System.String[])">
            <summary>
            创建模块结构
            </summary>
            <param name="modulesPath">模块根路径</param>
            <param name="moduleName">模块名称</param>
            <param name="templatePath">模板路径</param>
            <param name="entityFolder">实体文件夹</param>
            <param name="entities">要生成的实体信息数组，包含实体名称和中文描述</param>
            <param name="entityNames">要生成的实体名称数组，为空则生成所有实体对应的服务和控制器</param>
        </member>
        <member name="M:SSIC.DevTools.BuilderTools.CreateModuleProject.CreateServiceInterfaceFiles(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            创建服务接口文件配置（支持中文描述）
            </summary>
        </member>
        <member name="M:SSIC.DevTools.BuilderTools.CreateModuleProject.CreateServiceInterfaceFiles(System.String,System.String,System.String,System.String)">
            <summary>
            创建服务接口文件配置（原方法，向后兼容）
            </summary>
        </member>
        <member name="M:SSIC.DevTools.BuilderTools.CreateModuleProject.CreateServiceImplementationFiles(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            创建服务实现文件配置（支持中文描述）
            </summary>
        </member>
        <member name="M:SSIC.DevTools.BuilderTools.CreateModuleProject.CreateServiceImplementationFiles(System.String,System.String,System.String,System.String)">
            <summary>
            创建服务实现文件配置（原方法，向后兼容）
            </summary>
        </member>
        <member name="M:SSIC.DevTools.BuilderTools.CreateModuleProject.CreateControllerFiles(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            创建控制器文件配置（支持中文描述）
            </summary>
        </member>
        <member name="M:SSIC.DevTools.BuilderTools.CreateModuleProject.CreateControllerFiles(System.String,System.String,System.String,System.String)">
            <summary>
            创建控制器文件配置（原方法，向后兼容）
            </summary>
        </member>
        <member name="M:SSIC.DevTools.BuilderTools.CreateModuleProject.CreateProjectFiles(System.String,System.String)">
            <summary>
            创建项目文件(.csproj, Program.cs等)
            </summary>
            <param name="modulesPath">模块根路径</param>
            <param name="moduleName">模块名称</param>
        </member>
        <member name="M:SSIC.DevTools.BuilderTools.CreateModuleProject.CreateServicesProjectFile(System.String,System.String)">
            <summary>
            创建Services项目文件
            </summary>
        </member>
        <member name="M:SSIC.DevTools.BuilderTools.CreateModuleProject.CreateControllerProjectFile(System.String,System.String)">
            <summary>
            创建Controller项目文件
            </summary>
        </member>
        <member name="M:SSIC.DevTools.BuilderTools.CreateModuleProject.CreateSolutionFile(System.String,System.String)">
            <summary>
            创建解决方案文件
            </summary>
        </member>
        <member name="M:SSIC.DevTools.BuilderTools.CreateModuleProject.CreateLaunchSettings(System.String,System.String)">
            <summary>
            创建启动配置文件
            </summary>
            <param name="controllersPath">控制器项目路径</param>
            <param name="moduleName">模块名称</param>
        </member>
        <member name="M:SSIC.DevTools.BuilderTools.CreateModuleProject.LoadModuleFile(System.String,System.String,System.String,System.String,System.String,System.String,System.Boolean)">
            <summary>
            读取Html模板文件并生成目标文件
            </summary>
            <param name="path">文件地址</param>
            <param name="templatePath">模板路径</param>
            <param name="moduleName">模块名称</param>
            <param name="entityName">实体名称</param>
            <param name="entityDescription">实体中文描述</param>
            <param name="filename">文件名</param>
            <param name="isReplace">是否替换</param>
        </member>
        <member name="T:SSIC.DevTools.BuilderTools.ModulePathDic">
            <summary>
            模块文件路径配置
            </summary>
        </member>
        <member name="M:SSIC.DevTools.BuilderTools.CreateProject.LoadPathFile(System.String,System.String,System.String,System.String,System.String,System.Boolean)">
             <summary>
            读取Html文件生成文件
             </summary>
             <param name="path">文件地址</param>
             <param name="Templatepath">模板路径</param>
             <param name="BusinessName">业务模块</param>
             <param name="Filename">文件名</param>
             <param name="IsRepalce">是否替换</param>
        </member>
        <member name="T:SSIC.DevTools.CodeGenerator">
            <summary>
            代码生成器控制台应用
            </summary>
        </member>
        <member name="M:SSIC.DevTools.CodeGenerator.RunGenerator">
            <summary>
            运行代码生成器
            </summary>
        </member>
        <member name="M:SSIC.DevTools.CodeGenerator.GenerateModuleProjects">
            <summary>
            生成模块化项目结构
            </summary>
        </member>
        <member name="M:SSIC.DevTools.CodeGenerator.GenerateTraditionalProjects">
            <summary>
            生成传统项目结构
            </summary>
        </member>
        <member name="M:SSIC.DevTools.CodeGenerator.ShowHelp">
            <summary>
            显示帮助信息
            </summary>
        </member>
        <member name="T:SSIC.DevTools.Controllers.CodeGeneratorController">
            <summary>
            代码生成器API控制器
            支持通过实体名称数组同时生成对应的服务和控制器代码
            实体名称数组格式：["User", "Role", "Permission"]
            每个实体名称将生成：I{EntityName}Service.cs、{EntityName}Service.cs、{EntityName}Controller.cs
            </summary>
        </member>
        <member name="M:SSIC.DevTools.Controllers.CodeGeneratorController.GenerateModules(SSIC.DevTools.Controllers.GenerateModulesRequest)">
            <summary>
            生成模块化项目代码
            </summary>
            <param name="request">生成请求参数</param>
            <returns></returns>
        </member>
        <member name="M:SSIC.DevTools.Controllers.CodeGeneratorController.GenerateTraditional">
            <summary>
            生成传统项目代码
            </summary>
            <returns></returns>
        </member>
        <member name="M:SSIC.DevTools.Controllers.CodeGeneratorController.GetEntities">
            <summary>
            获取实体信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:SSIC.DevTools.Controllers.CodeGeneratorController.GetTemplates">
            <summary>
            获取模板信息
            </summary>
            <returns></returns>
        </member>
        <member name="T:SSIC.DevTools.Controllers.GenerateModulesRequest">
            <summary>
            生成模块请求参数
            </summary>
        </member>
        <member name="P:SSIC.DevTools.Controllers.GenerateModulesRequest.ModuleName">
            <summary>
            模块名称，为空则生成所有模块
            </summary>
        </member>
        <member name="P:SSIC.DevTools.Controllers.GenerateModulesRequest.Entities">
            <summary>
            要生成的实体信息数组，包含实体名称和中文描述
            </summary>
        </member>
        <member name="P:SSIC.DevTools.Controllers.GenerateModulesRequest.EntityNames">
            <summary>
            要生成的实体名称数组，为空则生成所有实体对应的服务和控制器（已弃用，使用Entities参数）
            </summary>
        </member>
        <member name="P:SSIC.DevTools.Controllers.GenerateModulesRequest.ServiceNames">
            <summary>
            要生成的服务名称数组，为空则生成所有实体对应的服务（已弃用，使用Entities参数）
            </summary>
        </member>
        <member name="P:SSIC.DevTools.Controllers.GenerateModulesRequest.ControllerNames">
            <summary>
            要生成的控制器名称数组，为空则生成所有实体对应的控制器（已弃用，使用Entities参数）
            </summary>
        </member>
        <member name="T:SSIC.DevTools.Controllers.EntityInfo">
            <summary>
            实体信息
            </summary>
        </member>
        <member name="P:SSIC.DevTools.Controllers.EntityInfo.Name">
            <summary>
            实体名称（英文）
            </summary>
        </member>
        <member name="P:SSIC.DevTools.Controllers.EntityInfo.Description">
            <summary>
            实体中文描述
            </summary>
        </member>
        <member name="M:SSIC.DevTools.Controllers.WeatherForecastController.CreateFiles(System.String)">
            <summary>
            生成后台模板
            </summary>
            <returns></returns>
        </member>
    </members>
</doc>
